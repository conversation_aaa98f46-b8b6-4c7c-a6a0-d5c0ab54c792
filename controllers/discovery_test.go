package controllers

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http/httptest"
	"testing"
	"time"

	"icode.baidu.com/baidu/gdp/ghttp"
	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/models"
)

// TestSubmitAntibody TestSubmitAntibody 是用于测试SubmitAntibody函数的单元测试，该函数用于提交蛋白质报告
// 参数t *testing.T：表示单元测试的上下文，必须传入
// 返回值没有返回值
func TestSubmitAntibody(t *testing.T) {
	ctx := context.Background()
	ctx = context.WithValue(ctx, "user_info", models.UserInfo{UserID: 1, Type: 1})

	input1, _ := json.Marshal(map[string]interface{}{
		"name":     "test003001",
		"file_url": "xxx",
	})
	input2, _ := json.<PERSON>(map[string]interface{}{
		"name":     "test003001",
		"file_url": "bos:/bml-test-test/helix_upload/fb5d8890-d7b0-41e0-ada1-da73270dba9b.txt",
	})
	input4, _ := json.Marshal(map[string]interface{}{
		"heavy_serial":   "xxx",
		"light_serial":   "xxx",
		"antigen_serial": "xxxx",
	})

	caseList := []struct {
		input  io.Reader
		output int
	}{
		{nil, helpers.SuccessCode},
		{bytes.NewReader(input1), helpers.ParamErrorCode},
		{bytes.NewReader(input2), helpers.SuccessCode},
		{bytes.NewReader(input4), helpers.SuccessCode},
	}

	for _, v := range caseList {
		raw := httptest.NewRequest("POST", "/", v.input)
		req := ghttp.NewRequest(time.Now(), raw)

		resp := SubmitAntibody(ctx, req).(*ghttp.JSONResponse)
		data := resp.Data.(helpers.JsonResp)
		if data.Code != v.output {
			fmt.Println(data)
			t.Errorf("code=%d, want=%d", data.Code, v.output)
		}
	}
}

// TestSubmitCovid 测试SubmitCovid函数，该函数用于提交卫生健康码信息
// 参数：t *testing.T - 单元测试的上下文，必须传入
// 返回值：无
func TestSubmitCovid(t *testing.T) {
	ctx := context.Background()
	ctx = context.WithValue(ctx, "user_info", models.UserInfo{UserID: 1, Type: 1})

	input1, _ := json.Marshal(map[string]interface{}{
		"name":     "test003001",
		"file_url": "xxx",
	})
	input2, _ := json.Marshal(map[string]interface{}{
		"name":     "test003001",
		"file_url": "bos:/bml-test-test/helix_upload/fb5d8890-d7b0-41e0-ada1-da73270dba9b.txt",
	})
	input4, _ := json.Marshal(map[string]interface{}{
		"heavy_serial": "xxx",
		"light_serial": "xxx",
	})

	caseList := []struct {
		input  io.Reader
		output int
	}{
		{nil, helpers.SuccessCode},
		{bytes.NewReader(input1), helpers.ParamErrorCode},
		{bytes.NewReader(input2), helpers.SuccessCode},
		{bytes.NewReader(input4), helpers.SuccessCode},
	}

	for _, v := range caseList {
		raw := httptest.NewRequest("POST", "/", v.input)
		req := ghttp.NewRequest(time.Now(), raw)

		resp := SubmitCovid(ctx, req).(*ghttp.JSONResponse)
		data := resp.Data.(helpers.JsonResp)
		if data.Code != v.output {
			fmt.Println(data)
			t.Errorf("code=%d, want=%d", data.Code, v.output)
		}
	}
}

// TestProteinMutation 测试函数，用于测试ProteinMutation的功能。
// 参数t：*testing.T类型，表示当前正在执行的单元测试。
// 返回值：无返回值
func TestProteinMutation(t *testing.T) {
	ctx := context.Background()
	ctx = context.WithValue(ctx, "user_info", models.UserInfo{UserID: 1, Type: 1})

	input1, _ := json.Marshal(map[string]interface{}{
		"protein1": "xxx",
	})
	input2, _ := json.Marshal(map[string]interface{}{
		"protein1": "xxx",
		"point1":   "xsss",
	})
	input3, _ := json.Marshal(map[string]interface{}{
		"protein1": "xxx",
		"protein2": "xxx",
		"point1":   "xsss",
	})
	input4, _ := json.Marshal(map[string]interface{}{
		"protein1": "xxx",
		"protein2": "xxx",
		"point1":   []string{"sdsd", "sdaf"},
		"point2":   []string{"sdsd", "sdaf"},
	})
	input5, _ := json.Marshal(map[string]interface{}{
		"file_url": "bos:/bml-test-test/helix_upload/866fae6a-b842-4b0b-a6b8-7bbb366afdd8.txt",
	})
	caseList := []struct {
		input  io.Reader
		output int
	}{
		{nil, helpers.ParamErrorCode},
		{bytes.NewReader(input1), helpers.ParamErrorCode},
		{bytes.NewReader(input2), helpers.ParamErrorCode},
		{bytes.NewReader(input3), helpers.ParamErrorCode},
		{bytes.NewReader(input4), helpers.SuccessCode},
		{bytes.NewReader(input5), helpers.SuccessCode},
	}

	for _, v := range caseList {
		raw := httptest.NewRequest("POST", "/", v.input)
		req := ghttp.NewRequest(time.Now(), raw)

		resp := SubmitProteinMutation(ctx, req).(*ghttp.JSONResponse)
		data := resp.Data.(helpers.JsonResp)
		if data.Code != v.output {
			fmt.Println(data)
			t.Errorf("code=%d, want=%d", data.Code, v.output)
		}
	}
}

// TestSubmitCompound 测试SubmitCompound函数，该函数用于提交复合任务
// 参数t *testing.T：表示当前的测试对象
// 返回值类型为空
func TestSubmitCompound(t *testing.T) {
	ctx := context.Background()
	ctx = context.WithValue(ctx, "user_info", models.UserInfo{UserID: 1, Type: 1})

	input1, _ := json.Marshal(map[string]interface{}{
		"serial": "serialxxx",
		"name":   "test001",
	})
	input3, _ := json.Marshal(map[string]interface{}{
		"file_url": "bos:/bml-test-test/helix_upload/fb5d8890-d7b0-41e0-ada1-da73270dba9b.txt",
		"name":     "test002",
	})
	input4, _ := json.Marshal(map[string]interface{}{
		"file_url": "xxx",
		"name":     "test002001",
	})

	caseList := []struct {
		input  io.Reader
		output int
	}{
		{nil, helpers.ParamErrorCode},
		{bytes.NewReader(input1), helpers.SuccessCode},
		{bytes.NewReader(input3), helpers.SuccessCode},
		{bytes.NewReader(input4), helpers.ParamErrorCode},
	}

	for _, v := range caseList {
		raw := httptest.NewRequest("POST", "/", v.input)
		req := ghttp.NewRequest(time.Now(), raw)

		resp := SubmitAdmet(ctx, req).(*ghttp.JSONResponse)
		data := resp.Data.(helpers.JsonResp)
		if data.Code != v.output {
			fmt.Println(data)
			t.Errorf("code=%d, want=%d", data.Code, v.output)
		}
	}
}
