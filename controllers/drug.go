package controllers

import (
	"context"
	"encoding/json"
	"errors"
	"strings"

	"icode.baidu.com/baidu/gdp/ghttp"

	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/models"
	"icode.baidu.com/helix_web/services"
)

const (
	DrugLenLimit = 128
	DrugGetLimit = 20
)

// 搜索药物
func SearchDrug(ctx context.Context, req ghttp.Request) ghttp.Response {
	params := helpers.GetBodyParams(ctx, req)
	keyword := helpers.GetStringParam(params, "keyword")
	if len(keyword) == 0 || len(keyword) > KeywordLenLimit {
		return helpers.SuccReturn(ctx, nil)
	}

	drugM := models.DrugLib{}
	drugList, err := drugM.GetList(ctx, keyword, models.DrugSearchLimit)
	if err != nil {
		return helpers.FailReturn(ctx, helpers.LogicErrorCode, "data error, repeat")
	}

	// 数据组装
	var items []map[string]any
	for _, resData := range drugList {
		items = append(items, resData.SwapData())
	}

	result := map[string]any{
		"items": items,
	}
	return helpers.SuccReturn(ctx, result)
}

// 批量获取药物信息
func BatchGetDrug(ctx context.Context, req ghttp.Request) ghttp.Response {
	params := helpers.GetBodyParams(ctx, req)
	cidList := helpers.GetStringSliceParam(params, "cid_list")
	if len(cidList) == 0 || len(cidList) > DrugGetLimit {
		return helpers.SuccReturn(ctx, nil)
	}

	drugM := models.DrugLib{}
	drugList, err := drugM.BatchGetByCid(ctx, cidList)
	if err != nil {
		return helpers.FailReturn(ctx, helpers.LogicErrorCode, "data error, repeat")
	}

	// 数据组装
	items := make(map[string]any)
	for _, resData := range drugList {
		items[strings.ToUpper(resData.Cid)] = resData.SwapData()
	}

	result := map[string]any{
		"items": items,
	}
	return helpers.SuccReturn(ctx, result)
}

// 提交预测（双药联用）
func SubmitDoubleDrug(ctx context.Context, req ghttp.Request) ghttp.Response {
	params := helpers.GetBodyParams(ctx, req)
	drugA := helpers.GetStringParam(params, "drug_a")
	drugB := helpers.GetStringParam(params, "drug_b")
	name := helpers.GetStringParam(params, "name")
	fileUrl := helpers.GetStringParam(params, "file_url")
	cellLine := helpers.GetStringParam(params, "cell_line")
	tissues := helpers.GetStringParam(params, "tissues")

	// 药物校验
	if len(drugA) > DrugLenLimit || len(drugB) > DrugLenLimit {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "drug length limit")
	}

	// name校验
	name = getTaskName(name)
	if len([]rune(name)) > MaxTaskNameLimit {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "name length limit")
	}

	// config check
	needConfig, err := getDoubleDrugConf(ctx, drugA, drugB, fileUrl, cellLine, tissues)
	if err != nil {
		return helpers.NewException(ctx, "PARAM_VALUE_ERR", err.Error())
	}

	// 数据组装
	configByte, _ := json.Marshal(needConfig)
	taskN := models.Task{
		Name:     name,
		UserId:   uint64(getUserId(ctx)),
		Type:     uint64(models.TaskTypeDoubleDrug),
		FuncType: uint64(models.FuncTypeForecast),
		Config:   string(configByte),
	}
	taskNew, err := taskN.Add(ctx, taskN)
	if err != nil {
		return helpers.NewException(ctx, "DB_ERR")
	}

	// 提交任务给调度
	services.PreSubmitTask(ctx, taskNew)
	result := map[string]any{
		"task_id": taskNew.ID,
	}
	return helpers.SuccReturn(ctx, result)
}

// 精准用药
func SubmitExactDrug(ctx context.Context, req ghttp.Request) ghttp.Response {
	params := helpers.GetBodyParams(ctx, req)
	name := helpers.GetStringParam(params, "name")
	drugList := helpers.GetStringSliceParam(params, "drug_list")
	molUrl := helpers.GetStringParam(params, "mol_url")
	serial := helpers.GetStringParam(params, "serial")
	sample := helpers.GetStringParam(params, "sample")
	sampleUrl := helpers.GetStringParam(params, "sample_url")

	// 参数检验
	name = getTaskName(name)
	if len([]rune(name)) > MaxTaskNameLimit {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "name length limit")
	}
	if len(drugList) > MaxDrugLimit {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "drug_list length limit")
	}
	if len(serial) > MaxSerialLimit {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "serial length limit")
	}

	// config check
	needConfig, err := getExactDrugConf(molUrl, serial, sample, sampleUrl, drugList)
	if err != nil {
		return helpers.NewException(ctx, "PARAM_VALUE_ERR", err.Error())
	}

	// 数据组装
	configByte, _ := json.Marshal(needConfig)
	taskN := models.Task{
		Name:      name,
		UserId:    uint64(getUserId(ctx)),
		Type:      uint64(models.TaskTypeExactDrug),
		FuncType:  uint64(models.FuncTypeForecast),
		Config:    string(configByte),
		IsExample: uint64(checkSerialExample(ctx, int64(models.TaskTypeExactDrug), serial)),
	}
	taskNew, err := taskN.Add(ctx, taskN)
	if err != nil {
		return helpers.NewException(ctx, "DB_ERR")
	}

	// 提交任务给调度
	services.PreSubmitTask(ctx, taskNew)
	result := map[string]any{
		"task_id": taskNew.ID,
	}
	return helpers.SuccReturn(ctx, result)
}

// 格式化Cid
func formatDrug(ctx context.Context, cid string) string {
	if len(cid) == 0 {
		return ""
	}

	drugM := models.DrugLib{}
	drugInfo, err := drugM.GetByCid(ctx, cid)
	if err == nil && drugInfo.ID > 0 {
		return drugInfo.Cid
	}
	return ""
}

func getDoubleDrugConf(ctx context.Context, drugA, drugB, fileUrl, cellLine, tissues string) (models.TaskDoubleDrugConf, error) {
	var needConfig models.TaskDoubleDrugConf

	// 药物format
	drugA = formatDrug(ctx, drugA)
	drugB = formatDrug(ctx, drugB)
	if (len(drugA) == 0 && len(drugB) == 0) || drugA == drugB {
		return needConfig, errors.New("drug param error")
	}
	needConfig.DrugA = drugA
	needConfig.DrugB = drugB

	if len(fileUrl) > 0 {
		err := checkFileContent(getFileContent(fileUrl))
		if err != nil {
			return needConfig, err
		}

		needConfig.FileUrl = fileUrl
	} else {
		if len(cellLine) > DrugLenLimit {
			return needConfig, errors.New("cell_line length limit")
		}
		if len(tissues) > DrugLenLimit {
			return needConfig, errors.New("tissues length limit")
		}

		needConfig.CellLine = cellLine
		needConfig.Tissues = tissues
	}

	return needConfig, nil
}

func getExactDrugConf(molUrl, serial, sample, sampleUrl string, drugList []string) (models.TaskExactDrugConf, error) {
	var needConfig models.TaskExactDrugConf
	if len(molUrl) > 0 {
		err := checkFileContent(getFileContent(molUrl))
		if err != nil {
			return needConfig, err
		}

		needConfig.MolUrl = molUrl
	}
	if len(drugList) > 0 {
		drugByte, _ := json.Marshal(drugList)
		needConfig.DrugList = string(drugByte)
	}
	needConfig.Serial = serial

	if len(sample) > 0 {
		needConfig.Sample = sample
	} else {
		err := checkFileContent(getFileContent(sampleUrl))
		if err != nil {
			return needConfig, err
		}

		needConfig.SampleUrl = sampleUrl
	}

	return needConfig, nil
}
