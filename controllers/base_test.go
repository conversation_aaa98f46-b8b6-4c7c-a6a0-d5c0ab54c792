package controllers

import (
	"context"
	"errors"
	"path/filepath"
	"reflect"
	"testing"
	"time"

	"icode.baidu.com/baidu/gdp/env"
	"icode.baidu.com/baidu/gdp/gorm_adapter"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/baidu/gdp/mysql"
	"icode.baidu.com/baidu/gdp/net/ral"
	"icode.baidu.com/baidu/gdp/net/servicer"
	"icode.baidu.com/baidu/gdp/redis"
	"icode.baidu.com/helix_web/library/resource"
	"icode.baidu.com/helix_web/models"
)

func TestFormatPaging(t *testing.T) {
	tests := []struct {
		limit   int64
		page    int64
		output1 int64
		output2 int64
	}{
		{0, 0, 3, 1},
		{10, 1, 10, 1},
	}

	for _, test := range tests {
		if got1, got2 := formatPaging(test.limit, test.page); got1 != test.output1 || got2 != test.output2 {
			t.Errorf("want(%v, %v), got(%v, %v)", test.output1, test.output2, got1, got2)
		}
	}
}

func TestCheckPretreatType(t *testing.T) {
	tests := []struct {
		input1 int
		output bool
	}{
		{models.TaskTypeVirtualFilter, true},
		{models.TaskTypeMolFormation, true},
		{0, false},
	}

	for _, test := range tests {
		if got := checkPretreatType(test.input1); got != test.output {
			t.Errorf("want(%v), got(%v)", test.output, got)
		}
	}
}

func TestCheckTaskChargeType(t *testing.T) {
	tests := []struct {
		input1 int
		output bool
	}{
		{models.TaskTypeLinearDesign, true},
		{models.TaskTypeProtein, true},
		{0, false},
	}

	for _, test := range tests {
		if got := models.CheckTaskChargeType(test.input1); got != test.output {
			t.Errorf("want(%v), got(%v)", test.output, got)
		}
	}
}

func TestCheckTaskCommonParam(t *testing.T) {
	tests := []struct {
		input1 string
		input2 string
		input3 string
		output error
	}{
		{"", "", "", errors.New("file_url or serial param error")},
		{"sdf", "sd", "sss", nil},
	}

	for _, test := range tests {
		if got := checkTaskCommonParam(test.input1, test.input2, test.input3); !reflect.DeepEqual(got, test.output) {
			t.Errorf("want(%v), got(%v)", test.output, got)
		}
	}
}

func TestGetCommonTaskConf(t *testing.T) {
	tests := []struct {
		input1 string
		input2 string
		output string
	}{
		{"", "", ""},
		{"sss", "cc", "sss"},
		{"", "bos:/bml-test-test/helix_upload/fb5d8890-d7b0-41e0-ada1-da73270dba9b.txt", "sss"},
	}

	for _, test := range tests {
		if _, got, _ := getCommonTaskConf(test.input1, test.input2); len(got) < len(test.output) {
			t.Errorf("want(%v), got(%v)", test.output, got)
		}
	}
}

func TestGetTaskName(t *testing.T) {
	tests := []struct {
		input  string
		output int
	}{
		{"", DefaultNameLength},
		{"xxxxxx", 6},
	}

	for _, test := range tests {
		if got := getTaskName(test.input); len(got) != test.output {
			t.Errorf("want(%v), got(%v)", test.output, got)
		}
	}
}

func TestGetFileContent(t *testing.T) {
	tests := []struct {
		input  string
		output string
	}{
		{"x/xx", ""},
		{"bos:/xxs/xxs", ""},
	}

	for _, test := range tests {
		if got := getFileContent(test.input); got != test.output {
			t.Errorf("want(%v), got(%v)", test.output, got)
		}
	}
}

func TestCheckFileContent(t *testing.T) {
	tests := []struct {
		input  string
		output error
	}{
		{"", errors.New("upload file error")},
		{"bos:/xxs/xxs", nil},
	}

	for _, test := range tests {
		if got := checkFileContent(test.input); !reflect.DeepEqual(got, test.output) {
			t.Errorf("want(%v), got(%v)", test.output, got)
		}
	}
}

// TestCheckUserPermission 测试函数，用于检查用户是否有权限进行操作
// 参数：t *testing.T - 类型为*testing.T的指针，表示当前测试用例的上下文信息
// 返回值：无返回值
func TestCheckUserPermission(t *testing.T) {
	ctx := context.Background()
	ctx1 := context.WithValue(ctx, "user_info", models.UserInfo{UserID: 1, Type: 1})

	tests := []struct {
		input1 context.Context
		input2 int
		output error
	}{
		{ctx1, models.TaskTypeLinearFold, nil},
		{ctx1, models.TaskTypeLinearDesign, nil},
	}

	for _, test := range tests {
		time.Sleep(4 * time.Second)
		if _, got := CheckUserPermission(test.input1, int64(test.input2), true); !reflect.DeepEqual(got, test.output) {
			t.Errorf("want(%v), got(%v)", test.output, got)
		}
	}
}

// 包初始化
func init() {
	ctx := context.Background()
	env.DefaultRunMode = "debug"

	// bootstrap
	mustInit(ctx)
}

// bootstrap 初始化
// MustInit 组件初始化，若失败，会panic
func mustInit(ctx context.Context) {
	initLoggers(ctx)

	initRal(ctx)

	loadServicer(ctx)

	initMySQL(ctx)

	initRedis(ctx)
}

// initLoggers 初始化logger
func initLoggers(ctx context.Context) {
	{
		webLogger, err := logit.NewLogger(ctx, logit.OptConfigFile("logit/service.toml"))
		if err != nil {
			panic(err.Error())
		}
		resource.LoggerService = webLogger
	}
}

// initRal 初始化服务配置 以及ral组件（日志...）
func initRal(ctx context.Context) {
	_ = ral.InitDefault(ctx)
}

// loadServicer 加载服务配置
func loadServicer(ctx context.Context) {
	pattern := filepath.Join(env.ConfDir(), "servicer", "*.toml")
	servicer.MustLoad(ctx, servicer.LoadOptFilesGlob(pattern, false))
}

// 初始化mysql
func initMySQL(_ context.Context) {
	// client 初始化为单例，放到 resource 里去
	resource.MySQLClient = mustInitOneMySQL("mysql")
	gorm, err := gorm_adapter.NewGorm(resource.MySQLClient, gorm_adapter.OptLogger(resource.LoggerService))
	if err != nil {
		panic(err.Error())
	}

	resource.Gorm = gorm
}
func mustInitOneMySQL(name interface{}) mysql.Client {
	opts := []mysql.ClientOption{
		mysql.OptObserver(mysql.NewMetricsObserverFunc(nil)),
	}
	client, err := mysql.NewClient(name, opts...)
	// 理论上，只有配置错误，才有可能返回err
	// 如配置不存在，配置内容错误
	if err != nil {
		panic(err.Error())
	}
	return client
}

// initRedis 初始化redis
func initRedis(_ context.Context) {
	// client 初始化为单例，放到 resource 里去
	resource.RedisClient = mustInitOneRedis("redis")
}
func mustInitOneRedis(name string) redis.Client {
	opts := []redis.ClientOption{
		redis.OptHooker(redis.NewMetricsHook(nil)),
		redis.OptHooker(redis.NewLogHook()),
	}

	client, err := redis.NewClient(name, opts...)
	// 理论上，只有配置错误，才有可能返回err
	// 如配置不存在，配置内容错误
	if err != nil {
		panic(err.Error())
	}
	return client
}
