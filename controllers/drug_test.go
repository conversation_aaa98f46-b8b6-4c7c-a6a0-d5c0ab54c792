package controllers

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http/httptest"
	"testing"
	"time"

	"icode.baidu.com/baidu/gdp/ghttp"
	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/models"
)

// TestSearchDrug 测试函数，用于搜索药品信息
// 参数：t *testing.T - 指向testing包中的*testing.T类型的指针，表示当前测试用例
func TestSearchDrug(t *testing.T) {
	ctx := context.Background()
	ctx = context.WithValue(ctx, "user_info", models.UserInfo{UserID: 1, Type: 1})

	input1 := map[string]interface{}{
		"keyword": "wewe",
	}
	input1Byte, _ := json.Marshal(input1)

	caseList := []struct {
		input  io.Reader
		output int
	}{
		{nil, helpers.SuccessCode},
		{bytes.NewReader(input1Byte), helpers.SuccessCode},
	}

	for _, v := range caseList {
		raw := httptest.NewRequest("POST", "/", v.input)
		req := ghttp.NewRequest(time.Now(), raw)

		resp := SearchDrug(ctx, req).(*ghttp.JSONResponse)
		data := resp.Data.(helpers.JsonResp)
		if data.Code != v.output {
			fmt.Println(data)
			t.Errorf("code=%d, want=%d", data.Code, v.output)
		}
	}
}

// TestBatchGetDrug 测试函数，用于批量获取药品信息
// 参数：t *testing.T - 类型为*testing.T的指针，表示当前测试用例
// 返回值：无
func TestBatchGetDrug(t *testing.T) {
	ctx := context.Background()
	ctx = context.WithValue(ctx, "user_info", models.UserInfo{UserID: 1, Type: 1})

	input1 := map[string]interface{}{
		"cid_list": []string{"wewe"},
	}
	input1Byte, _ := json.Marshal(input1)

	caseList := []struct {
		input  io.Reader
		output int
	}{
		{nil, helpers.SuccessCode},
		{bytes.NewReader(input1Byte), helpers.SuccessCode},
	}

	for _, v := range caseList {
		raw := httptest.NewRequest("POST", "/", v.input)
		req := ghttp.NewRequest(time.Now(), raw)

		resp := BatchGetDrug(ctx, req).(*ghttp.JSONResponse)
		data := resp.Data.(helpers.JsonResp)
		if data.Code != v.output {
			fmt.Println(data)
			t.Errorf("code=%d, want=%d", data.Code, v.output)
		}
	}
}

// TestSubmitDoubleDrug 测试提交双药物功能
//
// 参数：
//
//	t (pointer to testing.T) - 单元测试的指针，用于记录错误
//
// 返回值：
//
//	无
func TestSubmitDoubleDrug(t *testing.T) {
	ctx := context.Background()
	ctx = context.WithValue(ctx, "user_info", models.UserInfo{UserID: 1, Type: 1})

	input1 := map[string]interface{}{
		"drug_a":   "CIDs00165579",
		"file_url": "bos:/bml-test-test/helix_upload/866fae6a-b842-4b0b-a6b8-7bbb366afdd8.txt",
	}
	input1Byte, _ := json.Marshal(input1)
	input2 := map[string]interface{}{
		"drug_a":    "CIDs09690109",
		"drug_b":    "CIDs00033036",
		"cell_line": "sdsf",
		"tissues":   "ddsd",
	}
	input2Byte, _ := json.Marshal(input2)
	input3 := map[string]interface{}{
		"file_url": "bos:/bml-test-test/helix_upload/866fae6a-b842-4b0b-a6b8-7bbb366afdd8.txt",
	}
	input3Byte, _ := json.Marshal(input3)
	input4 := map[string]interface{}{
		"cell_line": "sdsf",
		"tissues":   "ddsd",
	}
	input4Byte, _ := json.Marshal(input4)

	caseList := []struct {
		input  io.Reader
		output int
	}{
		{nil, helpers.ParamErrorCode},
		{bytes.NewReader(input1Byte), helpers.SuccessCode},
		{bytes.NewReader(input2Byte), helpers.SuccessCode},
		{bytes.NewReader(input3Byte), helpers.ParamErrorCode},
		{bytes.NewReader(input4Byte), helpers.ParamErrorCode},
	}

	for _, v := range caseList {
		raw := httptest.NewRequest("POST", "/", v.input)
		req := ghttp.NewRequest(time.Now(), raw)

		resp := SubmitDoubleDrug(ctx, req).(*ghttp.JSONResponse)
		data := resp.Data.(helpers.JsonResp)
		if data.Code != v.output {
			fmt.Println(data)
			t.Errorf("code=%d, want=%d", data.Code, v.output)
		}
	}
}

// TestSubmitExactDrug 测试SubmitExactDrug函数，该函数用于提交精确药物信息
// 参数t *testing.T：表示当前的测试对象
// 返回值类型为空，无返回值
func TestSubmitExactDrug(t *testing.T) {
	ctx := context.Background()
	ctx = context.WithValue(ctx, "user_info", models.UserInfo{UserID: 1, Type: 1})

	input1 := map[string]interface{}{
		"drug_list": []string{"CIDs00387447"},
		"mol_url":   "bos:/bml-test-test/helix_upload/f7ad817d-614f-42bf-8f13-0a866d8000e2.csv",
		"sample":    "sss",
	}
	input1Byte, _ := json.Marshal(input1)
	input3 := map[string]interface{}{
		"drug_list":  []string{"CIDs00387447"},
		"serial":     "xxxx",
		"sample_url": "bos:/bml-test-test/helix_upload/f7ad817d-614f-42bf-8f13-0a866d8000e2.csv",
	}
	input3Byte, _ := json.Marshal(input3)

	caseList := []struct {
		input  io.Reader
		output int
	}{
		{nil, helpers.ParamErrorCode},
		{bytes.NewReader(input1Byte), helpers.SuccessCode},
		{bytes.NewReader(input3Byte), helpers.SuccessCode},
	}

	for _, v := range caseList {
		raw := httptest.NewRequest("POST", "/", v.input)
		req := ghttp.NewRequest(time.Now(), raw)

		resp := SubmitExactDrug(ctx, req).(*ghttp.JSONResponse)
		data := resp.Data.(helpers.JsonResp)
		if data.Code != v.output {
			fmt.Println(data)
			t.Errorf("code=%d, want=%d", data.Code, v.output)
		}
	}
}
