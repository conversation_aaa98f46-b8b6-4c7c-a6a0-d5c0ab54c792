package controllers

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"strings"
	"time"
	"unicode"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"icode.baidu.com/baidu/gdp/ghttp"

	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/library/bce"
	"icode.baidu.com/helix_web/library/bceaa"
	"icode.baidu.com/helix_web/library/ctxutils"
	"icode.baidu.com/helix_web/library/resource"
	"icode.baidu.com/helix_web/models"
	"icode.baidu.com/helix_web/services"
)

const (
	RNAConfStrLimit = 1024
	TargetLenLimit  = 10
)

var iupacNucleotideTable = map[rune]struct{}{
	'B': {}, 'D': {}, 'H': {}, 'K': {}, 'M': {}, 'N': {}, 'R': {}, 'S': {}, 'V': {}, 'W': {}, 'Y': {},
}

// 提交预测
func SubmitUTR(ctx context.Context, req ghttp.Request) ghttp.Response {
	params := helpers.GetBodyParams(ctx, req)
	serial := helpers.GetStringParam(params, "cds_serial")
	fileUrl := helpers.GetStringParam(params, "cds_file_url")
	name := helpers.GetStringParam(params, "name")
	targetLen := helpers.GetIntParam(params, "target_len", 0)
	givenUtr := helpers.GetBoolParam(params, "given_utr", false)
	utrSerial := helpers.GetStringParam(params, "utr_serial")
	utrFileUrl := helpers.GetStringParam(params, "utr_file_url")

	// 参数校验
	name = getTaskName(name)
	err := checkTaskCommonParam(serial, fileUrl, name)
	if err != nil {
		return helpers.NewException(ctx, "PARAM_VALUE_ERR", err.Error())
	}

	// config 校验
	commonConf, contentStr, err := getCommonTaskConf(serial, fileUrl)
	if err != nil {
		return helpers.NewException(ctx, "PARAM_VALUE_ERR", err.Error())
	}
	if targetLen < TargetLenLimit {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "target_len param error")
	}
	if givenUtr && len(utrSerial) <= 0 && len(utrFileUrl) <= 0 {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "utr params error")
	}
	if len(utrSerial) > MaxSerialLimit || len(utrFileUrl) > MaxFileUrlLimit {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "param length limit")
	}

	// 计费校验
	chargeP := chargeParam{IsCharge: false}
	chargeTab, err := checkCharge(ctx, int64(models.TaskTypeUTR), services.Level1, chargeP)
	if err != nil {
		return helpers.NewException(ctx, "PARAM_VALUE_ERR", err.Error())
	}

	needConfig := models.TaskUTRConf{
		Serial:     commonConf.Serial,
		FileUrl:    commonConf.FileUrl,
		TargetLen:  targetLen,
		GivenUtr:   givenUtr,
		UtrSerial:  utrSerial,
		UtrFileUrl: utrFileUrl,
		Level:      services.Level1,
	}

	// 数据组装
	configByte, _ := json.Marshal(needConfig)
	taskN := models.Task{
		Name:      name,
		UserId:    uint64(getUserId(ctx)),
		Type:      uint64(models.TaskTypeUTR),
		FuncType:  uint64(models.FuncTypeForecast),
		ChargeTab: uint64(chargeTab),
		Config:    string(configByte),
		IsExample: uint64(checkSerialExample(ctx, int64(models.TaskTypeUTR), contentStr)),
	}
	taskNew, err := taskN.Add(ctx, taskN)
	if err != nil {
		return helpers.NewException(ctx, "DB_ERR")
	}

	// 提交任务到调度
	services.PreSubmitTask(ctx, taskNew)
	result := map[string]any{
		"task_id": taskNew.ID,
	}
	return helpers.SuccReturn(ctx, result)
}

// 提交预测
func SubmitLinearFold(ctx context.Context, req ghttp.Request) ghttp.Response {
	var param SubmitLinearFoldParam
	if req.Body() == nil {
		returnTrialTime(ctx)
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "empty body")
	}
	bodyData, err := io.ReadAll(req.Body())
	if err != nil {
		returnTrialTime(ctx)
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, err.Error())
	}
	defer req.Body().Close()
	if err := json.Unmarshal(bodyData, &param); err != nil {
		returnTrialTime(ctx)
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, err.Error())
	}

	param.NeedFold = true // 默认开启fold
	param.Name = getTaskName(param.Name)

	// 参数校验
	if err := checkLinearFoldParam(ctx, &param); err != nil {
		returnTrialTime(ctx)
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, err.Error())
	}

	// 计算费用
	cost := models.LinearFoldCost
	isTrial := ctxutils.GetIsTrial(ctx)
	if isTrial {
		cost = 0
	}

	// 校验余额是否充足，包括helix平台代金券、billing余额/代金券
	if err := checkUserBalance(ctx, cost, models.TaskTypeLinearFold); err != nil {
		returnTrialTime(ctx)
		return helpers.FailReturn(ctx, helpers.AccountErrorCode, err.Error())
	}

	// config 组装
	taskConf := convertToLinearFoldTaskConf(&param)
	taskConf.Cost = cost

	// 创建任务
	taskNew, err := createLinearFoldTask(ctx, &taskConf)
	if err != nil {
		returnTrialTime(ctx)
		return helpers.NewException(ctx, err.Error())
	}

	// 提交任务到调度
	if err := services.PreSubmitTaskChpc(ctx, *taskNew); err != nil {
		taskNew.Status = int64(models.TaskStatusFailed)
		taskNew.JobFailReason = err.Error()
		_ = taskNew.Save(ctx)
		return helpers.FailReturn(ctx, helpers.SubmitErrorCode, err.Error())
	}
	result := map[string]any{
		"task_id": taskNew.ID,
	}
	return helpers.SuccReturn(ctx, result)
}

// 提交预测
func SubmitLinearPartition(ctx context.Context, req ghttp.Request) ghttp.Response {
	var param SubmitLinearPartitionParam
	if req.Body() == nil {
		returnTrialTime(ctx)
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "empty body")
	}
	bodyData, err := io.ReadAll(req.Body())
	if err != nil {
		returnTrialTime(ctx)
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, err.Error())
	}
	defer req.Body().Close()
	if err := json.Unmarshal(bodyData, &param); err != nil {
		returnTrialTime(ctx)
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, err.Error())
	}

	// 设置LinearPartition特定的默认值,LinearPartition固定为true
	param.NeedPartition = true
	param.Name = getTaskName(param.Name)

	// 参数校验
	if err := checkLinearPartitionParam(ctx, &param); err != nil {
		returnTrialTime(ctx)
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, err.Error())
	}

	// 计算费用
	cost := models.LinearPartitionCost
	isTrial := ctxutils.GetIsTrial(ctx)
	if isTrial {
		cost = 0
	}

	// 校验余额是否充足，包括helix平台代金券、billing余额/代金券
	if err := checkUserBalance(ctx, cost, models.TaskTypeLinearPartition); err != nil {
		returnTrialTime(ctx)
		return helpers.FailReturn(ctx, helpers.AccountErrorCode, err.Error())
	}

	// config 组装
	taskConf := convertToLinearPartitionTaskConf(&param)
	taskConf.Cost = cost

	// 创建任务
	taskNew, err := createLinearPartitionTask(ctx, &taskConf)
	if err != nil {
		returnTrialTime(ctx)
		return helpers.NewException(ctx, err.Error())
	}

	// 提交任务到调度
	if err := services.PreSubmitTaskChpc(ctx, *taskNew); err != nil {
		taskNew.Status = int64(models.TaskStatusFailed)
		taskNew.JobFailReason = err.Error()
		_ = taskNew.Save(ctx)
		return helpers.FailReturn(ctx, helpers.SubmitErrorCode, err.Error())
	}
	result := map[string]any{
		"task_id": taskNew.ID,
	}
	return helpers.SuccReturn(ctx, result)
}

type SubmitLinearDesignParam struct {
	models.LinearDesignTaskConf
	Version             string `json:"version,omitempty"`
	Sequence5UTRFileURL string `json:"sequence5utr_file_url,omitempty"`
	Sequence3UTRFileURL string `json:"sequence3utr_file_url,omitempty"`
}

type SubmitLinearFoldParam struct {
	Name            string                   `json:"name"`
	Sequence        string                   `json:"sequence"`
	SequenceFileURL string                   `json:"sequence_file_url"`
	NeedFold        bool                     `json:"need_fold"`
	NeedPartition   bool                     `json:"need_partition"`
	FoldConfig      *models.LinearFoldConfig `json:"fold_config,omitempty"`
}

type SubmitLinearPartitionParam struct {
	Name            string                        `json:"name"`
	Sequence        string                        `json:"sequence"`
	SequenceFileURL string                        `json:"sequence_file_url"`
	NeedFold        bool                          `json:"need_fold"`
	NeedPartition   bool                          `json:"need_partition"`
	PartitionConfig *models.LinearPartitionConfig `json:"partition_config,omitempty"`
}

// 提交预测
func SubmitLinearDesign(ctx context.Context, req ghttp.Request) ghttp.Response {
	var param SubmitLinearDesignParam
	if req.Body() == nil {
		returnTrialTime(ctx)
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "empty body")
	}
	bodyData, err := io.ReadAll(req.Body())
	if err != nil {
		returnTrialTime(ctx)
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, err.Error())
	}
	defer req.Body().Close()
	if err := json.Unmarshal(bodyData, &param); err != nil {
		returnTrialTime(ctx)
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, err.Error())
	}

	// 参数校验
	if err := checkLinearDesignTaskParam(ctx, &param); err != nil {
		returnTrialTime(ctx)
		return helpers.NewException(ctx, "PARAM_VALUE_ERR", err.Error())
	}
	level := services.RNAVersionToLevelMap[param.Version]
	if level <= 0 {
		returnTrialTime(ctx)
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "version param error")
	}

	// 计算费用
	cost := models.LinearDesignVersionCost[param.Version]
	if cost == 0 {
		returnTrialTime(ctx)
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "version param error")
	}
	isTrial := ctxutils.GetIsTrial(ctx)
	if param.Version != "basic" && isTrial {
		returnTrialTime(ctx)
		isTrial = false
	}
	if isTrial {
		cost = 0
	}

	if param.Version == "advance" {
		cost *= float64(len(param.ParamCDS.BeamSize)) * float64(len(param.ParamCDS.CaiLambda))
	}

	// 校验余额是否充足，包括helix平台代金券、billing余额/代金券
	if err := checkUserBalance(ctx, cost, models.TaskTypeLinearDesign); err != nil {
		returnTrialTime(ctx)
		return helpers.FailReturn(ctx, helpers.AccountErrorCode, err.Error())
	}

	// config 组装
	needConf := getLinearDesignConfig(&param)
	needConf.Cost = cost

	// 创建任务
	taskNew, err := createLinearDesignTask(ctx, &needConf)
	if err != nil {
		returnTrialTime(ctx)
		return helpers.NewException(ctx, err.Error())
	}

	// 提交任务到调度
	if err := services.PreSubmitTaskChpc(ctx, *taskNew); err != nil {
		taskNew.Status = int64(models.TaskStatusFailed)
		taskNew.JobFailReason = err.Error()
		_ = taskNew.Save(ctx)
		return helpers.FailReturn(ctx, helpers.SubmitErrorCode, err.Error())
	}
	result := map[string]any{
		"task_id": taskNew.ID,
	}
	return helpers.SuccReturn(ctx, result)
}

func checkLinearDesignTaskParam(ctx context.Context, param *SubmitLinearDesignParam) error {
	// name
	param.Name = getTaskName(param.Name)
	if len([]rune(param.Name)) > MaxTaskNameLimit || !IsValidTaskName(param.Name) {
		return fmt.Errorf("task %s param error: job_name/name is invalid. The job_name/name should only contain letters,"+
			" numbers, -, _, ., and should not exceed 30 characters in length", param.Name)
	}
	// design_mode
	if param.DesignMode == "" {
		param.DesignMode = "cds"
	}
	// version
	if param.Version == "" {
		param.Version = "basic"
	}
	// sequenceType
	if param.SequenceType == "" {
		param.SequenceType = "Protein"
	}
	// sequence
	if param.Sequence == "" {
		if param.SequenceFileURL == "" {
			return errors.New("sequence or sequence_file_url is required")
		}
		sequence := getFileContent(param.SequenceFileURL)
		param.Sequence = sequence
	}
	if err := checkLinearDesignSequence(param.SequenceType, param.Version, param.Sequence); err != nil {
		return err
	}
	// sequence_5utr
	if param.DesignMode == "cds" {
		if param.Sequence5UTR == "" && param.Sequence5UTRFileURL != "" {
			sequence5UTR := getFileContent(param.Sequence5UTRFileURL)
			param.Sequence5UTR = sequence5UTR
		}
		if param.Sequence5UTR != "" {
			if len(param.Sequence5UTR) > 500 {
				return errors.New("sequence_5utr length error, max length is 500")
			}
			for _, token := range param.Sequence5UTR {
				if _, ok := nucleotideTable[unicode.ToUpper(token)]; !ok {
					return errors.New("invalid nucleotide")
				}
			}
		}
	}
	// sequence_3utr
	if param.Sequence3UTR == "" && param.Sequence3UTRFileURL != "" {
		sequence3UTR := getFileContent(param.Sequence3UTRFileURL)
		param.Sequence3UTR = sequence3UTR
	}
	if param.Sequence3UTR != "" {
		if len(param.Sequence3UTR) > 500 {
			return errors.New("sequence_3utr length error, max length is 500")
		}
		for _, token := range param.Sequence3UTR {
			if _, ok := nucleotideTable[unicode.ToUpper(token)]; !ok {
				return errors.New("invalid nucleotide")
			}
		}
	}
	// param_cds
	if err := checkLinearDesignParamCDS(param.Version, &param.ParamCDS); err != nil {
		return err
	}
	// param_5utr
	if param.Param5UTR.ExcludeMotifs != "" {
		excludeMotifs := strings.Split(param.Param5UTR.ExcludeMotifs, ",")
		for _, motif := range excludeMotifs {
			if len(motif) < 3 {
				return errors.New("exclude_motifs length error, min length is 3")
			}
			for _, token := range motif {
				if _, ok := nucleotideTable[unicode.ToUpper(token)]; !ok {
					return errors.New("invalid motif")
				}
			}
		}
	}
	if param.Param5UTR.UTR5SimilarityThreshold < 0 || param.Param5UTR.UTR5SimilarityThreshold > 1 {
		return errors.New("utr5_similarity_threshold range error, 0-1")
	}
	return nil
}

func checkLinearDesignSequence(sequenceType, version, sequence string) error {
	// 如果第一行包含">"，去掉第一行
	lines := strings.Split(sequence, "\n")
	if len(lines) > 0 && strings.Contains(lines[0], ">") {
		sequence = strings.Join(lines[1:], "")
	}
	switch sequenceType {
	case "Protein":
		for _, token := range sequence {
			if _, ok := aminoAcidTable[unicode.ToUpper(token)]; !ok && token != '*' {
				return errors.New("invalid amino: " + string(token))
			}
		}
		switch version {
		case "basic", "plus":
			if len(sequence) > 500 {
				return errors.New("sequence length error, basic and plus version max length is 500")
			}
		case "advance":
			if len(sequence) > 2000 {
				return errors.New("sequence length error, advance version max length is 2000")
			}
		}
	case "RNA":
		for _, token := range sequence {
			if _, ok := rnaNucleotideTable[unicode.ToUpper(token)]; !ok {
				return errors.New("invalid rna")
			}
		}
		switch version {
		case "basic", "plus":
			if len(sequence) > 1500 {
				return errors.New("sequence length error, basic and plus version max length is 500")
			}
		case "advance":
			if len(sequence) > 6000 {
				return errors.New("sequence length error, advance version max length is 2000")
			}
		}
	default:
		return errors.New("sequenceType param error")
	}
	return nil
}

func checkLinearDesignParamCDS(version string, paramCDS *models.LinearDesignParamCDS) error {
	if paramCDS.CodonTable == "" {
		paramCDS.CodonTable = "Human"
	}
	if paramCDS.CodonTable == "Custom" && paramCDS.CodonTableURL == "" {
		return errors.New("custom codon table need codon_table_url")
	}
	if paramCDS.CodonTableURL != "" {
		bucket, object := helpers.DealBosFileUrl(paramCDS.CodonTableURL)
		var content string
		var err error
		if bucket != "paddle-helix" {
			content, err = bce.GetObject(bucket, object)
			if err != nil {
				return fmt.Errorf("CodonTableURL file get failed: %w", err)
			}
			if err = bceaa.PutObject(object, content); err != nil {
				return fmt.Errorf("CodonTableURL file put failure: %w", err)
			}
			paramCDS.CodonTableURL = strings.Replace(paramCDS.CodonTableURL, bucket, "paddle-helix", 1)
		}
	}
	// beam_size, cai_lambda
	switch version {
	case "basic":
		if len(paramCDS.BeamSize) != 1 {
			return errors.New("basic version beam_size length is 1")
		}
		if paramCDS.BeamSize[0] < 50 || paramCDS.BeamSize[0] > 100 {
			return errors.New("basic version beam_size range is 50-100")
		}
		if len(paramCDS.CaiLambda) != 1 || paramCDS.CaiLambda[0] != 0 {
			return errors.New("basic version cai_lambda should be 0")
		}
	case "plus":
		if len(paramCDS.BeamSize) != 1 {
			return errors.New("basic version beam_size length is 1")
		}
		if paramCDS.BeamSize[0] < 50 && paramCDS.BeamSize[0] != -1 {
			return errors.New("plus version beam_size should be -1 or >=50")
		}
		if len(paramCDS.CaiLambda) != 1 || (paramCDS.CaiLambda[0] < 0 && paramCDS.CaiLambda[0] != -1) {
			return errors.New("plus version cai_lambda should be >=0 or -1")
		}
	case "advance":
		if len(paramCDS.BeamSize) == 0 {
			return errors.New("advance version beam_size can not be empty")
		}
		for _, beamSize := range paramCDS.BeamSize {
			if beamSize < 50 && beamSize != -1 {
				return errors.New("advance version beam_size should be -1 or >=50")
			}
		}
		if len(paramCDS.CaiLambda) == 0 {
			return errors.New("advance version cai_lambda can not be empty")
		}
		if len(paramCDS.BeamSize)*len(paramCDS.CaiLambda) > 10 {
			return errors.New("advance version beam_size and cai_lambda length product should be <= 10")
		}
		for _, caiLambda := range paramCDS.CaiLambda {
			if caiLambda < 0 && caiLambda != -1 {
				return errors.New("advance version cai_lambda should be >= 0 or -1")
			}
		}
	}
	// tie_lambda
	if len(paramCDS.TieLambda) != 1 || paramCDS.TieLambda[0] != 0 {
		return errors.New("tie_lambda should be 0")
	}
	// enzyme_cutting_site
	if len(paramCDS.EnzymeCuttingSite) != 0 {
		for _, enzyme := range paramCDS.EnzymeCuttingSite {
			if len(enzyme) < 4 {
				return errors.New("invalid enzyme_cutting_site, length should be >= 4")
			}
			for _, token := range enzyme {
				if _, ok := nucleotideTable[unicode.ToUpper(token)]; !ok {
					if _, ok := iupacNucleotideTable[unicode.ToUpper(token)]; !ok {
						return errors.New("invalid enzyme_cutting_site: " + string(token))
					}
				}
			}
		}
	}
	// s1,s2
	if paramCDS.LimitStemLength {
		if paramCDS.S1 == 0 {
			paramCDS.S1 = 33
		}
		if paramCDS.S2 == 0 {
			paramCDS.S2 = 15
		}
	}
	// max_gc_content
	if paramCDS.MaxGCContent != 0 && (paramCDS.MaxGCContent < 0.4 || paramCDS.MaxGCContent > 1) {
		return errors.New("max_gc_content should be 0.4-1")
	}
	return nil
}

// 获取配置信息
func getLinearDesignConfig(param *SubmitLinearDesignParam) models.LinearDesignTaskConf {
	level := services.RNAVersionToLevelMap[param.Version]
	return models.LinearDesignTaskConf{
		DesignMode:      param.DesignMode,
		Name:            param.Name,
		Sequence:        param.Sequence,
		SequenceFileURL: param.SequenceFileURL,
		SequenceType:    param.SequenceType,
		Sequence5UTR:    param.Sequence5UTR,
		Sequence3UTR:    param.Sequence3UTR,
		ParamCDS:        param.ParamCDS,
		Param5UTR:       param.Param5UTR,
		Level:           level,
	}
}

func createLinearDesignTask(ctx context.Context, taskConf *models.LinearDesignTaskConf) (
	*models.Task, error) {
	userType := ctxutils.GetUserInfo(ctx).Type
	userID := ctxutils.GetUserID(ctx)
	db := resource.Gorm.WithContext(ctx)
	// 开启事务
	tx := db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()
	// 查询代金券
	now := time.Now().Truncate(time.Second)
	var coupons []*models.Coupon
	if err := tx.Model(&models.Coupon{}).Where("user_id = ?", userID).
		Where("task_type = ?", models.TaskTypeLinearDesign).
		Where("status <> ?", models.StatusDel).
		Where("status <> ?", models.CouponStatusLose).
		Where("start_time <= ?", now).
		Where("end_time >= ?", now).
		Order(clause.OrderByColumn{Column: clause.Column{Name: "rest_amount"}}).
		Find(&coupons).Error; err != nil {
		tx.Rollback()
		helpers.DBLogError(ctx, err)
		return nil, err
	}
	maxCouponRetry := 3
	cost := taskConf.Cost
	var freezeCouponsByte []byte
	if userType == int64(models.UserTypeTry) {
		// 遍历代金券
		freezeCoupons := &models.FreezeCoupons{}
		for _, coupon := range coupons {
			if cost == 0 {
				break
			}
			if coupon.RestAmount <= 0 || coupon.Amount < coupon.RestAmount {
				continue
			}
			var rangeList []int64
			if err := json.Unmarshal([]byte(coupon.RangeList), &rangeList); err != nil {
				tx.Rollback()
				helpers.DBLogError(ctx, err)
				return nil, err
			}
			if !helpers.Contains(rangeList, int64(taskConf.Level)) {
				continue
			}
			// 最多重试三次
			retry := 0
			for {
				if retry > maxCouponRetry {
					tx.Rollback()
					return nil, errors.New("请求频繁，请稍候再试")
				}
				// 再次查询代金券，防止并发扣减导致金额错误
				if err := tx.Model(&models.Coupon{}).Where("id = ?", coupon.ID).First(coupon).Error; err != nil {
					tx.Rollback()
					helpers.DBLogError(ctx, err)
					return nil, err
				}
				// 扣减代金券
				useCoupon := 0.0
				if coupon.RestAmount >= cost {
					useCoupon = cost
				} else {
					useCoupon = coupon.RestAmount
				}
				// 尝试更新代金券
				couponTx := tx.Model(coupon).Where("rest_amount >= ?", useCoupon).
					Update("rest_amount", gorm.Expr(fmt.Sprintf("rest_amount - %f", useCoupon)))
				if couponTx.Error != nil {
					tx.Rollback()
					helpers.DBLogError(ctx, couponTx.Error)
					return nil, couponTx.Error
				}
				// 说明已经被其他进程更新，重试
				if couponTx.RowsAffected == 0 {
					retry++
					continue
				}
				freezeCoupon := &models.FreezeCoupon{}
				freezeCoupon.CouponID = coupon.ID
				freezeCoupon.Amount = useCoupon
				freezeCoupons.Coupons = append(freezeCoupons.Coupons, freezeCoupon)
				cost -= useCoupon
				break
			}
		}
		freezeCouponsByte, _ = json.Marshal(freezeCoupons)
	}
	// 插入任务
	taskConf.BillingUnitCount = cost / models.HelixCPUBillingUnitPrice
	configByte, _ := json.Marshal(taskConf)
	taskN := models.Task{
		Name:            taskConf.Name,
		UserId:          uint64(getUserId(ctx)),
		Type:            uint64(models.TaskTypeLinearDesign),
		FuncType:        uint64(models.FuncTypeForecast),
		Config:          string(configByte),
		Balance:         taskConf.BillingUnitCount * models.HelixCPUBillingUnitPrice,
		Coupons:         string(freezeCouponsByte),
		IAMUserID:       ctxutils.GetIAMUserID(ctx),
		IAMUserDomainID: ctxutils.GetIAMUserDomainID(ctx),
	}
	if err := tx.Model(&taskN).Create(&taskN).Error; err != nil {
		tx.Rollback()
		helpers.DBLogError(ctx, err)
		return nil, err
	}
	// 提交事务
	if err := tx.Commit().Error; err != nil {
		tx.Rollback()
		helpers.DBLogError(ctx, err)
		return nil, err
	}
	return &taskN, nil
}

func createLinearFoldTask(ctx context.Context, taskConf *models.LinearFoldTaskConf) (
	*models.Task, error) {
	userType := ctxutils.GetUserInfo(ctx).Type
	userID := ctxutils.GetUserID(ctx)
	db := resource.Gorm.WithContext(ctx)
	// 开启事务
	tx := db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()
	// 查询代金券
	now := time.Now().Truncate(time.Second)
	var coupons []*models.Coupon
	if err := tx.Model(&models.Coupon{}).Where("user_id = ?", userID).
		Where("task_type = ?", models.TaskTypeLinearFold).
		Where("status <> ?", models.StatusDel).
		Where("status <> ?", models.CouponStatusLose).
		Where("start_time <= ?", now).
		Where("end_time >= ?", now).
		Order(clause.OrderByColumn{Column: clause.Column{Name: "rest_amount"}}).
		Find(&coupons).Error; err != nil {
		tx.Rollback()
		helpers.DBLogError(ctx, err)
		return nil, err
	}
	maxCouponRetry := 3
	cost := taskConf.Cost
	var freezeCouponsByte []byte
	if userType == int64(models.UserTypeTry) {
		// 遍历代金券
		freezeCoupons := &models.FreezeCoupons{}
		for _, coupon := range coupons {
			if cost == 0 {
				break
			}
			if coupon.RestAmount <= 0 || coupon.Amount < coupon.RestAmount {
				continue
			}
			var rangeList []int64
			if err := json.Unmarshal([]byte(coupon.RangeList), &rangeList); err != nil {
				tx.Rollback()
				helpers.DBLogError(ctx, err)
				return nil, err
			}
			if !helpers.Contains(rangeList, int64(services.Level1)) {
				continue
			}
			// 最多重试三次
			retry := 0
			for {
				if retry > maxCouponRetry {
					tx.Rollback()
					return nil, errors.New("请求频繁，请稍候再试")
				}
				// 再次查询代金券，防止并发扣减导致金额错误
				if err := tx.Model(&models.Coupon{}).Where("id = ?", coupon.ID).First(coupon).Error; err != nil {
					tx.Rollback()
					helpers.DBLogError(ctx, err)
					return nil, err
				}
				// 扣减代金券
				useCoupon := 0.0
				if coupon.RestAmount >= cost {
					useCoupon = cost
				} else {
					useCoupon = coupon.RestAmount
				}
				// 尝试更新代金券
				couponTx := tx.Model(coupon).Where("rest_amount >= ?", useCoupon).
					Update("rest_amount", gorm.Expr(fmt.Sprintf("rest_amount - %f", useCoupon)))
				if couponTx.Error != nil {
					tx.Rollback()
					helpers.DBLogError(ctx, couponTx.Error)
					return nil, couponTx.Error
				}
				// 说明已经被其他进程更新，重试
				if couponTx.RowsAffected == 0 {
					retry++
					continue
				}
				freezeCoupon := &models.FreezeCoupon{}
				freezeCoupon.CouponID = coupon.ID
				freezeCoupon.Amount = useCoupon
				freezeCoupons.Coupons = append(freezeCoupons.Coupons, freezeCoupon)
				cost -= useCoupon
				break
			}
		}
		freezeCouponsByte, _ = json.Marshal(freezeCoupons)
	}
	// 插入任务
	taskConf.BillingUnitCount = cost / models.HelixFoldBillingUnitPrice
	configByte, _ := json.Marshal(taskConf)
	taskN := models.Task{
		Name:            taskConf.Name,
		UserId:          uint64(getUserId(ctx)),
		Type:            uint64(models.TaskTypeLinearFold),
		FuncType:        uint64(models.FuncTypeForecast),
		Config:          string(configByte),
		Balance:         taskConf.BillingUnitCount * models.HelixFoldBillingUnitPrice,
		Coupons:         string(freezeCouponsByte),
		IAMUserID:       ctxutils.GetIAMUserID(ctx),
		IAMUserDomainID: ctxutils.GetIAMUserDomainID(ctx),
	}
	if err := tx.Model(&taskN).Create(&taskN).Error; err != nil {
		tx.Rollback()
		helpers.DBLogError(ctx, err)
		return nil, err
	}
	// 提交事务
	if err := tx.Commit().Error; err != nil {
		tx.Rollback()
		helpers.DBLogError(ctx, err)
		return nil, err
	}
	return &taskN, nil
}

func createLinearPartitionTask(ctx context.Context, taskConf *models.LinearPartitionTaskConf) (
	*models.Task, error) {
	userType := ctxutils.GetUserInfo(ctx).Type
	userID := ctxutils.GetUserID(ctx)
	db := resource.Gorm.WithContext(ctx)
	// 开启事务
	tx := db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()
	// 查询代金券
	now := time.Now().Truncate(time.Second)
	var coupons []*models.Coupon
	if err := tx.Model(&models.Coupon{}).Where("user_id = ?", userID).
		Where("task_type = ?", models.TaskTypeLinearPartition).
		Where("status <> ?", models.StatusDel).
		Where("status <> ?", models.CouponStatusLose).
		Where("start_time <= ?", now).
		Where("end_time >= ?", now).
		Order(clause.OrderByColumn{Column: clause.Column{Name: "rest_amount"}}).
		Find(&coupons).Error; err != nil {
		tx.Rollback()
		helpers.DBLogError(ctx, err)
		return nil, err
	}
	maxCouponRetry := 3
	cost := taskConf.Cost
	var freezeCouponsByte []byte
	if userType == int64(models.UserTypeTry) {
		// 遍历代金券
		freezeCoupons := &models.FreezeCoupons{}
		for _, coupon := range coupons {
			if cost == 0 {
				break
			}
			if coupon.RestAmount <= 0 || coupon.Amount < coupon.RestAmount {
				continue
			}
			var rangeList []int64
			if err := json.Unmarshal([]byte(coupon.RangeList), &rangeList); err != nil {
				tx.Rollback()
				helpers.DBLogError(ctx, err)
				return nil, err
			}
			if !helpers.Contains(rangeList, int64(services.Level1)) {
				continue
			}
			// 最多重试三次
			retry := 0
			for {
				if retry > maxCouponRetry {
					tx.Rollback()
					return nil, errors.New("请求频繁，请稍候再试")
				}
				// 再次查询代金券，防止并发扣减导致金额错误
				if err := tx.Model(&models.Coupon{}).Where("id = ?", coupon.ID).First(coupon).Error; err != nil {
					tx.Rollback()
					helpers.DBLogError(ctx, err)
					return nil, err
				}
				// 扣减代金券
				useCoupon := 0.0
				if coupon.RestAmount >= cost {
					useCoupon = cost
				} else {
					useCoupon = coupon.RestAmount
				}
				// 尝试更新代金券
				couponTx := tx.Model(coupon).Where("rest_amount >= ?", useCoupon).
					Update("rest_amount", gorm.Expr(fmt.Sprintf("rest_amount - %f", useCoupon)))
				if couponTx.Error != nil {
					tx.Rollback()
					helpers.DBLogError(ctx, couponTx.Error)
					return nil, couponTx.Error
				}
				// 说明已经被其他进程更新，重试
				if couponTx.RowsAffected == 0 {
					retry++
					continue
				}
				freezeCoupon := &models.FreezeCoupon{}
				freezeCoupon.CouponID = coupon.ID
				freezeCoupon.Amount = useCoupon
				freezeCoupons.Coupons = append(freezeCoupons.Coupons, freezeCoupon)
				cost -= useCoupon
				break
			}
		}
		freezeCouponsByte, _ = json.Marshal(freezeCoupons)
	}
	// 插入任务
	taskConf.BillingUnitCount = cost / models.HelixFoldBillingUnitPrice
	configByte, _ := json.Marshal(taskConf)
	taskN := models.Task{
		Name:            taskConf.Name,
		UserId:          uint64(getUserId(ctx)),
		Type:            uint64(models.TaskTypeLinearPartition),
		FuncType:        uint64(models.FuncTypeForecast),
		Config:          string(configByte),
		Balance:         taskConf.BillingUnitCount * models.HelixFoldBillingUnitPrice,
		Coupons:         string(freezeCouponsByte),
		IAMUserID:       ctxutils.GetIAMUserID(ctx),
		IAMUserDomainID: ctxutils.GetIAMUserDomainID(ctx),
	}
	if err := tx.Model(&taskN).Create(&taskN).Error; err != nil {
		tx.Rollback()
		helpers.DBLogError(ctx, err)
		return nil, err
	}
	// 提交事务
	if err := tx.Commit().Error; err != nil {
		tx.Rollback()
		helpers.DBLogError(ctx, err)
		return nil, err
	}
	return &taskN, nil
}

// convertToLinearFoldTaskConf 转换为LinearFoldTaskConf
func convertToLinearFoldTaskConf(param *SubmitLinearFoldParam) models.LinearFoldTaskConf {
	return models.LinearFoldTaskConf{
		Name:             param.Name,
		Sequence:         param.Sequence,
		SequenceFileURL:  param.SequenceFileURL,
		NeedFold:         param.NeedFold,
		NeedPartition:    param.NeedPartition,
		FoldConfig:       param.FoldConfig,
		Cost:             0, // 将在调用处设置
		BillingUnitCount: 0, // 将在创建任务时计算
	}
}

// convertToLinearPartitionTaskConf 转换为LinearPartitionTaskConf
func convertToLinearPartitionTaskConf(param *SubmitLinearPartitionParam) models.LinearPartitionTaskConf {

	return models.LinearPartitionTaskConf{
		Name:             param.Name,
		Sequence:         param.Sequence,
		SequenceFileURL:  param.SequenceFileURL,
		NeedFold:         param.NeedFold,
		NeedPartition:    param.NeedPartition,
		PartitionConfig:  param.PartitionConfig,
		Cost:             0, // 将在调用处设置
		BillingUnitCount: 0, // 将在创建任务时计算
	}
}

// LinearFold 任务参数校验
func checkLinearFoldParam(ctx context.Context, param *SubmitLinearFoldParam) error {
	// name 校验
	if len([]rune(param.Name)) > MaxTaskNameLimit {
		return fmt.Errorf("task %s param error: job_name/name is invalid. The job_name/name "+
			"should not exceed %d characters in length", param.Name, MaxTaskNameLimit)
	}

	// sequence 和 sequence_file_url 校验
	if param.Sequence == "" && param.SequenceFileURL == "" {
		return errors.New("sequence and sequence_file_url cannot both be empty")
	}
	if param.Sequence != "" && param.SequenceFileURL != "" {
		return errors.New("sequence and sequence_file_url cannot both be provided")
	}

	// sequence 校验
	if param.Sequence == "" {
		param.Sequence = getFileContent(param.SequenceFileURL)
	}
	lines := strings.Split(param.Sequence, "\n")
	// 判断结构约束
	if strings.ContainsAny(lines[len(lines)-1], "?.()") {
		if param.FoldConfig.UseConstraints {
			param.FoldConfig.ConstraintStr = lines[len(lines)-1]
		}
		param.Sequence = strings.Join(lines[:len(lines)-1], "\n")
	} else {
		param.FoldConfig.UseConstraints = false
	}
	sequence := param.Sequence
	// 如果第一行包含">"，去掉第一行
	if len(lines) > 0 && strings.Contains(lines[0], ">") {
		if len(lines) == 1 {
			return errors.New("sequence is empty")
		}
		sequence = lines[1]
	}
	if len(sequence) == 0 || len(sequence) > 10000 {
		return errors.New("sequence length cannot be 0 or exceed 10000 characters")
	}
	// 校验序列字符
	for _, char := range sequence {
		if char != 'A' && char != 'U' && char != 'G' && char != 'C' && char != 'T' &&
			char != 'a' && char != 'u' && char != 'g' && char != 'c' && char != 't' {
			return fmt.Errorf("invalid nucleotide character: %c", char)
		}
	}

	// need_fold 和 need_partition 校验
	if !param.NeedFold && !param.NeedPartition {
		return errors.New("at least one of need_fold or need_partition must be true")
	}

	// fold_config 校验
	if param.FoldConfig == nil {
		return errors.New("fold_config is required when need_fold is true")
	}
	if param.FoldConfig.BeamSize <= 0 || param.FoldConfig.BeamSize > MaxBeamSizeLimit {
		return fmt.Errorf("fold_config beam_size must be between 1 and %d", MaxBeamSizeLimit)
	}
	if param.FoldConfig.ZukerScore < 0 || param.FoldConfig.ZukerScore > MaxScoreLimit {
		return fmt.Errorf("fold_config zuker_score must be between 0 and %d", MaxScoreLimit)
	}

	return nil
}

// LinearPartition 任务参数校验
func checkLinearPartitionParam(ctx context.Context, param *SubmitLinearPartitionParam) error {
	// name 校验
	if len([]rune(param.Name)) > MaxTaskNameLimit {
		return fmt.Errorf("task %s param error: job_name/name is invalid. The job_name/name "+
			"should not exceed %d characters in length", param.Name, MaxTaskNameLimit)
	}

	// sequence 和 sequence_file_url 校验
	if param.Sequence == "" && param.SequenceFileURL == "" {
		return errors.New("sequence and sequence_file_url cannot both be empty")
	}
	if param.Sequence != "" && param.SequenceFileURL != "" {
		return errors.New("sequence and sequence_file_url cannot both be provided")
	}

	// sequence 校验
	if param.Sequence == "" {
		param.Sequence = getFileContent(param.SequenceFileURL)
	}
	sequence := param.Sequence
	lines := strings.Split(param.Sequence, "\n")
	// 如果第一行包含">"，去掉第一行
	if len(lines) > 0 && strings.Contains(lines[0], ">") {
		if len(lines) == 1 {
			return errors.New("sequence is empty")
		}
		sequence = lines[1]
	}
	if len(sequence) > 10000 {
		return errors.New("sequence length cannot exceed 10000 characters")
	}
	// 校验序列字符
	for _, char := range sequence {
		if char != 'A' && char != 'U' && char != 'G' && char != 'C' && char != 'T' &&
			char != 'a' && char != 'u' && char != 'g' && char != 'c' && char != 't' {
			return fmt.Errorf("invalid nucleotide character: %c", char)
		}
	}

	// partition_config 校验
	if param.PartitionConfig == nil {
		return errors.New("partition_config is required for LinearPartition tasks")
	}
	if param.PartitionConfig.BeamSize == 0 {
		param.PartitionConfig.BeamSize = 100
	}
	if param.PartitionConfig.BeamSize <= 0 || param.PartitionConfig.BeamSize > MaxBeamSizeLimit {
		return fmt.Errorf("partition_config beam_size must be between 1 and %d", MaxBeamSizeLimit)
	}

	return nil
}
