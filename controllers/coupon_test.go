package controllers

import (
	"bytes"
	"context"
	"encoding/json"
	"net/http/httptest"
	"testing"
	"time"

	"icode.baidu.com/baidu/gdp/ghttp"
	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/models"
)

// TestGetCouponList 测试函数TestGetCouponList，用于获取优惠券列表
// 参数t *testing.T：指向当前测试用例的指针，用于标记是否通过测试
// 返回值类型为空，不需要返回值
func TestGetCouponList(t *testing.T) {
	ctx := context.Background()
	ctx = context.WithValue(ctx, "user_info", models.UserInfo{UserID: 1, Type: 1})

	input1, _ := json.Marshal(map[string]interface{}{
		"task_type": models.TaskTypeLinearDesign,
	})
	input2, _ := json.Marshal(map[string]interface{}{
		"task_type": models.TaskTypeProtein,
	})

	caseList := []struct {
		input  []byte
		output int
	}{
		{nil, helpers.ParamErrorCode},
		{input1, helpers.SuccessCode},
		{input2, helpers.SuccessCode},
	}

	for _, v := range caseList {
		raw := httptest.NewRequest("POST", "/", bytes.NewReader(v.input))
		req := ghttp.NewRequest(time.Now(), raw)

		resp := GetCouponList(ctx, req).(*ghttp.JSONResponse)
		data := resp.Data.(helpers.JsonResp)
		if data.Code != v.output {
			t.Errorf("code=%d, want=%d", data.Code, v.output)
		}
	}
}

// TestCouponCostList 测试函数，用于获取优惠券消费记录列表
// 参数：t *testing.T - 指向testing包中的*testing.T类型的指针，表示当前测试用例
// 返回值：无
func TestCouponCostList(t *testing.T) {
	ctx := context.Background()
	ctx = context.WithValue(ctx, "user_info", models.UserInfo{UserID: 1, Type: 1})

	input1, _ := json.Marshal(map[string]interface{}{
		"limit": 2,
	})
	input2, _ := json.Marshal(map[string]interface{}{
		"page": 2,
	})

	caseList := []struct {
		input  []byte
		output int
	}{
		{nil, helpers.SuccessCode},
		{input1, helpers.SuccessCode},
		{input2, helpers.SuccessCode},
	}

	for _, v := range caseList {
		raw := httptest.NewRequest("POST", "/", bytes.NewReader(v.input))
		req := ghttp.NewRequest(time.Now(), raw)

		resp := CouponCostList(ctx, req).(*ghttp.JSONResponse)
		data := resp.Data.(helpers.JsonResp)
		if data.Code != v.output {
			t.Errorf("coupon cost code=%d, want=%d", data.Code, v.output)
		}
	}

	for _, v := range caseList {
		raw := httptest.NewRequest("POST", "/", bytes.NewReader(v.input))
		req := ghttp.NewRequest(time.Now(), raw)

		resp := CouponSendList(ctx, req).(*ghttp.JSONResponse)
		data := resp.Data.(helpers.JsonResp)
		if data.Code != v.output {
			t.Errorf("coupon send code=%d, want=%d", data.Code, v.output)
		}
	}
}
