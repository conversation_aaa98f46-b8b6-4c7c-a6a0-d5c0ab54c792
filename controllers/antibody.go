package controllers

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strings"

	"icode.baidu.com/baidu/gdp/ghttp"
	"icode.baidu.com/baidu/gdp/logit"

	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/library/bce"
	"icode.baidu.com/helix_web/library/bceaa"
	"icode.baidu.com/helix_web/library/ctxutils"
	"icode.baidu.com/helix_web/library/resource"
	"icode.baidu.com/helix_web/library/tool"
	"icode.baidu.com/helix_web/models"
	"icode.baidu.com/helix_web/services"
)

const (
	defaultAntibodyDesignNum  = 100
	defaultAntibodyDesignMode = "basic"
)

var (
	antibodyDesignModeMap = map[string]struct{}{
		"basic":   {},
		"plus":    {},
		"advance": {},
		"pro":     {},
	}
)

type AntibodyDesignConf struct {
	models.AntibodyDesignTask
	Cost   float64
	Tokens uint64
}

type BatchSubmitAntibodyDesignTaskParam struct {
	Tasks []models.AntibodyDesignTask `json:"tasks"`
}

type CheckAntibodyDesignTaskParam struct {
	Tasks []models.AntibodyDesignTask `json:"tasks"`
}

func CheckAntibodyDesignTask(ctx context.Context, req ghttp.Request) ghttp.Response {
	// 获取参数
	var param CheckAntibodyDesignTaskParam
	if req.Body() == nil {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "empty body")
	}
	bodyData, err := io.ReadAll(req.Body())
	if err != nil {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, err.Error())
	}
	defer req.Body().Close()
	if err := json.Unmarshal(bodyData, &param); err != nil {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, err.Error())
	}
	if len(param.Tasks) != 1 {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "only support one task")
	}
	resource.LoggerService.Notice(ctx, "http server request params:",
		logit.Reflect("body", string(bodyData)),
	)
	// 校验任务参数
	resp, err := checkAntibodyDesignTaskParam(ctx, &param.Tasks[0], true)
	if err != nil {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, err.Error())
	}
	return helpers.SuccReturn(ctx, resp)
}

// BatchSubmitAntibodyDesignTask 批量提交任务
func BatchSubmitAntibodyDesignTask(ctx context.Context, req ghttp.Request) ghttp.Response {
	// 获取参数
	var param BatchSubmitAntibodyDesignTaskParam
	if req.Body() == nil {
		returnTrialTime(ctx)
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "empty body")
	}
	bodyData, err := io.ReadAll(req.Body())
	if err != nil {
		returnTrialTime(ctx)
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, err.Error())
	}
	defer req.Body().Close()
	if err = json.Unmarshal(bodyData, &param); err != nil {
		returnTrialTime(ctx)
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, err.Error())
	}
	resource.LoggerService.Notice(ctx, "http server request params:",
		logit.Reflect("body", string(bodyData)),
	)
	tasks := param.Tasks
	if len(tasks) <= 0 {
		returnTrialTime(ctx)
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "param tasks is empty")
	}
	// 校验任务参数
	if err := checkAntibodyDesignTasksParam(ctx, tasks); err != nil {
		// 返还试用次数
		returnTrialTime(ctx)
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, err.Error())
	}
	// TODO 校验权限
	// 计算任务费用并组装任务
	taskConfs, _, err := figureAntibodyDesignTasksCost(ctx, tasks, true)
	if err != nil {
		// 返还试用次数
		returnTrialTime(ctx)
		return helpers.FailReturn(ctx, helpers.ServiceErrorCode, err.Error())
	}
	// TODO 校验余额是否充足
	// 提交调度任务
	taskIDs := submitAntibodyDesignTasks(ctx, taskConfs)
	// 返回结果
	result := map[string]any{
		"task_ids": taskIDs,
	}
	return helpers.SuccReturn(ctx, result)
}

func checkAntibodyDesignTasksParam(ctx context.Context, tasks []models.AntibodyDesignTask) error {
	for i := range tasks {
		if _, err := checkAntibodyDesignTaskParam(ctx, &tasks[i], false); err != nil {
			return err
		}
	}
	return nil
}

func checkAntibodyDesignTaskParam(ctx context.Context, task *models.AntibodyDesignTask, onlyCheck bool) (
	map[string]models.ChainInfo, error) {
	// name
	if len([]rune(task.JobName)) > MaxTaskNameLimit || !IsValidTaskName(task.JobName) {
		return nil, fmt.Errorf("task %s param error: job_name/name is invalid. The job_name/name should only contain letters,"+
			" numbers, -, _, ., and should not exceed 30 characters in length", task.JobName)
	}
	// design_num
	if task.DesignNum == 0 {
		task.DesignNum = defaultAntibodyDesignNum
	}
	// design_mode
	if task.DesignMode == "" {
		task.DesignMode = defaultAntibodyDesignMode
	}
	if _, ok := antibodyDesignModeMap[task.DesignMode]; !ok {
		return nil, fmt.Errorf("task %s param error: the value of design_mode is invalid", task.JobName)
	}
	// 处理参考文件路径
	task.Reference = strings.TrimSuffix(task.Reference, "qt")
	bucket, object := helpers.DealBosFileUrl(task.Reference)
	content := ""
	var err error
	if bucket != "paddle-helix" {
		content, err = bce.GetObject(bucket, object)
		if err != nil {
			return nil, fmt.Errorf("reference file get failed: %w", err)
		}
		if err = bceaa.PutObject(object, content); err != nil {
			return nil, fmt.Errorf("reference file put failure: %w", err)
		}
		task.Reference = strings.Replace(task.Reference, bucket, "paddle-helix", 1)
	} else {
		content, err = bceaa.GetObject(bucket, object)
		if err != nil {
			return nil, fmt.Errorf("reference file get failed: %w", err)
		}
	}
	// 算法侧校验
	// 生成临时文件
	fileName := helpers.GetRandomString(10) + filepath.Ext(object)
	tempDir := os.TempDir()
	filePath := filepath.Join(tempDir, fileName)
	tempFile, err := os.Create(filePath)
	if err != nil {
		return nil, fmt.Errorf("create temp file failed: %w", err)
	}
	// 运行结束后删除文件
	defer os.Remove(tempFile.Name())
	// 写入数据
	if _, err = tempFile.WriteString(content); err != nil {
		return nil, fmt.Errorf("write temp file failed: %w", err)
	}
	// 传给算法校验
	tmpTask := models.AntibodyDesignTask{
		JobName:      task.JobName,
		DesignNum:    task.DesignNum,
		Diverse:      task.Diverse,
		DesignMode:   task.DesignMode,
		Reference:    tempFile.Name(),
		DesignChains: task.DesignChains,
	}
	chains, err := tool.AntibodyDesignCheck(tmpTask)
	if err != nil {
		return nil, err
	}
	if onlyCheck {
		return chains, nil
	}
	// 校验
	heavyExist := false
	for _, chain := range chains {
		if chain.Function == models.AntibodyChainFunctionHchain {
			heavyExist = true
		}
		// CDR是否完整
		if chain.Function != models.AntibodyChainFunctionAntigen && !chain.CdrComplete {
			return nil, fmt.Errorf("task %s param error: Your input chain %s sequence is not complete! Please check your input cif/pdb file",
				task.JobName, chain.ChainID)
		}
	}
	// 是否有重链
	if !heavyExist {
		return nil, fmt.Errorf("task %s param error: heavy chain not exist", task.JobName)
	}
	chainLen := 0
	for _, selectChain := range task.DesignChains {
		chainInfo := chains[selectChain.ChainID]
		// Fv缺失是否>2
		if chainInfo.MissingFv > 2 {
			return nil, fmt.Errorf("task %s param error: No. of deleted residues > 2 in the selected chain %s fv",
				task.JobName, chainInfo.ChainID)
		}
		if chainInfo.Function == models.AntibodyChainFunctionAntigen {
			chainLen += chainInfo.LengthFill
		} else {
			chainLen += chainInfo.Length
		}
		for _, region := range selectChain.Region {
			if region == "ALL" && chainInfo.MissingFc > 2 {
				return nil, fmt.Errorf("task %s param error: No. of deleted residues > 2 in the selected chain %s fc",
					task.JobName, chainInfo.ChainID)
			}
		}
	}
	return chains, nil
}

func figureAntibodyDesignTasksCost(ctx context.Context, tasks []models.AntibodyDesignTask,
	isTrial bool) ([]*AntibodyDesignConf, float64, error) {
	var taskConfs []*AntibodyDesignConf
	taskCost := 0.0
	for _, task := range tasks {
		if task.JobName == "" {
			task.JobName = getTaskName(task.JobName)
		}
		// 计算费用，本期不计费
		cost := 0.0
		tokens := 0
		taskConf := &AntibodyDesignConf{
			AntibodyDesignTask: task,
			Cost:               cost,
			Tokens:             uint64(tokens),
		}
		taskConfs = append(taskConfs, taskConf)
		taskCost += cost
	}
	return taskConfs, taskCost, nil
}

func submitAntibodyDesignTasks(ctx context.Context, taskConfs []*AntibodyDesignConf) []uint64 {
	isApi := 0
	if _, ok := ctx.Value("is_api").(int); ok {
		isApi = 1
	}
	if _, ok := ctx.Value("is_api").(int); ok {
		isApi = 1
	}
	var taskIDs []uint64
	for _, taskConf := range taskConfs {
		// TODO 冻结代金券
		billingUnitCount := 0.0
		// 组装参数
		extendParams := models.TaskAntibodyDesignConf{
			DesignNum:        taskConf.DesignNum,
			Diverse:          taskConf.Diverse,
			DesignMode:       taskConf.DesignMode,
			DesignChains:     taskConf.DesignChains,
			Reference:        taskConf.Reference,
			Tokens:           taskConf.Tokens,
			BillingUnitCount: billingUnitCount,
		}
		extendParamsByte, _ := json.Marshal(extendParams)
		taskN := models.Task{
			Name:            taskConf.JobName,
			UserId:          uint64(getUserId(ctx)),
			Type:            uint64(models.TaskTypeAntibodyDesign),
			FuncType:        uint64(models.FuncTypeForecast),
			Config:          string(extendParamsByte),
			IsApi:           uint64(isApi),
			IAMUserID:       ctxutils.GetIAMUserID(ctx),
			IAMUserDomainID: ctxutils.GetIAMUserDomainID(ctx),
			NTokens:         taskConf.Tokens,
			Balance:         billingUnitCount * BILLING_UNIT_PRICE,
		}
		taskNew, err := taskN.Add(ctx, taskN)
		if err != nil {
			taskIDs = append(taskIDs, 0)
			// 返还代金券或试用次数
			returnTrialTime(ctx)
			continue
		}
		taskIDs = append(taskIDs, taskNew.ID)
		// submit task
		if err = services.PreSubmitTaskChpc(ctx, taskNew); err != nil {
			// 返还代金券或试用次数
			returnTrialTime(ctx)
			// 更新任务状态和失败原因
			taskNew.Status = int64(models.TaskStatusFailed)
			taskNew.JobFailReason = err.Error()
			_ = taskNew.Save(ctx)
			continue
		}
	}
	return taskIDs
}
