package controllers

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http/httptest"
	"testing"
	"time"

	"icode.baidu.com/baidu/gdp/ghttp"
	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/models"
)

// TestSubmitProtein 测试SubmitProtein函数，该函数用于提交蛋白质信息
// 参数：t *testing.T - 单元测试的对象指针，用于输出错误信息
// 返回值：无
func TestSubmitProtein(t *testing.T) {
	ctx := context.Background()
	ctx1 := context.WithValue(ctx, "user_info", models.UserInfo{UserID: 1, Type: 1})

	input1, _ := json.Marshal(map[string]interface{}{
		"serial": "testddsdsds",
	})

	caseList := []struct {
		ctx    context.Context
		input  io.Reader
		output int
	}{
		{ctx1, nil, helpers.ParamErrorCode},
		{ctx1, bytes.<PERSON><PERSON>eader(input1), helpers.SuccessCode},
	}

	for _, v := range caseList {
		raw := httptest.NewRequest("POST", "/", v.input)
		req := ghttp.NewRequest(time.Now(), raw)

		resp := SubmitProtein(v.ctx, req).(*ghttp.JSONResponse)
		data := resp.Data.(helpers.JsonResp)
		if data.Code != v.output {
			t.Errorf("code=%d, want=%d", data.Code, v.output)
		}
	}
}

// TestSubmitProteinSingle 测试SubmitProteinSingle函数，该函数用于提交单个蛋白质信息
// 参数t *testing.T：表示当前测试用例的上下文，必须传入
// 返回值bool：无返回值
func TestSubmitProteinSingle(t *testing.T) {
	ctx := context.Background()
	ctx = context.WithValue(ctx, "user_info", models.UserInfo{UserID: 1, Type: 1})

	input := map[string]interface{}{
		"file_url": "bos:/bml-test-test/output/100004/mol_generation_infer_result_to_display_20210923070525.csv",
	}
	input2Byte, _ := json.Marshal(input)

	input = map[string]interface{}{
		"name":   "xxs",
		"serial": "xxddddx",
	}
	input3Byte, _ := json.Marshal(input)

	caseList := []struct {
		input  io.Reader
		output int
	}{
		{nil, helpers.ParamErrorCode},
		{bytes.NewReader(input2Byte), helpers.SuccessCode},
		{bytes.NewReader(input3Byte), helpers.SuccessCode},
	}

	for _, v := range caseList {
		raw := httptest.NewRequest("POST", "/", v.input)
		req := ghttp.NewRequest(time.Now(), raw)

		resp := SubmitProteinSingle(ctx, req).(*ghttp.JSONResponse)
		data := resp.Data.(helpers.JsonResp)
		if data.Code != v.output {
			fmt.Println(data)
			t.Errorf("code=%d, want=%d", data.Code, v.output)
		}
	}
}

// TestSubmitProteinFunc 测试SubmitProteinFunc函数，该函数用于提交蛋白质信息
// 参数t *testing.T：表示当前的测试对象
// 返回值类型为空，不需要返回值
func TestSubmitProteinFunc(t *testing.T) {
	ctx := context.Background()
	ctx = context.WithValue(ctx, "user_info", models.UserInfo{UserID: 1, Type: 1})

	input := map[string]interface{}{
		"name": "xxs",
	}
	input1Byte, _ := json.Marshal(input)

	input = map[string]interface{}{
		"pdb_url": "bos:/bml-test-test/output/100004/mol_generation_infer_result_to_display_20210923070525.csv",
	}
	input2Byte, _ := json.Marshal(input)

	input = map[string]interface{}{
		"pdb_name": "xxs",
		"pdb_url":  "bos:/bml-test-test/output/100004/mol_generation_infer_result_to_display_20210923070525.csv",
	}
	input3Byte, _ := json.Marshal(input)

	caseList := []struct {
		input  io.Reader
		output int
	}{
		{nil, helpers.ParamErrorCode},
		{bytes.NewReader(input1Byte), helpers.ParamErrorCode},
		{bytes.NewReader(input2Byte), helpers.ParamErrorCode},
		{bytes.NewReader(input3Byte), helpers.SuccessCode},
	}

	for _, v := range caseList {
		raw := httptest.NewRequest("POST", "/", v.input)
		req := ghttp.NewRequest(time.Now(), raw)

		resp := SubmitProteinFunc(ctx, req).(*ghttp.JSONResponse)
		data := resp.Data.(helpers.JsonResp)
		if data.Code != v.output {
			fmt.Println(data)
			t.Errorf("code=%d, want=%d", data.Code, v.output)
		}
	}
}
