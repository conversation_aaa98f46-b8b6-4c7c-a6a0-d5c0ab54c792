package controllers

import (
	"context"
	"encoding/json"
	"errors"
	"math"
	"strconv"
	"strings"
	"time"

	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/library/bce"
	"icode.baidu.com/helix_web/library/ctxutils"
	"icode.baidu.com/helix_web/library/redis"
	"icode.baidu.com/helix_web/models"
	"icode.baidu.com/helix_web/services"
)

const (
	ChargeBalance = 1
	ChargeCoupon  = 2
)

type TaskPriceInfos struct {
	Prices               []*TaskPriceInfo `json:"prices"`
	TotalPrice           float64          `json:"total_amount"`
	DiscountedTotalPrice float64          `json:"discounted_total_price,omitempty"`
}

type TaskPriceInfo struct {
	Name  string  `json:"name"`
	Price float64 `json:"price"`
}

// 获取登陆用户Id
func getUserId(ctx context.Context) int64 {
	userInfo := ctx.Value("user_info").(models.UserInfo)
	userId := userInfo.UserID

	return userId
}

// 获得用户免费次数
func getUserFreeNum(ctx context.Context, userId int64, taskType int) int {
	awardKey := redis.UserAwardPrefix + strconv.Itoa(int(userId))
	countStr := redis.Get(ctx, awardKey)
	count, _ := strconv.Atoi(countStr)
	if taskType == models.TaskTypeHelixFoldAA || int(taskType) == models.TaskTypeHF3Agab || taskType == models.TaskTypeHelixFold3S1 {
		return models.UserFreeHelixFold3TaskLimit + count
	}
	// 小蛋白没有免费次数
	if taskType == models.TaskTypeMiniProteinDesign || taskType == models.TaskTypeAntibodyDesign {
		return 0
	}
	return models.UserFreeTaskLimit + count
}

// 获得用户已使用次数
func getUserUsedNum(ctx context.Context, userId int64, taskType int) int {
	cacheKey := redis.UserTaskNumPrefix + strconv.Itoa(int(userId))
	if taskType == models.TaskTypeHelixFoldAA || int(taskType) == models.TaskTypeHF3Agab || taskType == models.TaskTypeHelixFold3S1 {
		cacheKey = redis.UserHelixFold3TaskNumPrefix + strconv.Itoa(int(userId))
		timestampKey := redis.UserHelixFold3UsedPrefix + strconv.Itoa(int(userId))
		timestampStr := redis.Get(ctx, timestampKey)
		currDay := time.Now()
		if timestampStr != "" {
			timestamp, _ := strconv.ParseInt(timestampStr, 10, 64)
			lastDay := time.Unix(timestamp, 0)
			if !(lastDay.Year() == currDay.Year() && lastDay.YearDay() == currDay.YearDay()) {
				redis.Set(ctx, cacheKey, 0, redis.TaskUserNumTime)
				redis.Set(ctx, timestampKey, strconv.FormatInt(currDay.Unix(), 10), redis.TaskUserNumTime)
			}
		} else {
			redis.Set(ctx, timestampKey, strconv.FormatInt(currDay.Unix(), 10), redis.TaskUserNumTime)
		}
		countStr := redis.Get(ctx, cacheKey)
		count, _ := strconv.Atoi(countStr)
		return count
	}
	countStr := redis.Get(ctx, cacheKey)
	count, _ := strconv.Atoi(countStr)
	return count
}

// del page
func formatPaging(limit, page int64) (int64, int64) {
	if limit <= 0 || limit > MaxLimitNum {
		limit = DefaultLimit
	}
	if page <= 0 || page > MaxPageNum {
		page = DefaultPage
	}

	return limit, page
}

// 检查可以预处理的类型
func checkPretreatType(tType int) bool {
	if tType == models.TaskTypeVirtualFilter ||
		tType == models.TaskTypeHelixDock ||
		tType == models.TaskTypeMolFormation ||
		tType == models.TaskTypeMolFormPath ||
		tType == models.TaskTypeHelixVSSyn ||
		tType == models.TaskTypeProtein ||
		tType == models.TaskTypeVirtualVS ||
		tType == models.TaskTypeKYKT ||
		tType == models.TaskTypeProteinFunc {
		return true
	}
	return false
}

// 检查任务类型是否可以训练
func checkTrainTaskType(tType int) bool {
	res := models.TrainTaskTypeList[tType]
	return res
}

// 检查serial type类型
func checkSerialType(tType int) bool {
	res := models.TaskTypeList[tType]
	return res
}

func checkSerialFuncType(funcType int) bool {
	if funcType == models.SerialFuncTypeTrain ||
		funcType == models.SerialFuncTypeTrainClassify ||
		funcType == models.SerialFuncTypeForecast ||
		funcType == models.SerialFuncTypeForecastRNA ||
		funcType == models.SerialFuncTypeForecastCodon ||
		funcType == models.SerialFuncTypeForecast5UTR ||
		funcType == models.SerialFuncTypeForecast3UTR ||
		funcType == models.SerialFuncTypePretreat ||
		funcType == models.SerialFuncTypeNoStruct {
		return true
	}

	return false
}

func checkSerialDataType(dataType int) bool {
	if dataType == models.DataTypeFile ||
		dataType == models.DataTypeString ||
		dataType == models.DataTypeFileMore {
		return true
	}

	return false
}

// 检查训练的算法类型
func checkTrainAlgorithm(trainType string) bool {
	if trainType == models.TrainTypeCla || trainType == models.TrainTypeReg {
		return true
	}

	return false
}

// 检查任务是否可以删除
func checkTaskStatusDel(status int) bool {
	statusList := map[int]bool{
		models.TaskStatusCancel: true,
		models.TaskStatusSucc:   true,
		models.TaskStatusFailed: true,
	}

	res := statusList[status]
	return res
}

// 检查任务是否可以取消
func checkTaskStatusCancel(status int) bool {
	if status == models.TaskStatusDoing || status == models.TaskStatusNew {
		return true
	}

	return false
}

// 获取任务名字
func getTaskName(name string) string {
	if len(name) == 0 {
		name = helpers.GetRandomString(DefaultNameLength)
	}

	return name
}

// 检查任务通用参数
func checkTaskCommonParam(serial, fileUrl, name string) error {
	fileUrlLen := len([]rune(fileUrl))
	serialLen := len([]rune(serial))
	if fileUrlLen <= 0 && serialLen <= 0 {
		return errors.New("file_url or serial parameter cannot be empty")
	}
	if serialLen > MaxSerialLimit {
		return errors.New("serial parameter length exceeds maximum limit")
	}

	if fileUrlLen > MaxSerialLimit {
		return errors.New("file_url parameter length exceeds maximum limit")
	}
	if len([]rune(name)) > MaxTaskNameLimit {
		return errors.New("name parameter length exceeds maximum limit")
	}
	return nil
}

// 获取通用配置
func getCommonTaskConf(serial, fileUrl string) (models.TaskCommonConf, string, error) {
	var seqContent string
	var needConfig models.TaskCommonConf
	if len(serial) > 0 {
		needConfig.Serial = serial
		seqContent = serial
	} else {
		content := getFileContent(fileUrl)
		err := checkFileContent(content)
		if err != nil {
			return needConfig, "", err
		}
		needConfig.FileUrl = fileUrl
		seqContent = content
	}

	return needConfig, seqContent, nil
}

// 获取上传文件内容
func getFileContent(fileUrl string) string {
	bucket, object := helpers.DealBosFileUrl(fileUrl)
	if len(bucket) == 0 || len(object) == 0 {
		return ""
	}

	contentStr, err := bce.GetObject(bucket, object)
	if err != nil || len(contentStr) == 0 {
		return ""
	}

	contentStr = strings.Trim(contentStr, " ")
	return contentStr
}

// 检查文件内容
func checkFileContent(content string) error {
	contentLen := len(content)
	if contentLen == 0 || contentLen > MaxFileContentLimit {
		return errors.New("upload file error")
	}

	return nil
}

// check用户权限，尝试使用免费/试用次数
func CheckUserPermission(ctx context.Context, taskType int64, isForecast bool) (context.Context, error) {
	userInfo := ctx.Value("user_info").(models.UserInfo)
	if userInfo.Type == int64(models.UserTypeInside) {
		return ctx, nil
	}

	// 优先使用免费次数
	err := checkFreeTrial(ctx, userInfo.UserID, taskType)
	if err != nil && err.Error() != helpers.ErrorExceedFreeLimit {
		return ctx, err
	}
	if err == nil {
		return context.WithValue(ctx, models.IsTrialKey, true), nil
	}

	// 计费模块无试用次数
	if isForecast && models.CheckTaskChargeType(int(taskType)) {
		return ctx, nil
	}

	// 试用
	if userInfo.Type == int64(models.UserTypeTry) || userInfo.Type == int64(models.UserTypeCharge) {
		trialM := models.Trial{}
		trialList, err := trialM.GetTrial(ctx, userInfo.UserID, taskType)
		if err != nil {
			return ctx, errors.New("data error, please repeat")
		}

		for _, trialData := range trialList {
			if time.Now().Unix() < trialData.StartTime.Unix() {
				continue
			}
			if trialData.Type == uint64(models.TrialTypeTime) {
				if time.Now().Unix() < trialData.EndTime.Unix() {
					trialData.UsedNum = trialData.UsedNum + 1
					if err = trialData.Save(ctx); err != nil {
						return ctx, errors.New("data error, please repeat")
					}
					return context.WithValue(ctx, models.TrialIDKey, trialData.ID), nil
				}
			} else {
				if trialData.UsedNum < trialData.LimitNum {
					trialData.UsedNum = trialData.UsedNum + 1
					if err = trialData.Save(ctx); err != nil {
						return ctx, errors.New("data error, please repeat")
					}
					return context.WithValue(ctx, models.TrialIDKey, trialData.ID), nil
				}
			}

			// 失效试用
			trialData.Status = int64(models.TrialStatusLose)
			_ = trialData.Save(ctx)
		}
	}

	return ctx, nil
}

// check免费试用次数
func checkFreeTrial(ctx context.Context, userId int64, taskType int64) error {
	// 小蛋白设计模块和抗体设计模块没有免费次数
	if taskType == int64(models.TaskTypeMiniProteinDesign) || taskType == int64(models.TaskTypeAntibodyDesign) {
		return errors.New(helpers.ErrorExceedFreeLimit)
	}
	cacheKey := redis.UserTaskNumPrefix + strconv.Itoa(int(userId))
	lockKey := redis.UserTaskNumLock + strconv.Itoa(int(userId))
	// hf3和hf3agab共用免费次数
	if int(taskType) == models.TaskTypeHelixFoldAA || int(taskType) == models.TaskTypeHF3Agab || int(taskType) == models.TaskTypeHelixFold3S1 {
		cacheKey = redis.UserHelixFold3TaskNumPrefix + strconv.Itoa(int(userId))
		lockKey = redis.UserHelixFold3TaskNumLock + strconv.Itoa(int(userId))
	}
	res := redis.SetNX(ctx, lockKey, true, redis.TaskUserNumLockTime)
	if !res {
		return errors.New(helpers.ErrorRequestTooFast)
	}

	countStr := redis.Get(ctx, cacheKey)
	if countStr != "" {
		count, _ := strconv.Atoi(countStr)
		if count >= getUserFreeNum(ctx, userId, int(taskType)) {
			return errors.New(helpers.ErrorExceedFreeLimit)
		}

		redis.Incr(ctx, cacheKey)
	} else {
		redis.Set(ctx, cacheKey, 1, redis.TaskUserNumTime)
	}
	redis.Del(ctx, lockKey)

	return nil
}

// check 计费
type chargeParam struct {
	IsCharge bool
	IsApi    bool
}

// checkCharge 判断用户是否需要支付费用，返回对应的状态码和错误信息
// ctx: 上下文，包含了用户信息和其他必要参数
// taskType: 任务类型，int64类型，值为models.TaskTypeMRNA、models.TaskTypeAdmet等常量
// level: 级别，int类型，值为services.Level1、services.Level2等常量
// chargeP: chargeParam结构体，包含了一些关于支付相关的参数，如IsCharge、IsApi等
// 返回值：int类型，表示用户是否需要支付费用，值为models.ChargeTabNo、models.ChargeTabCoupon、models.ChargeTabBilling等常量
//
//	如果出现错误，则返回-1，error类型，表示错误信息
func checkCharge(ctx context.Context, taskType int64, level int, chargeP chargeParam) (int, error) {
	userInfo := ctx.Value("user_info").(models.UserInfo)
	if userInfo.Type == int64(models.UserTypeInside) {
		return models.ChargeTabNo, nil
	}
	if userInfo.Type == int64(models.UserTypeNormal) {
		if level > services.Level1 || chargeP.IsCharge {
			return models.ChargeTabNo, errors.New("序列长度过长，需要开通付费后使用")
		}
		return models.ChargeTabNo, nil
	}

	// 试用账户
	if userInfo.Type == int64(models.UserTypeTry) {
		couponM := models.Coupon{}
		couponList, err := couponM.GetList(ctx, userInfo.UserID, taskType)
		if err != nil {
			return models.ChargeTabNo, errors.New("data error, please repeat")
		}

		for _, item := range couponList {
			if time.Now().Unix() < item.StartTime.Unix() || item.Status == models.CouponStatusLose {
				continue
			}
			if time.Now().Unix() > item.EndTime.Unix() || item.RestAmount <= 0 {
				item.Status = int64(models.TrialStatusLose)
				_ = item.Save(ctx)
				continue
			}

			var rangeList []int64
			_ = json.Unmarshal([]byte(item.RangeList), &rangeList)
			if helpers.Contains(rangeList, int64(level)) {
				return models.ChargeTabCoupon, nil
			}
		}

		// 试用用户-优惠券已用完
		if level > services.Level1 || chargeP.IsCharge {
			return models.ChargeTabNo, errors.New("序列长度过长，需要开通付费后使用")
		}
		return models.ChargeTabNo, checkFreeTrial(ctx, userInfo.UserID, taskType)
	}

	// 付费用户
	if level == services.Level1 && !chargeP.IsCharge {
		err := checkFreeTrial(ctx, userInfo.UserID, taskType)
		if err == nil {
			return models.ChargeTabNo, nil
		}
	}

	// 查询流量包信息
	if taskType == int64(models.TaskTypeLinearDesign) || taskType == int64(models.TaskTypeAdmet) {
		var apiId int
		if taskType == int64(models.TaskTypeLinearDesign) {
			apiId = services.RNALevelToIdMap[level]
		} else {
			apiId = services.AdmetForecast
			if chargeP.IsApi {
				apiId = services.AdmetApi
			}
		}

		packList, err := services.GetPackageList(ctx, userInfo.RealID, userInfo.Source, []int{apiId})
		if err != nil {
			return models.ChargeTabNo, errors.New("data error, please repeat")
		}
		for _, packItem := range packList {
			if helpers.StrToInt(packItem.Total)-helpers.StrToInt(packItem.Usage) > 0 {
				return models.ChargeTabBilling, nil
			}
		}
	}

	// 查询是否开通后付费
	taskLevel := strconv.Itoa(int(taskType)) + "-" + strconv.Itoa(level)
	apiId := services.LevelToApiIdMap[taskLevel]
	if chargeP.IsApi {
		apiId = services.AdmetApi
	}
	orderList, err := services.GetOrderStatus(ctx, userInfo.RealID, userInfo.Source, []int{apiId})
	if err != nil {
		return models.ChargeTabNo, errors.New("data error, please repeat")
	}
	if len(orderList) > 0 && orderList[int64(apiId)].Status == services.OrderStatusStopped {
		return models.ChargeTabNo, errors.New("您的账户已欠费，请前往财务总览查看。")
	}
	if len(orderList) <= 0 || orderList[int64(apiId)].Status != services.OrderStatusOpened {
		return models.ChargeTabNo, errors.New("您还没有开通付费功能，请开通或前往财务总览查看。")
	}

	return models.ChargeTabBilling, nil
}

// task Map
func getTaskMap(ctx context.Context, taskIdList []int64) map[int]models.Task {
	taskMap := make(map[int]models.Task)

	taskM := models.Task{}
	taskList, err := taskM.GetAllByIds(ctx, taskIdList, "id desc")
	if err != nil {
		return taskMap
	}

	for _, task := range taskList {
		taskMap[int(task.ID)] = task
	}
	return taskMap
}

// check serial example
func checkSerialExample(ctx context.Context, taskType int64, contentStr string) int {
	serialM := models.Serial{}
	serialData, err := serialM.GetBySerial(ctx, taskType, contentStr)
	if err == nil && serialData.ID > 0 {
		return models.IsExampleTrue
	}

	return models.IsExampleFalse
}

// IsValidTaskName 函数名: IsValidTaskName
// 功能：判断给定的字符串是否为有效的任务名称，只允许使用大小写英文、数字、下划线、点和短横线，且最多30个字符。
// 参数:
//
//	name string - 待校验的字符串
//
// 返回值:
//
//	bool - 如果是有效的任务名称则返回true，否则返回false
func IsValidTaskName(name string) bool {
	return true
}

func roundToTwoSignificantFigures(num float64) float64 {
	if num == 0 {
		return 0
	}
	// 计算数量级
	order := math.Floor(math.Log10(math.Abs(num)))
	// 计算缩放因子，使得前两位数变成整数
	scale := math.Pow(10, order-1)
	// 四舍五入并返回
	return math.Round(num/scale) * scale
}

// 返还试用次数
func returnTrialTime(ctx context.Context) {
	trialID := ctxutils.GetTrialID(ctx)
	if trialID != 0 {
		trialM := models.Trial{}
		trial, err := trialM.GetTrialByID(ctx, trialID)
		if err != nil {
			helpers.LogError(ctx, err)
		}
		trial.UsedNum--
		_ = trial.Save(ctx)
	}
	if ctxutils.GetIsTrial(ctx) {
		cacheKey := redis.UserTaskNumPrefix + strconv.Itoa(int(ctxutils.GetUserID(ctx)))
		redis.IncrBy(ctx, cacheKey, -1)
	}
}
