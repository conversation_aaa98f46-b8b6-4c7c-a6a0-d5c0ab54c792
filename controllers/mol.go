package controllers

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"math"
	"strings"
	"time"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"icode.baidu.com/baidu/gdp/ghttp"
	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/library/bce"
	"icode.baidu.com/helix_web/library/bceaa"
	"icode.baidu.com/helix_web/library/chpc"
	"icode.baidu.com/helix_web/library/ctxutils"
	"icode.baidu.com/helix_web/library/resource"
	"icode.baidu.com/helix_web/models"
	"icode.baidu.com/helix_web/services"
)

var (
	helixVSMolecularLibraryAmount = map[string]float64{
		"Targetmol_CherryPick": 3.4,
		"Lifechemicals":        82.00,
		"ChemDiv":              283.00,
		"topscience database":  1687.00,
	}

	designModeEfficient = "Efficient"
	designModeDepth     = "Depth"

	helixVSSynDesignMode = map[string]int{
		designModeEfficient: 50000,
		designModeDepth:     500000,
	}
)

func SubmitHelixVSSyn(ctx context.Context, req ghttp.Request) ghttp.Response {
	params := helpers.GetBodyParams(ctx, req)
	pdbUrl := helpers.GetStringParam(params, "pdb_url")
	pdbName := helpers.GetStringParam(params, "pdb_name")
	fileUrl := helpers.GetStringParam(params, "file_url")
	serial := helpers.GetStringParam(params, "serial")
	name := helpers.GetStringParam(params, "name")
	config := helpers.GetMapParam(params, "config")
	indexList := helpers.GetIntSliceParam(params, "index_list")
	designMode := helpers.GetStringParam(params, "design_mode")

	name = getTaskName(name)
	err := checkTaskCommonParam(serial, fileUrl, name)
	if err != nil {
		return helpers.NewException(ctx, "PARAM_VALUE_ERR", err.Error())
	}
	if len(pdbUrl) <= 0 || len(pdbUrl) > MaxFileUrlLimit {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "pdb_url param error")
	}
	if len(pdbName) <= 0 || len(pdbName) > MaxTaskNameLimit {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "pdb_name param error")
	}
	if len(serial) <= 0 {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "serial param error")
	}
	if designMode == "" {
		designMode = designModeEfficient
	}
	designMolNum, ok := helixVSSynDesignMode[designMode]
	if !ok {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "design_mode param error")
	}

	// 计算费用
	cost := calculateHelixVSSynCost(float64(designMolNum / 10000))
	// 校验余额是否充足，包括helix平台代金券、billing余额/代金券
	if err := checkUserBalance(ctx, cost, models.TaskTypeHelixVSSyn); err != nil {
		return helpers.FailReturn(ctx, helpers.AccountErrorCode, err.Error())
	}

	// temporary handling pdb file
	if strings.HasSuffix(pdbUrl, "qt") {
		pdbUrl = pdbUrl[:len(pdbUrl)-2]
	}
	bucket, object := helpers.DealBosFileUrl(pdbUrl)
	if bucket != "paddle-helix" {
		content, err := bce.GetObject(bucket, object)
		if err != nil {
			return helpers.FailReturn(ctx, helpers.ParamErrorCode, "pdb file load failure")
		}
		err = bceaa.PutObject(object, content)
		if err != nil {
			return helpers.FailReturn(ctx, helpers.ParamErrorCode, "pdb file store failure")
		}
		pdbUrl = strings.Replace(pdbUrl, bucket, "paddle-helix", 1)
	}

	// config 检验
	needConfig, err := getPointConfig(config)
	if err != nil {
		return helpers.NewException(ctx, "PARAM_VALUE_ERR", err.Error())
	}

	needConfig.PdbURL = pdbUrl
	needConfig.PdbName = pdbName
	needConfig.Serial = serial
	needConfig.IndexList = indexList
	needConfig.DesignMolNum = int64(designMolNum)
	needConfig.Cost = cost

	taskNew, err := createHelixVSSynTask(ctx, &needConfig, name)
	if err != nil {
		returnTrialTime(ctx)
		return helpers.NewException(ctx, err.Error())
	}

	// 提交任务给调度
	if err := services.PreSubmitTaskChpc(ctx, *taskNew); err != nil {
		taskNew.Status = int64(models.TaskStatusFailed)
		taskNew.JobFailReason = err.Error()
		_ = taskNew.Save(ctx)
		return helpers.FailReturn(ctx, helpers.SubmitErrorCode, err.Error())
	}
	result := map[string]any{
		"task_id": taskNew.ID,
	}
	return helpers.SuccReturn(ctx, result)
}

func createHelixVSSynTask(ctx context.Context, taskConf *models.TaskVirtualConf, taskName string) (
	*models.Task, error) {
	userType := ctxutils.GetUserInfo(ctx).Type
	userID := ctxutils.GetUserID(ctx)
	db := resource.Gorm.WithContext(ctx)
	// 开启事务
	tx := db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()
	// 查询代金券
	now := time.Now().Truncate(time.Second)
	var coupons []*models.Coupon
	if err := tx.Model(&models.Coupon{}).Where("user_id = ?", userID).
		Where("task_type = ?", models.TaskTypeHelixVSSyn).
		Where("status <> ?", models.StatusDel).
		Where("status <> ?", models.CouponStatusLose).
		Where("start_time <= ?", now).
		Where("end_time >= ?", now).
		Order(clause.OrderByColumn{Column: clause.Column{Name: "rest_amount"}}).
		Find(&coupons).Error; err != nil {
		tx.Rollback()
		helpers.DBLogError(ctx, err)
		return nil, err
	}
	maxCouponRetry := 3
	cost := taskConf.Cost
	var freezeCouponsByte []byte
	if userType == int64(models.UserTypeTry) {
		// 遍历代金券
		freezeCoupons := &models.FreezeCoupons{}
		for _, coupon := range coupons {
			if cost == 0 {
				break
			}
			if coupon.RestAmount <= 0 || coupon.Amount < coupon.RestAmount {
				continue
			}
			// 最多重试三次
			retry := 0
			for {
				if retry > maxCouponRetry {
					tx.Rollback()
					return nil, errors.New("请求频繁，请稍候再试")
				}
				// 再次查询代金券，防止并发扣减导致金额错误
				if err := tx.Model(&models.Coupon{}).Where("id = ?", coupon.ID).First(coupon).Error; err != nil {
					tx.Rollback()
					helpers.DBLogError(ctx, err)
					return nil, err
				}
				// 扣减代金券
				useCoupon := 0.0
				if coupon.RestAmount >= cost {
					useCoupon = cost
				} else {
					useCoupon = coupon.RestAmount
				}
				// 尝试更新代金券
				couponTx := tx.Model(coupon).Where("rest_amount >= ?", useCoupon).
					Update("rest_amount", gorm.Expr(fmt.Sprintf("rest_amount - %f", useCoupon)))
				if couponTx.Error != nil {
					tx.Rollback()
					helpers.DBLogError(ctx, couponTx.Error)
					return nil, couponTx.Error
				}
				// 说明已经被其他进程更新，重试
				if couponTx.RowsAffected == 0 {
					retry++
					continue
				}
				freezeCoupon := &models.FreezeCoupon{}
				freezeCoupon.CouponID = coupon.ID
				freezeCoupon.Amount = useCoupon
				freezeCoupons.Coupons = append(freezeCoupons.Coupons, freezeCoupon)
				cost -= useCoupon
				break
			}
		}
		freezeCouponsByte, _ = json.Marshal(freezeCoupons)
	}
	// 插入任务
	taskConf.BillingUnitCount = cost / models.HelixCPUBillingUnitPrice
	configByte, _ := json.Marshal(taskConf)
	taskN := models.Task{
		Name:     taskName,
		UserId:   uint64(getUserId(ctx)),
		Type:     uint64(models.TaskTypeHelixVSSyn),
		FuncType: uint64(models.FuncTypeForecast),
		Config:   string(configByte),
		Balance:  cost,
		Coupons:  string(freezeCouponsByte),
	}
	if err := tx.Model(&taskN).Create(&taskN).Error; err != nil {
		tx.Rollback()
		helpers.DBLogError(ctx, err)
		return nil, err
	}
	// 提交事务
	if err := tx.Commit().Error; err != nil {
		tx.Rollback()
		helpers.DBLogError(ctx, err)
		return nil, err
	}
	return &taskN, nil
}

func calculateHelixVSSynCost(amount float64) float64 {
	return roundToTwoSignificantFigures(13.68 * 1.3 * math.Pow(amount, 0.9) * 2.04 / 0.5)
}

func SubmitMMGBSA(ctx context.Context, req ghttp.Request) ghttp.Response {
	params := helpers.GetBodyParams(ctx, req)
	name := helpers.GetStringParam(params, "name")
	proteinUrl := helpers.GetStringParam(params, "protein_url")
	molUrl := helpers.GetStringParam(params, "mol_url")
	molName := helpers.GetStringParam(params, "mol_name")
	gbModel := helpers.GetIntParam(params, "gb_model", 0)
	simulationLen := helpers.GetIntParam(params, "simulation_len", 0)
	epsilon := helpers.GetIntParam(params, "epsilon", 0)

	name = getTaskName(name)
	if len(proteinUrl) <= 0 || len(proteinUrl) > MaxFileUrlLimit {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "pdb_url param error")
	}
	if len(molUrl) <= 0 || len(molUrl) > MaxFileUrlLimit {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "mol_url param error")
	}
	if len(molName) > MaxTaskNameLimit {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "mol_name param error")
	}
	if gbModel < 1 || gbModel > 8 {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "gb_model param error")
	}
	if simulationLen < 1 || simulationLen > 10 {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "simulation_len param error")
	}
	if epsilon < 1 || epsilon > 10 {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "epsilon param error")
	}

	// config 检验
	needConfig := models.TaskMMGBSAConf{
		ProteinUrl:    proteinUrl,
		MolUrl:        molUrl,
		MolName:       molName,
		GbModel:       gbModel,
		SimulationLen: simulationLen,
		Epsilon:       epsilon,
	}

	// 数据组装
	configByte, _ := json.Marshal(needConfig)
	taskN := models.Task{
		Name:     name,
		UserId:   uint64(getUserId(ctx)),
		Type:     uint64(models.TaskTypeMMGBSA),
		FuncType: uint64(models.FuncTypeForecast),
		Config:   string(configByte),
	}
	taskNew, err := taskN.Add(ctx, taskN)
	if err != nil {
		return helpers.NewException(ctx, "DB_ERR")
	}

	// 提交任务给调度
	services.PreSubmitTask(ctx, taskNew)
	result := map[string]any{
		"task_id": taskNew.ID,
	}
	return helpers.SuccReturn(ctx, result)
}

func SubmitHelixDock(ctx context.Context, req ghttp.Request) ghttp.Response {
	params := helpers.GetBodyParams(ctx, req)
	pdbUrl := helpers.GetStringParam(params, "pdb_url")
	pdbName := helpers.GetStringParam(params, "pdb_name")
	fileUrl := helpers.GetStringParam(params, "file_url")
	serial := helpers.GetStringParam(params, "serial")
	name := helpers.GetStringParam(params, "name")
	config := helpers.GetMapParam(params, "config")

	name = getTaskName(name)
	err := checkTaskCommonParam(serial, fileUrl, name)
	if err != nil {
		return helpers.NewException(ctx, "PARAM_VALUE_ERR", err.Error())
	}
	if len(pdbUrl) <= 0 || len(pdbUrl) > MaxFileUrlLimit {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "pdb_url param error")
	}
	if len(pdbName) <= 0 || len(pdbName) > MaxTaskNameLimit {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "pdb_name param error")
	}

	// config 检验
	conf, contentStr, err := getCommonTaskConf(serial, fileUrl)
	if err != nil {
		return helpers.NewException(ctx, "PARAM_VALUE_ERR", err.Error())
	}
	needConfig, err := getPointConfig(config)
	if err != nil {
		return helpers.NewException(ctx, "PARAM_VALUE_ERR", err.Error())
	}
	needConfig.PdbURL = pdbUrl
	needConfig.PdbName = pdbName
	needConfig.Serial = conf.Serial
	needConfig.FileUrl = conf.FileUrl

	// 数据组装
	configByte, _ := json.Marshal(needConfig)
	taskN := models.Task{
		Name:      name,
		UserId:    uint64(getUserId(ctx)),
		Type:      uint64(models.TaskTypeHelixDock),
		FuncType:  uint64(models.FuncTypeForecast),
		Config:    string(configByte),
		IsExample: uint64(checkSerialExample(ctx, int64(models.TaskTypeHelixDock), contentStr)),
	}
	taskNew, err := taskN.Add(ctx, taskN)
	if err != nil {
		return helpers.NewException(ctx, "DB_ERR")
	}

	// 提交任务给调度
	services.PreSubmitTask(ctx, taskNew)
	result := map[string]any{
		"task_id": taskNew.ID,
	}
	return helpers.SuccReturn(ctx, result)
}

// helixVS
func SubmitHelixVS(ctx context.Context, req ghttp.Request) ghttp.Response {
	params := helpers.GetBodyParams(ctx, req)
	pdbUrl := helpers.GetStringParam(params, "pdb_path")
	pdbName := helpers.GetStringParam(params, "pdb_name")
	name := helpers.GetStringParam(params, "name")
	molNum := helpers.GetIntParam(params, "mol_num", 0)
	filterStr := helpers.GetStringParam(params, "filter_str")
	filterLib := helpers.GetStringParam(params, "input_ligand_library")
	config := helpers.GetMapParam(params, "config")
	customLigPath := helpers.GetStringParam(params, "custom_lig_path")

	name = getTaskName(name)
	if len(name) > MaxTaskNameLimit {
		returnTrialTime(ctx)
		return helpers.FailReturn(ctx, helpers.ParamErrorCode,
			fmt.Sprintf("name is invalid. The param name should not exceed %d characters in length", MaxTaskNameLimit))
	}
	if len(pdbUrl) <= 0 || len(pdbUrl) > MaxFileUrlLimit {
		returnTrialTime(ctx)
		return helpers.FailReturn(ctx, helpers.ParamErrorCode,
			fmt.Sprintf("pdb_path is valid, length out of range [%d, %d]", 1, MaxFileUrlLimit))
	}
	if len([]rune(pdbName)) <= 0 || len([]rune(pdbName)) > MaxTaskNameLimit {
		returnTrialTime(ctx)
		return helpers.FailReturn(ctx, helpers.ParamErrorCode,
			fmt.Sprintf("pdb_name is valid, length out of range [%d, %d]", 1, MaxTaskNameLimit))
	}

	amount := 0.0
	if len(customLigPath) == 0 {
		returnTrialTime(ctx)
		amount = helixVSMolecularLibraryAmount[filterLib]
		if amount == 0 {
			return helpers.FailReturn(ctx, helpers.ParamErrorCode,
				fmt.Sprintf("input_ligand_library %s is invalid", filterLib))
		}
	}

	// helixVsCnt := services.GetTaskCount(ctx, models.TaskTypeVirtualVS)
	// if helixVsCnt >= models.MaxHelixVSTaskDoingCount {
	// 	return helpers.FailReturn(ctx, helpers.TooManyTask,
	// 		fmt.Sprintf("The running task count exceeds the limit %d", models.MaxHelixVSTaskDoingCount))
	// }

	// temporary handling pdb file
	if strings.HasSuffix(pdbUrl, "qt") {
		pdbUrl = pdbUrl[:len(pdbUrl)-2]
	}
	bucket, object := helpers.DealBosFileUrl(pdbUrl)
	if bucket != "paddle-helix" {
		content, err := bce.GetObject(bucket, object)
		if err != nil {
			return helpers.FailReturn(ctx, helpers.ParamErrorCode, "pdb file load failure")
		}
		err = bceaa.PutObject(object, content)
		if err != nil {
			return helpers.FailReturn(ctx, helpers.ParamErrorCode, "pdb file store failure")
		}
		pdbUrl = strings.Replace(pdbUrl, bucket, "paddle-helix", 1)
	}

	// check chpc service open or not
	open := chpc.IsOpenCHPCService(ctxutils.GetIAMUserDomainID(ctx))
	if !open {
		return helpers.FailReturn(ctx, helpers.AccountErrorCode, "chpc service is not open")
	}

	// 费用计算
	cost := calculateHelixVSCost(amount)
	billingUnitCount := cost / models.HelixCPUBillingUnitPrice

	// config 检验
	needConfig, err := getPointConfig(config)
	if err != nil {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, err.Error())
	}
	needConfig.PdbURL = pdbUrl
	needConfig.PdbName = pdbName
	needConfig.MolNum = molNum
	needConfig.FilterStr = filterStr
	needConfig.FilterLib = filterLib
	needConfig.Cost = cost
	needConfig.BillingUnitCount = billingUnitCount

	// 数据组装
	var isSuccess bool
	configByte, _ := json.Marshal(needConfig)
	taskN := models.Task{
		UserId:   uint64(getUserId(ctx)),
		Type:     uint64(models.TaskTypeVirtualVS),
		Name:     name,
		FuncType: uint64(models.FuncTypeForecast),
		Config:   string(configByte),
		Balance:  cost,
	}
	taskNew, err := taskN.Add(ctx, taskN)
	defer func() {
		if !isSuccess {
			taskNew.Status = int64(models.TaskStatusFailed)
			_ = taskNew.Save(ctx)
		}
	}()
	if err != nil {
		return helpers.NewException(ctx, "DB_ERR")
	}

	// 提交任务给调度
	if err = services.PreSubmitTaskChpc(ctx, taskNew); err != nil {
		return helpers.FailReturn(ctx, helpers.SubmitErrorCode, err.Error())
	}

	// 提交成功
	isSuccess = true
	result := map[string]any{
		"task_id": taskNew.ID,
	}
	return helpers.SuccReturn(ctx, result)
}

func calculateHelixVSCost(amount float64) float64 {
	return roundToTwoSignificantFigures(13.68 * 1.3 * math.Pow(amount, 0.9) * 0.65 / 0.5)
}

// 提交预测（分子生成）
func SubmitMolFormation(ctx context.Context, req ghttp.Request) ghttp.Response {
	params := helpers.GetBodyParams(ctx, req)
	typ := helpers.GetIntParam(params, "type", 0)
	name := helpers.GetStringParam(params, "name")
	pdbName := helpers.GetStringParam(params, "pdb_name")
	pdbUrl := helpers.GetStringParam(params, "pdb_url")
	config := helpers.GetMapParam(params, "config")
	pdbName2 := helpers.GetStringParam(params, "pdb_name2")
	pdbUrl2 := helpers.GetStringParam(params, "pdb_url2")
	config2 := helpers.GetMapParam(params, "config2")

	// 参数校验
	name = getTaskName(name)
	if len([]rune(name)) > MaxTaskNameLimit {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "name length limit")
	}
	if typ != int64(models.TaskTypeMolFormation) && typ != int64(models.TaskTypeMolFormPath) {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "type param error")
	}

	// config
	needConfig := models.TaskMolFormationConf{BasedType: models.BasedTypeTarget}
	if len(pdbUrl) == 0 || len(pdbUrl) > MaxFileUrlLimit {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "pdb_url param error")
	}
	if len(pdbName) == 0 || len(pdbName) > MaxFileUrlLimit {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "pdb_name param error")
	}

	tmpConf, err := getPointConfig(config)
	if err != nil {
		return helpers.NewException(ctx, "PARAM_VALUE_ERR", err.Error())
	}
	needConfig.CenterX = tmpConf.CenterX
	needConfig.CenterY = tmpConf.CenterY
	needConfig.CenterZ = tmpConf.CenterZ
	needConfig.SizeX = tmpConf.SizeX
	needConfig.SizeY = tmpConf.SizeY
	needConfig.SizeZ = tmpConf.SizeZ
	needConfig.PocketSource = tmpConf.PocketSource
	needConfig.ProteinType = tmpConf.ProteinType
	needConfig.PdbName = pdbName
	needConfig.PdbURL = pdbUrl

	// 支持双靶点
	if len(pdbUrl2) > 0 {
		if len(pdbUrl2) > MaxFileUrlLimit {
			return helpers.FailReturn(ctx, helpers.ParamErrorCode, "pdb_url2 param error")
		}
		if len(pdbName2) == 0 || len(pdbName2) > MaxFileUrlLimit {
			return helpers.FailReturn(ctx, helpers.ParamErrorCode, "pdb_name2 param error")
		}

		tmpConf, err := getPointConfig(config2)
		if err != nil {
			return helpers.NewException(ctx, "PARAM_VALUE_ERR", err.Error())
		}
		needConfig.CenterX2 = tmpConf.CenterX
		needConfig.CenterY2 = tmpConf.CenterY
		needConfig.CenterZ2 = tmpConf.CenterZ
		needConfig.SizeX2 = tmpConf.SizeX
		needConfig.SizeY2 = tmpConf.SizeY
		needConfig.SizeZ2 = tmpConf.SizeZ
		needConfig.PocketSource2 = tmpConf.PocketSource
		needConfig.ProteinType2 = tmpConf.ProteinType
		needConfig.PdbName2 = pdbName2
		needConfig.PdbUrl2 = pdbUrl2
	}

	// 数据组装
	configByte, _ := json.Marshal(needConfig)
	taskN := models.Task{
		UserId:   uint64(getUserId(ctx)),
		Type:     uint64(typ),
		Name:     name,
		FuncType: uint64(models.FuncTypeForecast),
		Config:   string(configByte),
	}
	taskNew, err := taskN.Add(ctx, taskN)
	if err != nil {
		return helpers.NewException(ctx, "DB_ERR")
	}

	// 提交任务给调度
	services.PreSubmitTask(ctx, taskNew)
	result := map[string]any{
		"task_id": taskNew.ID,
	}
	return helpers.SuccReturn(ctx, result)
}

// 提交预测（分子对接）
func SubmitMolDocking(ctx context.Context, req ghttp.Request) ghttp.Response {
	params := helpers.GetBodyParams(ctx, req)
	pdbUrl := helpers.GetStringParam(params, "pdb_url")
	pdbName := helpers.GetStringParam(params, "pdb_name")
	fileUrl := helpers.GetStringParam(params, "file_url")
	serial := helpers.GetStringParam(params, "serial")
	name := helpers.GetStringParam(params, "name")
	config := helpers.GetMapParam(params, "config")

	name = getTaskName(name)
	err := checkTaskCommonParam(serial, fileUrl, name)
	if err != nil {
		return helpers.NewException(ctx, "PARAM_VALUE_ERR", err.Error())
	}
	if len(pdbUrl) <= 0 || len(pdbUrl) > MaxFileUrlLimit {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "pdb_url param error")
	}
	if len(pdbName) <= 0 || len(pdbName) > MaxTaskNameLimit {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "pdb_name param error")
	}

	// config 检验
	conf, contentStr, err := getCommonTaskConf(serial, fileUrl)
	if err != nil {
		return helpers.NewException(ctx, "PARAM_VALUE_ERR", err.Error())
	}
	needConfig, err := getPointConfig(config)
	if err != nil {
		return helpers.NewException(ctx, "PARAM_VALUE_ERR", err.Error())
	}
	needConfig.PdbURL = pdbUrl
	needConfig.PdbName = pdbName
	needConfig.Serial = conf.Serial
	needConfig.FileUrl = conf.FileUrl

	// 数据组装
	configByte, _ := json.Marshal(needConfig)
	taskN := models.Task{
		Name:      name,
		UserId:    uint64(getUserId(ctx)),
		Type:      uint64(models.TaskTypeVirtualFilter),
		FuncType:  uint64(models.FuncTypeForecast),
		Config:    string(configByte),
		IsExample: uint64(checkSerialExample(ctx, int64(models.TaskTypeVirtualFilter), contentStr)),
	}
	taskNew, err := taskN.Add(ctx, taskN)
	if err != nil {
		return helpers.NewException(ctx, "DB_ERR")
	}

	// 提交任务给调度
	services.PreSubmitTask(ctx, taskNew)
	result := map[string]any{
		"task_id": taskNew.ID,
	}
	return helpers.SuccReturn(ctx, result)
}

// 提交预测（分子活性-结构）
func SubmitMolActStruct(ctx context.Context, req ghttp.Request) ghttp.Response {
	params := helpers.GetBodyParams(ctx, req)
	proteinUrl := helpers.GetStringParam(params, "protein_url")
	molUrl := helpers.GetStringParam(params, "mol_url")
	name := helpers.GetStringParam(params, "name")

	// name校验
	name = getTaskName(name)
	if len([]rune(name)) > MaxTaskNameLimit {
		return helpers.NewException(ctx, "PARAM_VALUE_ERR", "name length limit")
	}
	err := checkFileContent(getFileContent(proteinUrl))
	if err != nil {
		return helpers.NewException(ctx, "PARAM_VALUE_ERR", err.Error())
	}
	err = checkFileContent(getFileContent(molUrl))
	if err != nil {
		return helpers.NewException(ctx, "PARAM_VALUE_ERR", err.Error())
	}

	// config校验
	needConfig := models.TaskMolActStructConf{
		MolUrl:     molUrl,
		ProteinUrl: proteinUrl,
	}

	// 数据组装
	configByte, _ := json.Marshal(needConfig)
	taskN := models.Task{
		Name:     name,
		UserId:   uint64(getUserId(ctx)),
		Type:     uint64(models.TaskTypeMolActStruct),
		FuncType: uint64(models.FuncTypeForecast),
		Config:   string(configByte),
	}
	taskNew, err := taskN.Add(ctx, taskN)
	if err != nil {
		return helpers.NewException(ctx, "DB_ERR")
	}

	// 提交任务给调度
	services.PreSubmitTask(ctx, taskNew)
	result := map[string]any{
		"task_id": taskNew.ID,
	}
	return helpers.SuccReturn(ctx, result)
}

// 提交预测
func SubmitMolActSerial(ctx context.Context, req ghttp.Request) ghttp.Response {
	params := helpers.GetBodyParams(ctx, req)
	fileUrl := helpers.GetStringParam(params, "file_url")
	serial := helpers.GetStringParam(params, "serial")
	name := helpers.GetStringParam(params, "name")
	config := helpers.GetMapParam(params, "config")
	protein := helpers.GetStringParam(params, "protein")
	taskId := helpers.GetIntParam(params, "task_id", 0)

	// 参数校验
	name = getTaskName(name)
	err := checkTaskCommonParam(serial, fileUrl, name)
	if err != nil {
		return helpers.NewException(ctx, "PARAM_VALUE_ERR", err.Error())
	}
	if len(protein) > MaxSerialLimit {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "protein param error")
	}

	// config 校验
	needConfig, contentStr, err := getMolActSerialConf(serial, fileUrl, config)
	if err != nil {
		return helpers.NewException(ctx, "PARAM_VALUE_ERR", err.Error())
	}
	needConfig.Protein = protein

	// 判断自训练数据
	if taskId > 0 {
		modelPath, err := getTrainModelPath(ctx, taskId)
		if err != nil {
			return helpers.NewException(ctx, "PARAM_VALUE_ERR", err.Error())
		}
		needConfig.ModelPath = modelPath
	}

	// 数据组装
	configByte, _ := json.Marshal(needConfig)
	taskN := models.Task{
		Name:      name,
		UserId:    uint64(getUserId(ctx)),
		Type:      uint64(models.TaskTypeMolActivity),
		FuncType:  uint64(models.FuncTypeForecast),
		Config:    string(configByte),
		IsExample: uint64(checkSerialExample(ctx, int64(models.TaskTypeMolActivity), contentStr)),
	}
	if taskId > 0 {
		taskN.ParentID = uint64(taskId)
	}
	taskNew, err := taskN.Add(ctx, taskN)
	if err != nil {
		return helpers.NewException(ctx, "DB_ERR")
	}

	// 提交任务到调度
	services.PreSubmitTask(ctx, taskNew)
	result := map[string]any{
		"task_id": taskNew.ID,
	}
	return helpers.SuccReturn(ctx, result)
}

// 获取虚拟筛选配置
func getPointConfig(config map[string]any) (models.TaskVirtualConf, error) {
	var conf models.TaskVirtualConf

	confByte, err := json.Marshal(config)
	if err != nil {
		return conf, err
	}

	if err := json.Unmarshal(confByte, &conf); err != nil {
		return conf, err
	}

	if conf.PocketSource != models.PocketSourceSearch &&
		conf.PocketSource != models.PocketSourceExample {
		conf.PocketSource = models.PocketSourceUser
	}

	if conf.ProteinType != models.MolProteinTypeTarget &&
		conf.ProteinType != models.MolProteinTypeNotTarget &&
		conf.ProteinType != "" {
		return conf, errors.New("config protein type error")
	}

	if conf.CenterX <= -10000 || conf.CenterX >= 10000 ||
		conf.CenterY <= -10000 || conf.CenterY >= 10000 ||
		conf.CenterZ <= -10000 || conf.CenterZ >= 10000 ||
		conf.SizeX <= -10000 || conf.SizeX >= 10000 ||
		conf.SizeY <= -10000 || conf.SizeY >= 10000 ||
		conf.SizeZ <= -10000 || conf.SizeZ >= 10000 {
		return conf, errors.New("config point error")
	}
	conf.CenterX = helpers.FormatFloat(conf.CenterX, 3)
	conf.CenterY = helpers.FormatFloat(conf.CenterY, 3)
	conf.CenterZ = helpers.FormatFloat(conf.CenterZ, 3)
	conf.SizeX = helpers.FormatFloat(conf.SizeX, 3)
	conf.SizeY = helpers.FormatFloat(conf.SizeY, 3)
	conf.SizeZ = helpers.FormatFloat(conf.SizeZ, 3)

	return conf, nil
}

func getMolActSerialConf(serial, fileUrl string, config map[string]any) (models.TaskMolActivityConf, string, error) {
	var needConfig models.TaskMolActivityConf

	conf, contentStr, err := getCommonTaskConf(serial, fileUrl)
	if err != nil {
		return needConfig, "", err
	}
	needConfig.Serial = conf.Serial
	needConfig.FileUrl = conf.FileUrl

	if len(config) <= 0 {
		return needConfig, contentStr, nil
	}
	useBingdingdb := helpers.GetBoolParam(config, "use_bingdingdb", false)
	if useBingdingdb {
		needConfig.UseBingdingdb = useBingdingdb
	}
	useChembl := helpers.GetBoolParam(config, "use_chembl", false)
	if useChembl {
		needConfig.UseChembl = useChembl
	}

	return needConfig, contentStr, nil
}
