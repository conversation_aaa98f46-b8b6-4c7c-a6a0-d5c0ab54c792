package controllers

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http/httptest"
	"testing"
	"time"

	"icode.baidu.com/baidu/gdp/ghttp"
	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/models"
)

// TestSearchProtein 测试函数，用于搜索蛋白质信息
// 参数：t *testing.T - 类型为*testing.T的指针，表示当前测试用例
// 返回值：无
func TestSearchProtein(t *testing.T) {
	ctx := context.Background()
	ctx = context.WithValue(ctx, "user_info", models.UserInfo{UserID: 1, Type: 1})

	input1 := map[string]interface{}{
		"keyword": "xxs",
	}
	input1Byte, _ := json.Marshal(input1)

	caseList := []struct {
		input  io.Reader
		output int
	}{
		{nil, helpers.SuccessCode},
		{bytes.NewReader(input1Byte), helpers.SuccessCode},
	}

	for _, v := range caseList {
		raw := httptest.NewRequest("POST", "/", v.input)
		req := ghttp.NewRequest(time.Now(), raw)

		resp := SearchProtein(ctx, req).(*ghttp.JSONResponse)
		data := resp.Data.(helpers.JsonResp)
		if data.Code != v.output {
			t.Errorf("code=%d, want=%d", data.Code, v.output)
		}
	}
}

// TestSubmitPretreat 测试函数，用于提交预处理任务
// 参数t *testing.T：表示当前测试用例的指针，用于标识该测试用例是否通过或失败
// 返回值无返回值
func TestSubmitPretreat(t *testing.T) {
	ctx := context.Background()
	ctx = context.WithValue(ctx, "user_info", models.UserInfo{UserID: 1, Type: 1})

	input1 := map[string]interface{}{
		"pdb_url": "bos:/bml-test-test/helix_upload/866fae6a-b842-4b0b-a6b8-7bbb366afdd8.txt",
		"type":    50,
	}
	input1Byte, _ := json.Marshal(input1)

	caseList := []struct {
		input  io.Reader
		output int
	}{
		{nil, helpers.ParamErrorCode},
		{bytes.NewReader(input1Byte), helpers.SuccessCode},
	}

	for _, v := range caseList {
		raw := httptest.NewRequest("POST", "/", v.input)
		req := ghttp.NewRequest(time.Now(), raw)

		resp := SubmitPretreat(ctx, req).(*ghttp.JSONResponse)
		data := resp.Data.(helpers.JsonResp)
		if data.Code != v.output {
			fmt.Println(data)
			t.Errorf("code=%d, want=%d", data.Code, v.output)
		}
	}
}

// TestGetPretreatInfo 测试函数TestGetPretreatInfo，用于获取预处理信息
// 参数t：*testing.T类型，表示测试对象
// 返回值：无
func TestGetPretreatInfo(t *testing.T) {
	ctx := context.Background()
	ctx = context.WithValue(ctx, "user_info", models.UserInfo{UserID: 1, Type: 1})

	input1 := map[string]interface{}{
		"pretreat_id": 12,
	}
	input1Byte, _ := json.Marshal(input1)

	caseList := []struct {
		input  io.Reader
		output int
	}{
		{nil, helpers.ParamErrorCode},
		{bytes.NewReader(input1Byte), helpers.SuccessCode},
	}

	for _, v := range caseList {
		raw := httptest.NewRequest("POST", "/", v.input)
		req := ghttp.NewRequest(time.Now(), raw)

		resp := GetPretreatInfo(ctx, req).(*ghttp.JSONResponse)
		data := resp.Data.(helpers.JsonResp)
		if data.Code != v.output {
			t.Errorf("code=%d, want=%d", data.Code, v.output)
		}
	}
}
