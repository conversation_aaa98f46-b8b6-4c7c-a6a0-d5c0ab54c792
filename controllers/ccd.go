package controllers

import (
	"context"
	"errors"
	"regexp"

	"icode.baidu.com/baidu/gdp/ghttp"
	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/models"
	"icode.baidu.com/helix_web/services"
)

const (
	DefaultPageNum  int64 = 1  // 默认页码
	DefaultPageSize int64 = 20 // 默认页大小
)

type QueryCcdResult struct {
	Items []models.Ccd `json:"items"`
}

// QueryCcd 检索CCD数据库
func QueryCcd(ctx context.Context, req ghttp.Request) ghttp.Response {
	// 获取查询键、分页信息
	params := helpers.GetBodyParams(ctx, req)
	key := helpers.GetStringParam(params, "key")
	pageNum := helpers.GetIntParam(params, "page_num", DefaultPageNum)
	pageSize := helpers.GetIntParam(params, "page_size", DefaultPageSize)

	// 当key不存在时，由于数据记录较多，此处不做全量查询，直接返回空
	result := QueryCcdResult{}
	if key == "" {
		return helpers.SuccReturn(ctx, result)
	}

	// 普通参数校验
	if ok, err := checkCommonParam(ctx, pageNum, pageSize); !ok {
		return helpers.NewException(ctx, "PARAM_VALUE_ERR", err.Error())
	}
	var allowedChars = regexp.MustCompile(`^[a-zA-Z0-9_\p{Han}]+$`)
	if !allowedChars.MatchString(key) {
		return helpers.NewException(ctx, "PARAM_VALUE_ERR", "key 中包含非法字符")
	}

	// 检索CCD列表
	ccdList, err := services.NewCcdService().QueryCcdList(ctx, key, int(pageNum), int(pageSize))
	if err != nil {
		return helpers.FailReturn(ctx, helpers.DBErrorCode, err.Error())
	}
	result.Items = ccdList
	return helpers.SuccReturn(ctx, result)
}

// 检查参数合法性
func checkCommonParam(ctx context.Context, pageNum int64, pageSize int64) (bool, error) {
	if pageNum <= 0 || pageSize < 0 {
		return false, errors.New("page_num or page_size is invalid")
	}
	return true, nil
}
