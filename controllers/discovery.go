package controllers

import (
	"context"
	"encoding/json"
	"errors"
	"icode.baidu.com/helix_web/library/ctxutils"

	"icode.baidu.com/baidu/gdp/ghttp"

	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/models"
	"icode.baidu.com/helix_web/services"
)

// 提交预测
func SubmitKYKT(ctx context.Context, req ghttp.Request) ghttp.Response {
	params := helpers.GetBodyParams(ctx, req)
	name := helpers.GetStringParam(params, "name")
	heavySerial := helpers.GetStringParam(params, "heavy_serial")
	lightSerial := helpers.GetStringParam(params, "light_serial")
	antigenSerial := helpers.GetStringParam(params, "antigen_serial")
	isApi := 0
	if _, ok := ctx.Value("is_api").(int); ok {
		isApi = 1
	}

	// name 校验
	name = getTaskName(name)
	if len([]rune(name)) > MaxTaskNameLimit {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "name length limit")
	}

	// config 获取
	needConfig, err := getTaskAntigenConf("", heavySerial, lightSerial, antigenSerial)
	if err != nil {
		return helpers.NewException(ctx, "PARAM_VALUE_ERR", err.Error())
	}

	// 计费check
	chargeP := chargeParam{IsCharge: false}
	chargeTab, err := checkCharge(ctx, int64(models.TaskTypeKYKT), services.Level1, chargeP)
	if err != nil {
		return helpers.NewException(ctx, "PARAM_VALUE_ERR", err.Error())
	}

	// 数据组装
	configByte, _ := json.Marshal(needConfig)
	taskN := models.Task{
		Name:      name,
		UserId:    uint64(getUserId(ctx)),
		Type:      uint64(models.TaskTypeKYKT),
		FuncType:  uint64(models.FuncTypeForecast),
		Config:    string(configByte),
		ChargeTab: uint64(chargeTab),
		IsExample: uint64(checkSerialExample(ctx, int64(models.TaskTypeKYKT), heavySerial)),
		IsApi:     uint64(isApi),
		IAMUserID: ctxutils.GetIAMUserID(ctx),
	}
	taskNew, err := taskN.Add(ctx, taskN)
	if err != nil {
		return helpers.NewException(ctx, "DB_ERR")
	}

	// 提交任务给调度
	services.PreSubmitTask(ctx, taskNew)
	result := map[string]any{
		"task_id": taskNew.ID,
	}
	return helpers.SuccReturn(ctx, result)
}

// 提交预测
func SubmitAntibody(ctx context.Context, req ghttp.Request) ghttp.Response {
	params := helpers.GetBodyParams(ctx, req)
	name := helpers.GetStringParam(params, "name")
	heavySerial := helpers.GetStringParam(params, "heavy_serial")
	lightSerial := helpers.GetStringParam(params, "light_serial")
	antigenSerial := helpers.GetStringParam(params, "antigen_serial")
	fileUrl := helpers.GetStringParam(params, "file_url")

	// name校验
	name = getTaskName(name)
	if len([]rune(name)) > MaxTaskNameLimit {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "name length limit")
	}

	// config 获取
	needConfig, err := getTaskAntigenConf(fileUrl, heavySerial, lightSerial, antigenSerial)
	if err != nil {
		return helpers.NewException(ctx, "PARAM_VALUE_ERR", err.Error())
	}

	// 数据组装
	configByte, _ := json.Marshal(needConfig)
	taskN := models.Task{
		Name:      name,
		UserId:    uint64(getUserId(ctx)),
		Type:      uint64(models.TaskTypeAntibody),
		FuncType:  uint64(models.FuncTypeForecast),
		Config:    string(configByte),
		IsExample: uint64(checkSerialExample(ctx, int64(models.TaskTypeAntibody), heavySerial)),
	}
	taskNew, err := taskN.Add(ctx, taskN)
	if err != nil {
		return helpers.NewException(ctx, "DB_ERR")
	}

	// 提交任务给调度
	services.PreSubmitTask(ctx, taskNew)
	result := map[string]any{
		"task_id": taskNew.ID,
	}
	return helpers.SuccReturn(ctx, result)
}

// 新冠抗体
func SubmitCovid(ctx context.Context, req ghttp.Request) ghttp.Response {
	params := helpers.GetBodyParams(ctx, req)
	name := helpers.GetStringParam(params, "name")
	heavySerial := helpers.GetStringParam(params, "heavy_serial")
	lightSerial := helpers.GetStringParam(params, "light_serial")
	fileUrl := helpers.GetStringParam(params, "file_url")

	// name校验
	name = getTaskName(name)
	if len([]rune(name)) > MaxTaskNameLimit {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "name length limit")
	}

	// config校验
	needConfig, err := getTaskAntigenConf(fileUrl, heavySerial, lightSerial, "")
	if err != nil {
		return helpers.NewException(ctx, "PARAM_VALUE_ERR", err.Error())
	}

	// 数据组装
	configByte, _ := json.Marshal(needConfig)
	taskN := models.Task{
		Name:      name,
		UserId:    uint64(getUserId(ctx)),
		Type:      uint64(models.TaskTypeAntibody),
		FuncType:  uint64(models.FuncTypeForecast),
		Config:    string(configByte),
		IsExample: uint64(checkSerialExample(ctx, int64(models.TaskTypeAntibody), heavySerial)),
	}
	taskNew, err := taskN.Add(ctx, taskN)
	if err != nil {
		return helpers.NewException(ctx, "DB_ERR")
	}

	// 提交任务给调度
	services.PreSubmitTask(ctx, taskNew)
	result := map[string]any{
		"task_id": taskNew.ID,
	}
	return helpers.SuccReturn(ctx, result)
}

// 提交预测(突变)
func SubmitProteinMutation(ctx context.Context, req ghttp.Request) ghttp.Response {
	params := helpers.GetBodyParams(ctx, req)
	name := helpers.GetStringParam(params, "name")
	protein1 := helpers.GetStringParam(params, "protein1")
	protein2 := helpers.GetStringParam(params, "protein2")
	point1 := helpers.GetStringSliceParam(params, "point1")
	point2 := helpers.GetStringSliceParam(params, "point2")
	fileUrl := helpers.GetStringParam(params, "file_url")

	// param check
	name = getTaskName(name)
	if len([]rune(name)) > MaxTaskNameLimit {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "name length limit")
	}

	// config check
	needConfig, err := getProteinMutationConf(fileUrl, protein1, protein2, point1, point2)
	if err != nil {
		return helpers.NewException(ctx, "PARAM_VALUE_ERR", err.Error())
	}

	// 数据组装
	configByte, _ := json.Marshal(needConfig)
	taskN := models.Task{
		Name:     name,
		UserId:   uint64(getUserId(ctx)),
		Type:     uint64(models.TaskTypeProteinMutation),
		FuncType: uint64(models.FuncTypeForecast),
		Config:   string(configByte),
	}
	taskNew, err := taskN.Add(ctx, taskN)
	if err != nil {
		return helpers.NewException(ctx, "DB_ERR")
	}

	// 提交任务给调度
	services.PreSubmitTask(ctx, taskNew)
	result := map[string]any{
		"task_id": taskNew.ID,
	}
	return helpers.SuccReturn(ctx, result)
}

// 提交预测（逆合成路线）
func SubmitCompound(ctx context.Context, req ghttp.Request) ghttp.Response {
	params := helpers.GetBodyParams(ctx, req)
	serial := helpers.GetStringParam(params, "serial")
	fileUrl := helpers.GetStringParam(params, "file_url")
	name := helpers.GetStringParam(params, "name")

	// 参数校验
	name = getTaskName(name)
	err := checkTaskCommonParam(serial, fileUrl, name)
	if err != nil {
		return helpers.NewException(ctx, "PARAM_VALUE_ERR", err.Error())
	}

	// config check
	needConfig, _, err := getCommonTaskConf(serial, fileUrl)
	if err != nil {
		return helpers.NewException(ctx, "PARAM_VALUE_ERR", err.Error())
	}

	// 数据组装
	configByte, _ := json.Marshal(needConfig)
	taskN := models.Task{
		UserId:   uint64(getUserId(ctx)),
		Type:     uint64(models.TaskTypeCompound),
		Name:     name,
		FuncType: uint64(models.FuncTypeForecast),
		Config:   string(configByte),
	}
	taskNew, err := taskN.Add(ctx, taskN)
	if err != nil {
		return helpers.NewException(ctx, "DB_ERR")
	}

	// 提交任务给调度
	services.PreSubmitTask(ctx, taskNew)
	result := map[string]any{
		"task_id": taskNew.ID,
	}
	return helpers.SuccReturn(ctx, result)
}

// 处理抗体配置
func getTaskAntigenConf(fileUrl, heavySerial, lightSerial, antigenSerial string) (models.TaskAntigenConf, error) {
	var needConfig models.TaskAntigenConf
	if len(fileUrl) > 0 {
		err := checkFileContent(getFileContent(fileUrl))
		if err != nil {
			return needConfig, err
		}

		needConfig.FileUrl = fileUrl
	} else {
		if len(heavySerial) > MaxSerialLimit {
			return needConfig, errors.New("heavy_serial length limit")
		}
		if len(lightSerial) > MaxSerialLimit {
			return needConfig, errors.New("light_serial length limit")
		}
		if len(antigenSerial) > MaxSerialLimit {
			return needConfig, errors.New("antigen_serial length limit")
		}

		needConfig.HeavySerial = heavySerial
		needConfig.LightSerial = lightSerial
		needConfig.AntigenSerial = antigenSerial
	}

	return needConfig, nil
}

func getProteinMutationConf(fileUrl, protein1, protein2 string, point1, point2 []string) (models.TaskProteinMutationConf, error) {
	var needConfig models.TaskProteinMutationConf
	if len(fileUrl) > 0 {
		err := checkFileContent(getFileContent(fileUrl))
		if err != nil {
			return needConfig, err
		}

		needConfig.FileUrl = fileUrl
	} else {
		if len(protein1) > MaxSerialLimit {
			return needConfig, errors.New("protein1 length limit")
		}
		if len(protein2) > MaxSerialLimit {
			return needConfig, errors.New("protein2 length limit")
		}

		pointLen := len(point1) + len(point2)
		if pointLen <= 0 || pointLen > MaxPointLimit {
			return needConfig, errors.New("point params error")
		}

		var pointStr1, pointStr2 string
		if len(point1) > 0 {
			point1Byte, _ := json.Marshal(point1)
			pointStr1 = string(point1Byte)
		}
		if len(point2) > 0 {
			point2Byte, _ := json.Marshal(point2)
			pointStr2 = string(point2Byte)
		}
		needConfig.Protein1 = protein1
		needConfig.Protein2 = protein2
		needConfig.Point1 = pointStr1
		needConfig.Point2 = pointStr2
	}

	return needConfig, nil
}
