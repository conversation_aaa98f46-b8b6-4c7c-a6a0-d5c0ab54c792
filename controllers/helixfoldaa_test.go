package controllers

import (
	"bytes"
	"context"
	"encoding/json"
	"net/http/httptest"
	"os"
	"strings"
	"testing"
	"time"

	"icode.baidu.com/baidu/gdp/ghttp"
	"icode.baidu.com/helix_web/models"
	pb "icode.baidu.com/helix_web/proto/generate/proto"
)

// Mock 服务接口
type MockCcdService struct {
	QueryCcdListFunc func(ctx context.Context, key string, pageNum int, pageSize int) ([]models.Ccd, error)
}

func (m *MockCcdService) QueryCcdList(ctx context.Context, key string, pageNum int, pageSize int) ([]models.Ccd, error) {
	if m.QueryCcdListFunc != nil {
		return m.QueryCcdListFunc(ctx, key, pageNum, pageSize)
	}
	return []models.Ccd{}, nil
}

type MockSerialServiceClient struct {
	GetEntitiesTokenCountFunc func(ctx context.Context, req *pb.GetEntitiesTokenCountRequest) (*pb.GetEntitiesTokenCountResponse, error)
}

func (m *MockSerialServiceClient) GetEntitiesTokenCount(ctx context.Context, req *pb.GetEntitiesTokenCountRequest) (*pb.GetEntitiesTokenCountResponse, error) {
	if m.GetEntitiesTokenCountFunc != nil {
		return m.GetEntitiesTokenCountFunc(ctx, req)
	}
	return &pb.GetEntitiesTokenCountResponse{Count: 100}, nil
}

// Mock 函数变量，用于替换真实的服务调用
var (
	mockCcdServiceQueryCcdList     func(ctx context.Context, key string, pageNum int, pageSize int) ([]models.Ccd, error)
	mockSerialServiceGetTokenCount func(ctx context.Context, req *pb.GetEntitiesTokenCountRequest) (*pb.GetEntitiesTokenCountResponse, error)
	mockChpcIsOpenService          func(iamUserDomainID string) bool
	mockChpcQueryArrears           func(ctx context.Context, accountID string) (bool, error)
	mockRedisIncrBy                func(ctx context.Context, key string, value int64) bool
	mockRedisGet                   func(ctx context.Context, key string) string
	mockRedisSet                   func(ctx context.Context, key string, value interface{}, expire int) bool
	mockPreSubmitTaskChpc          func(ctx context.Context, task models.Task) error
)

// 保存原始函数引用
var (
	originalCcdServiceQueryCcdList     func(ctx context.Context, key string, pageNum int, pageSize int) ([]models.Ccd, error)
	originalSerialServiceGetTokenCount func(ctx context.Context, req *pb.GetEntitiesTokenCountRequest) (*pb.GetEntitiesTokenCountResponse, error)
	originalChpcIsOpenService          func(iamUserDomainID string) bool
	originalChpcQueryArrears           func(ctx context.Context, accountID string) (bool, error)
	originalRedisIncrBy                func(ctx context.Context, key string, value int64) bool
	originalRedisGet                   func(ctx context.Context, key string) string
	originalRedisSet                   func(ctx context.Context, key string, value interface{}, expire int) bool
	originalPreSubmitTaskChpc          func(ctx context.Context, task models.Task) error
)

// 设置 mock
func setupMocks() {
	// 这里需要在实际项目中替换真实的服务调用
	// 由于无法直接替换包级别的函数，这里提供 mock 的思路
	// 在实际项目中，可以通过依赖注入或接口来实现 mock
}

// 恢复原始函数
func restoreMocks() {
	// 恢复原始函数调用
}

func TestMain(m *testing.M) {
	setupMocks()
	code := m.Run()
	restoreMocks()
	os.Exit(code)
}

func TestBatchSubmitHelixFold3(t *testing.T) {
	ctx := context.Background()
	ctx = context.WithValue(ctx, "user_info", models.UserInfo{UserID: 1, Type: 1})

	t.Run("正常提交", func(t *testing.T) {
		// mock 依赖
		setupMocks()
		defer restoreMocks()
		// 构造测试数据
		input, _ := json.Marshal(map[string]interface{}{
			"tasks": []map[string]interface{}{
				{
					"name":       "test_task",
					"recycle":    10,
					"ensemble":   1,
					"model_type": "HelixFold3",
					"entities": []map[string]interface{}{
						{
							"type":     "protein",
							"sequence": "MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG",
							"count":    1,
						},
					},
				},
			},
		})
		req := httptest.NewRequest("POST", "/api/batch_submit_helixfold3", bytes.NewBuffer(input))
		req.Header.Set("Content-Type", "application/json")
		ghttpReq := ghttp.NewRequest(time.Now(), req)
		resp := BatchSubmitHelixFold3(ctx, ghttpReq)
		if resp == nil {
			t.Fatal("BatchSubmitHelixFold3 返回值为 nil")
		}
		// 可根据实际返回结构进一步断言
	})

	t.Run("空任务列表", func(t *testing.T) {
		setupMocks()
		defer restoreMocks()
		input, _ := json.Marshal(map[string]interface{}{
			"tasks": []map[string]interface{}{},
		})
		req := httptest.NewRequest("POST", "/api/batch_submit_helixfold3", bytes.NewBuffer(input))
		req.Header.Set("Content-Type", "application/json")
		ghttpReq := ghttp.NewRequest(time.Now(), req)
		resp := BatchSubmitHelixFold3(ctx, ghttpReq)
		if resp == nil {
			t.Fatal("空任务列表应返回错误响应")
		}
		// 可断言 resp 中的错误码和错误信息
	})

	// 可继续补充：token 超限、余额不足、参数非法等子测试
}

func TestCheckHelixFold3RefStructure(t *testing.T) {
	ctx := context.Background()
	ctx = context.WithValue(ctx, "user_info", models.UserInfo{UserID: 1, Type: 1})

	t.Run("正常参考结构检查", func(t *testing.T) {
		setupMocks()
		defer restoreMocks()

		input, _ := json.Marshal(map[string]interface{}{
			"tasks": []map[string]interface{}{
				{
					"name":       "test_ref_check",
					"recycle":    10,
					"ensemble":   1,
					"model_type": "HelixFold3",
					"entities": []map[string]interface{}{
						{
							"type":     "protein",
							"sequence": "MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG",
							"count":    1,
						},
					},
					"ref_structures": []map[string]interface{}{
						{
							"ref_file": "paddle-helix/test_ref.pdb",
							"refer_target_pairs": []map[string]interface{}{
								{
									"refer":  "A",
									"target": "1-1",
								},
							},
						},
					},
				},
			},
		})

		req := httptest.NewRequest("POST", "/api/check_helixfold3_ref_structure", bytes.NewBuffer(input))
		req.Header.Set("Content-Type", "application/json")
		ghttpReq := ghttp.NewRequest(time.Now(), req)

		resp := CheckHelixFold3RefStructure(ctx, ghttpReq)
		if resp == nil {
			t.Fatal("CheckHelixFold3RefStructure 返回值为 nil")
		}
	})

	t.Run("空任务列表", func(t *testing.T) {
		setupMocks()
		defer restoreMocks()

		input, _ := json.Marshal(map[string]interface{}{
			"tasks": []map[string]interface{}{},
		})

		req := httptest.NewRequest("POST", "/api/check_helixfold3_ref_structure", bytes.NewBuffer(input))
		req.Header.Set("Content-Type", "application/json")
		ghttpReq := ghttp.NewRequest(time.Now(), req)

		resp := CheckHelixFold3RefStructure(ctx, ghttpReq)
		if resp == nil {
			t.Fatal("空任务列表应返回错误响应")
		}
	})

	t.Run("无效参考文件", func(t *testing.T) {
		setupMocks()
		defer restoreMocks()

		input, _ := json.Marshal(map[string]interface{}{
			"tasks": []map[string]interface{}{
				{
					"name":       "test_invalid_ref",
					"recycle":    10,
					"ensemble":   1,
					"model_type": "HelixFold3",
					"entities": []map[string]interface{}{
						{
							"type":     "protein",
							"sequence": "MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG",
							"count":    1,
						},
					},
					"ref_structures": []map[string]interface{}{
						{
							"ref_file": "", // 空文件路径
							"refer_target_pairs": []map[string]interface{}{
								{
									"refer":  "A",
									"target": "1-1",
								},
							},
						},
					},
				},
			},
		})

		req := httptest.NewRequest("POST", "/api/check_helixfold3_ref_structure", bytes.NewBuffer(input))
		req.Header.Set("Content-Type", "application/json")
		ghttpReq := ghttp.NewRequest(time.Now(), req)

		resp := CheckHelixFold3RefStructure(ctx, ghttpReq)
		if resp == nil {
			t.Fatal("无效参考文件应返回错误响应")
		}
	})

	t.Run("无效链名", func(t *testing.T) {
		setupMocks()
		defer restoreMocks()

		input, _ := json.Marshal(map[string]interface{}{
			"tasks": []map[string]interface{}{
				{
					"name":       "test_invalid_chain",
					"recycle":    10,
					"ensemble":   1,
					"model_type": "HelixFold3",
					"entities": []map[string]interface{}{
						{
							"type":     "protein",
							"sequence": "MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG",
							"count":    1,
						},
					},
					"ref_structures": []map[string]interface{}{
						{
							"ref_file": "paddle-helix/test_ref.pdb",
							"refer_target_pairs": []map[string]interface{}{
								{
									"refer":  "A@", // 包含特殊字符
									"target": "1-1",
								},
							},
						},
					},
				},
			},
		})

		req := httptest.NewRequest("POST", "/api/check_helixfold3_ref_structure", bytes.NewBuffer(input))
		req.Header.Set("Content-Type", "application/json")
		ghttpReq := ghttp.NewRequest(time.Now(), req)

		resp := CheckHelixFold3RefStructure(ctx, ghttpReq)
		if resp == nil {
			t.Fatal("无效链名应返回错误响应")
		}
	})

	// 可继续补充：无效目标格式、序列不匹配等子测试
}

func TestSubmitHelixfoldaa(t *testing.T) {
	ctx := context.Background()
	ctx = context.WithValue(ctx, "user_info", models.UserInfo{UserID: 1, Type: 1})

	t.Run("正常提交蛋白质任务", func(t *testing.T) {
		setupMocks()
		defer restoreMocks()

		input, _ := json.Marshal(map[string]interface{}{
			"job_name":   "test_protein_task",
			"recycle":    10,
			"ensemble":   1,
			"model_type": "HelixFold3",
			"entities": []map[string]interface{}{
				{
					"type":     "protein",
					"sequence": "MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG",
					"count":    1,
				},
			},
		})

		req := httptest.NewRequest("POST", "/api/submit_helixfoldaa", bytes.NewBuffer(input))
		req.Header.Set("Content-Type", "application/json")
		ghttpReq := ghttp.NewRequest(time.Now(), req)

		resp := SubmitHelixfoldaa(ctx, ghttpReq)
		if resp == nil {
			t.Fatal("SubmitHelixfoldaa 返回值为 nil")
		}
		// 可断言返回的 task_id 等关键信息
	})

	t.Run("提交配体任务", func(t *testing.T) {
		setupMocks()
		defer restoreMocks()

		input, _ := json.Marshal(map[string]interface{}{
			"job_name":   "test_ligand_task",
			"recycle":    10,
			"ensemble":   1,
			"model_type": "HelixFold3",
			"entities": []map[string]interface{}{
				{
					"type":   "ligand",
					"ccd":    "ATP",
					"smiles": "C1=NC2=C(C(=N1)N)N=CN2C3C(C(C(O3)COP(=O)(O)O)O)O",
					"count":  1,
				},
			},
		})

		req := httptest.NewRequest("POST", "/api/submit_helixfoldaa", bytes.NewBuffer(input))
		req.Header.Set("Content-Type", "application/json")
		ghttpReq := ghttp.NewRequest(time.Now(), req)

		resp := SubmitHelixfoldaa(ctx, ghttpReq)
		if resp == nil {
			t.Fatal("配体任务提交失败")
		}
	})

	t.Run("参数缺失", func(t *testing.T) {
		setupMocks()
		defer restoreMocks()

		input, _ := json.Marshal(map[string]interface{}{
			"job_name": "test_invalid_task",
			// 缺少 entities 参数
		})

		req := httptest.NewRequest("POST", "/api/submit_helixfoldaa", bytes.NewBuffer(input))
		req.Header.Set("Content-Type", "application/json")
		ghttpReq := ghttp.NewRequest(time.Now(), req)

		resp := SubmitHelixfoldaa(ctx, ghttpReq)
		if resp == nil {
			t.Fatal("参数缺失应返回错误响应")
		}
		// 可断言错误码和错误信息
	})

	t.Run("无效蛋白质序列", func(t *testing.T) {
		setupMocks()
		defer restoreMocks()

		input, _ := json.Marshal(map[string]interface{}{
			"job_name":   "test_invalid_sequence",
			"recycle":    10,
			"ensemble":   1,
			"model_type": "HelixFold3",
			"entities": []map[string]interface{}{
				{
					"type":     "protein",
					"sequence": "INVALID_SEQUENCE_123", // 包含无效字符
					"count":    1,
				},
			},
		})

		req := httptest.NewRequest("POST", "/api/submit_helixfoldaa", bytes.NewBuffer(input))
		req.Header.Set("Content-Type", "application/json")
		ghttpReq := ghttp.NewRequest(time.Now(), req)

		resp := SubmitHelixfoldaa(ctx, ghttpReq)
		if resp == nil {
			t.Fatal("无效序列应返回错误响应")
		}
	})

	// 可继续补充：RNA/DNA/离子任务、约束条件、参考结构等子测试
}

func TestCheckHF3CommonParam(t *testing.T) {
	ctx := context.Background()

	t.Run("正常参数", func(t *testing.T) {
		entities := []models.HelixFoldAAEntity{
			{
				Type:     "protein",
				Sequence: "MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG",
				Count:    1,
			},
		}

		err := checkHF3CommonParam(ctx, "test_task", 10, 1, entities)
		if err != nil {
			t.Errorf("正常参数校验失败: %v", err)
		}
	})

	t.Run("任务名过长", func(t *testing.T) {
		longName := strings.Repeat("a", 101) // 超过100字符限制
		entities := []models.HelixFoldAAEntity{
			{
				Type:     "protein",
				Sequence: "MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG",
				Count:    1,
			},
		}

		err := checkHF3CommonParam(ctx, longName, 10, 1, entities)
		if err == nil {
			t.Error("任务名过长应返回错误")
		}
	})

	t.Run("recycle参数超出范围", func(t *testing.T) {
		entities := []models.HelixFoldAAEntity{
			{
				Type:     "protein",
				Sequence: "MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG",
				Count:    1,
			},
		}

		// 测试小于最小值
		err := checkHF3CommonParam(ctx, "test_task", 5, 1, entities)
		if err == nil {
			t.Error("recycle小于10应返回错误")
		}

		// 测试大于最大值
		err = checkHF3CommonParam(ctx, "test_task", 150, 1, entities)
		if err == nil {
			t.Error("recycle大于100应返回错误")
		}
	})

	t.Run("ensemble参数超出范围", func(t *testing.T) {
		entities := []models.HelixFoldAAEntity{
			{
				Type:     "protein",
				Sequence: "MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG",
				Count:    1,
			},
		}

		// 测试小于最小值
		err := checkHF3CommonParam(ctx, "test_task", 10, 0, entities)
		if err == nil {
			t.Error("ensemble小于1应返回错误")
		}

		// 测试大于最大值
		err = checkHF3CommonParam(ctx, "test_task", 10, 150, entities)
		if err == nil {
			t.Error("ensemble大于100应返回错误")
		}
	})

	t.Run("空实体列表", func(t *testing.T) {
		err := checkHF3CommonParam(ctx, "test_task", 10, 1, []models.HelixFoldAAEntity{})
		if err == nil {
			t.Error("空实体列表应返回错误")
		}
	})
}

func TestCheckHF3TaskEntity(t *testing.T) {
	ctx := context.Background()

	// 测试正常蛋白质实体
	entities := []models.HelixFoldAAEntity{
		{
			Type:     "protein",
			Sequence: "MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG",
			Count:    1,
		},
	}
	err := checkHF3TaskEntity(ctx, entities)
	if err != nil {
		t.Errorf("checkHF3TaskEntity 正常蛋白质实体测试失败: %v", err)
	}

	// 测试无效实体类型
	entities = []models.HelixFoldAAEntity{
		{
			Type:     "invalid_type",
			Sequence: "MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG",
			Count:    1,
		},
	}
	err = checkHF3TaskEntity(ctx, entities)
	if err == nil {
		t.Error("checkHF3TaskEntity 无效实体类型测试应该失败")
	}

	// 测试空实体类型
	entities = []models.HelixFoldAAEntity{
		{
			Type:     "",
			Sequence: "MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG",
			Count:    1,
		},
	}
	err = checkHF3TaskEntity(ctx, entities)
	if err == nil {
		t.Error("checkHF3TaskEntity 空实体类型测试应该失败")
	}
}

func TestCheckHF3LigandParam(t *testing.T) {
	ctx := context.Background()

	t.Run("正常配体参数", func(t *testing.T) {
		entity := models.HelixFoldAAEntity{
			Type:   "ligand",
			Ccd:    "ATP",
			Smiles: "C1=NC2=C(C(=N1)N)N=CN2C3C(C(C(O3)COP(=O)(O)O)O)O",
			Count:  1,
		}

		err := checkHF3LigandParam(ctx, entity)
		if err != nil {
			t.Errorf("正常配体参数校验失败: %v", err)
		}
	})

	t.Run("CCD和SMILES都为空", func(t *testing.T) {
		entity := models.HelixFoldAAEntity{
			Type:   "ligand",
			Ccd:    "",
			Smiles: "",
			Count:  1,
		}

		err := checkHF3LigandParam(ctx, entity)
		if err == nil {
			t.Error("CCD和SMILES都为空应返回错误")
		}
	})

	t.Run("数量超出范围", func(t *testing.T) {
		entity := models.HelixFoldAAEntity{
			Type:   "ligand",
			Smiles: "C1=NC2=C(C(=N1)N)N=CN2C3C(C(C(O3)COP(=O)(O)O)O)O",
			Count:  100, // 超过50的限制
		}

		err := checkHF3LigandParam(ctx, entity)
		if err == nil {
			t.Error("数量超过50应返回错误")
		}
	})

	t.Run("SMILES包含分隔符", func(t *testing.T) {
		entity := models.HelixFoldAAEntity{
			Type:   "ligand",
			Smiles: "C1=NC2=C(C(=N1)N)N=CN2C3C.C(C(O3)COP(=O)(O)O)O)O", // 包含点号
			Count:  1,
		}

		err := checkHF3LigandParam(ctx, entity)
		if err == nil {
			t.Error("SMILES包含分隔符应返回错误")
		}
	})
}

func TestCheckHF3IONParam(t *testing.T) {
	ctx := context.Background()

	// 测试正常离子参数
	entity := models.HelixFoldAAEntity{
		Type:  "ion",
		Ccd:   "MG",
		Count: 1,
	}
	err := checkHF3IONParam(ctx, entity)
	if err != nil {
		t.Errorf("checkHF3IONParam 正常离子参数测试失败: %v", err)
	}

	// 测试缺少 CCD
	entity = models.HelixFoldAAEntity{
		Type:  "ion",
		Count: 1,
	}
	err = checkHF3IONParam(ctx, entity)
	if err == nil {
		t.Error("checkHF3IONParam 缺少 CCD 测试应该失败")
	}

	// 测试无效 CCD
	entity = models.HelixFoldAAEntity{
		Type:  "ion",
		Ccd:   "INVALID",
		Count: 1,
	}
	err = checkHF3IONParam(ctx, entity)
	if err == nil {
		t.Error("checkHF3IONParam 无效 CCD 测试应该失败")
	}

	// 测试 count 参数超出范围
	entity = models.HelixFoldAAEntity{
		Type:  "ion",
		Ccd:   "MG",
		Count: 51,
	}
	err = checkHF3IONParam(ctx, entity)
	if err == nil {
		t.Error("checkHF3IONParam count 参数超出范围测试应该失败")
	}
}

func TestCheckHF3RNAParam(t *testing.T) {
	ctx := context.Background()

	// 测试正常 RNA 参数
	entity := models.HelixFoldAAEntity{
		Type:     "rna",
		Sequence: "AUGCUGAUCCGAU",
		Count:    1,
	}
	err := checkHF3RNAParam(ctx, entity)
	if err != nil {
		t.Errorf("checkHF3RNAParam 正常 RNA 参数测试失败: %v", err)
	}

	// 测试缺少序列
	entity = models.HelixFoldAAEntity{
		Type:  "rna",
		Count: 1,
	}
	err = checkHF3RNAParam(ctx, entity)
	if err == nil {
		t.Error("checkHF3RNAParam 缺少序列测试应该失败")
	}

	// 测试无效核苷酸
	entity = models.HelixFoldAAEntity{
		Type:     "rna",
		Sequence: "AUGCXGAUCCGAU", // X 是无效核苷酸
		Count:    1,
	}
	err = checkHF3RNAParam(ctx, entity)
	if err == nil {
		t.Error("checkHF3RNAParam 无效核苷酸测试应该失败")
	}

	// 测试 count 参数超出范围
	entity = models.HelixFoldAAEntity{
		Type:     "rna",
		Sequence: "AUGCUGAUCCGAU",
		Count:    2001,
	}
	err = checkHF3RNAParam(ctx, entity)
	if err == nil {
		t.Error("checkHF3RNAParam count 参数超出范围测试应该失败")
	}
}

func TestCheckHF3DNAParam(t *testing.T) {
	ctx := context.Background()

	// 测试正常 DNA 参数
	entity := models.HelixFoldAAEntity{
		Type:     "dna",
		Sequence: "ATGCAGATCCGAT",
		Count:    1,
	}
	err := checkHF3DNAParam(ctx, entity)
	if err != nil {
		t.Errorf("checkHF3DNAParam 正常 DNA 参数测试失败: %v", err)
	}

	// 测试缺少序列
	entity = models.HelixFoldAAEntity{
		Type:  "dna",
		Count: 1,
	}
	err = checkHF3DNAParam(ctx, entity)
	if err == nil {
		t.Error("checkHF3DNAParam 缺少序列测试应该失败")
	}

	// 测试无效核苷酸
	entity = models.HelixFoldAAEntity{
		Type:     "dna",
		Sequence: "ATGCXGATCCGAT", // X 是无效核苷酸
		Count:    1,
	}
	err = checkHF3DNAParam(ctx, entity)
	if err == nil {
		t.Error("checkHF3DNAParam 无效核苷酸测试应该失败")
	}

	// 测试 count 参数超出范围
	entity = models.HelixFoldAAEntity{
		Type:     "dna",
		Sequence: "ATGCAGATCCGAT",
		Count:    2001,
	}
	err = checkHF3DNAParam(ctx, entity)
	if err == nil {
		t.Error("checkHF3DNAParam count 参数超出范围测试应该失败")
	}
}

func TestCheckHF3ProteinParam(t *testing.T) {
	ctx := context.Background()

	t.Run("正常蛋白质参数", func(t *testing.T) {
		entity := models.HelixFoldAAEntity{
			Type:     "protein",
			Sequence: "MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG",
			Count:    1,
		}

		err := checkHF3ProteinParam(ctx, entity)
		if err != nil {
			t.Errorf("正常蛋白质参数校验失败: %v", err)
		}
	})

	t.Run("空序列", func(t *testing.T) {
		entity := models.HelixFoldAAEntity{
			Type:     "protein",
			Sequence: "",
			Count:    1,
		}

		err := checkHF3ProteinParam(ctx, entity)
		if err == nil {
			t.Error("空序列应返回错误")
		}
	})

	t.Run("无效氨基酸", func(t *testing.T) {
		entity := models.HelixFoldAAEntity{
			Type:     "protein",
			Sequence: "MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGGX", // 包含无效字符X
			Count:    1,
		}

		err := checkHF3ProteinParam(ctx, entity)
		if err == nil {
			t.Error("无效氨基酸应返回错误")
		}
	})

	t.Run("数量为0", func(t *testing.T) {
		entity := models.HelixFoldAAEntity{
			Type:     "protein",
			Sequence: "MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG",
			Count:    0,
		}

		err := checkHF3ProteinParam(ctx, entity)
		if err == nil {
			t.Error("数量为0应返回错误")
		}
	})

	t.Run("修饰参数校验", func(t *testing.T) {
		entity := models.HelixFoldAAEntity{
			Type:     "protein",
			Sequence: "MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG",
			Count:    1,
			Modifications: []models.ModificationEntity{
				{
					Type:  "residue_replace",
					Index: 1,
					Ccd:   "SEP", // 磷酸化丝氨酸
				},
			},
		}

		err := checkHF3ProteinParam(ctx, entity)
		if err != nil {
			t.Errorf("正常修饰参数校验失败: %v", err)
		}
	})
}

func TestQuerySubmitHelixFold3TaskPrice(t *testing.T) {
	ctx := context.Background()
	ctx = context.WithValue(ctx, "user_info", models.UserInfo{UserID: 1, Type: 1})

	t.Run("正常价格查询", func(t *testing.T) {
		setupMocks()
		defer restoreMocks()

		input, _ := json.Marshal(map[string]interface{}{
			"job_name":   "test_price_task",
			"recycle":    10,
			"ensemble":   1,
			"model_type": "HelixFold3",
			"entities": []map[string]interface{}{
				{
					"type":     "protein",
					"sequence": "MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG",
					"count":    1,
				},
			},
		})

		req := httptest.NewRequest("POST", "/api/query_helixfold3_task_price", bytes.NewBuffer(input))
		req.Header.Set("Content-Type", "application/json")
		ghttpReq := ghttp.NewRequest(time.Now(), req)

		resp := QuerySubmitHelixFold3TaskPrice(ctx, ghttpReq)
		if resp == nil {
			t.Fatal("QuerySubmitHelixFold3TaskPrice 返回值为 nil")
		}
		// 可断言返回的价格信息
	})

	t.Run("API模式价格查询", func(t *testing.T) {
		setupMocks()
		defer restoreMocks()

		ctx := context.WithValue(ctx, "is_api", 1)
		input, _ := json.Marshal(map[string]interface{}{
			"job_name":   "test_api_price_task",
			"recycle":    10,
			"ensemble":   1,
			"model_type": "HelixFold3",
			"entities": []map[string]interface{}{
				{
					"type":     "protein",
					"sequence": "MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG",
					"count":    1,
				},
			},
		})

		req := httptest.NewRequest("POST", "/api/query_helixfold3_task_price", bytes.NewBuffer(input))
		req.Header.Set("Content-Type", "application/json")
		ghttpReq := ghttp.NewRequest(time.Now(), req)

		resp := QuerySubmitHelixFold3TaskPrice(ctx, ghttpReq)
		if resp == nil {
			t.Fatal("API模式价格查询失败")
		}
	})

	t.Run("参数错误", func(t *testing.T) {
		setupMocks()
		defer restoreMocks()

		input, _ := json.Marshal(map[string]interface{}{
			"job_name":   "test_invalid_price_task",
			"recycle":    5, // 小于最小值
			"ensemble":   1,
			"model_type": "HelixFold3",
			"entities": []map[string]interface{}{
				{
					"type":     "protein",
					"sequence": "MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG",
					"count":    1,
				},
			},
		})

		req := httptest.NewRequest("POST", "/api/query_helixfold3_task_price", bytes.NewBuffer(input))
		req.Header.Set("Content-Type", "application/json")
		ghttpReq := ghttp.NewRequest(time.Now(), req)

		resp := QuerySubmitHelixFold3TaskPrice(ctx, ghttpReq)
		if resp == nil {
			t.Fatal("参数错误应返回错误响应")
		}
	})

	t.Run("S1模型价格查询", func(t *testing.T) {
		setupMocks()
		defer restoreMocks()

		input, _ := json.Marshal(map[string]interface{}{
			"job_name":   "test_s1_price_task",
			"recycle":    10,
			"ensemble":   1,
			"model_type": "HelixFold-S1",
			"entities": []map[string]interface{}{
				{
					"type":     "protein",
					"sequence": "MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG",
					"count":    1,
				},
			},
		})

		req := httptest.NewRequest("POST", "/api/query_helixfold3_task_price", bytes.NewBuffer(input))
		req.Header.Set("Content-Type", "application/json")
		ghttpReq := ghttp.NewRequest(time.Now(), req)

		resp := QuerySubmitHelixFold3TaskPrice(ctx, ghttpReq)
		if resp == nil {
			t.Fatal("S1模型价格查询失败")
		}
	})
}

func TestQueryBatchSubmitHelixFold3TaskPrice(t *testing.T) {
	ctx := context.Background()
	ctx = context.WithValue(ctx, "user_info", models.UserInfo{UserID: 1, Type: 1})

	t.Run("正常批量价格查询", func(t *testing.T) {
		setupMocks()
		defer restoreMocks()

		input, _ := json.Marshal(map[string]interface{}{
			"tasks": []map[string]interface{}{
				{
					"job_name":   "test_batch_price_task1",
					"recycle":    10,
					"ensemble":   1,
					"model_type": "HelixFold3",
					"entities": []map[string]interface{}{
						{
							"type":     "protein",
							"sequence": "MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG",
							"count":    1,
						},
					},
				},
				{
					"job_name":   "test_batch_price_task2",
					"recycle":    10,
					"ensemble":   1,
					"model_type": "HelixFold3",
					"entities": []map[string]interface{}{
						{
							"type":     "protein",
							"sequence": "MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG",
							"count":    1,
						},
					},
				},
			},
		})

		req := httptest.NewRequest("POST", "/api/query_batch_helixfold3_task_price", bytes.NewBuffer(input))
		req.Header.Set("Content-Type", "application/json")
		ghttpReq := ghttp.NewRequest(time.Now(), req)

		resp := QueryBatchSubmitHelixFold3TaskPrice(ctx, ghttpReq)
		if resp == nil {
			t.Fatal("QueryBatchSubmitHelixFold3TaskPrice 返回值为 nil")
		}
	})

	t.Run("空任务列表", func(t *testing.T) {
		setupMocks()
		defer restoreMocks()

		input, _ := json.Marshal(map[string]interface{}{
			"tasks": []map[string]interface{}{},
		})

		req := httptest.NewRequest("POST", "/api/query_batch_helixfold3_task_price", bytes.NewBuffer(input))
		req.Header.Set("Content-Type", "application/json")
		ghttpReq := ghttp.NewRequest(time.Now(), req)

		resp := QueryBatchSubmitHelixFold3TaskPrice(ctx, ghttpReq)
		if resp == nil {
			t.Fatal("空任务列表应返回错误响应")
		}
	})

	t.Run("混合模型类型", func(t *testing.T) {
		setupMocks()
		defer restoreMocks()

		input, _ := json.Marshal(map[string]interface{}{
			"tasks": []map[string]interface{}{
				{
					"job_name":   "test_base_model",
					"recycle":    10,
					"ensemble":   1,
					"model_type": "HelixFold3",
					"entities": []map[string]interface{}{
						{
							"type":     "protein",
							"sequence": "MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG",
							"count":    1,
						},
					},
				},
				{
					"job_name":   "test_s1_model",
					"recycle":    10,
					"ensemble":   1,
					"model_type": "HelixFold-S1",
					"entities": []map[string]interface{}{
						{
							"type":     "protein",
							"sequence": "MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG",
							"count":    1,
						},
					},
				},
			},
		})

		req := httptest.NewRequest("POST", "/api/query_batch_helixfold3_task_price", bytes.NewBuffer(input))
		req.Header.Set("Content-Type", "application/json")
		ghttpReq := ghttp.NewRequest(time.Now(), req)

		resp := QueryBatchSubmitHelixFold3TaskPrice(ctx, ghttpReq)
		if resp == nil {
			t.Fatal("混合模型类型价格查询失败")
		}
	})
}

// 辅助函数测试
func TestCalculateT(t *testing.T) {
	// 测试正常计算
	result := calculateT(100, 1, 10)
	if result <= 0 {
		t.Error("calculateT 计算结果应该大于 0")
	}

	// 测试边界值
	result = calculateT(1, 1, 10)
	if result <= 0 {
		t.Error("calculateT 边界值计算结果应该大于 0")
	}

	result = calculateT(3000, 100, 100)
	if result <= 0 {
		t.Error("calculateT 最大值计算结果应该大于 0")
	}
}

func TestToCamelCase(t *testing.T) {
	// 测试下划线转驼峰
	result := toCamelCase("test_case")
	if result != "testCase" {
		t.Errorf("toCamelCase 期望 'testCase', 实际得到 '%s'", result)
	}

	// 测试多个下划线
	result = toCamelCase("test_case_example")
	if result != "testCaseExample" {
		t.Errorf("toCamelCase 期望 'testCaseExample', 实际得到 '%s'", result)
	}

	// 测试无下划线
	result = toCamelCase("testcase")
	if result != "testcase" {
		t.Errorf("toCamelCase 期望 'testcase', 实际得到 '%s'", result)
	}

	// 测试空字符串
	result = toCamelCase("")
	if result != "" {
		t.Errorf("toCamelCase 期望 '', 实际得到 '%s'", result)
	}
}

// 测试数据组装函数
func TestAssembleHelixFoldAAEntities(t *testing.T) {
	// 测试正常实体组装
	entities := []interface{}{
		map[string]interface{}{
			"type":     "protein",
			"sequence": "MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG",
			"count":    1,
		},
	}

	result := assembleHelixFoldAAEntities(entities)
	if len(result) != 1 {
		t.Errorf("assembleHelixFoldAAEntities 期望长度 1, 实际得到 %d", len(result))
	}

	if result[0].Type != "protein" {
		t.Errorf("assembleHelixFoldAAEntities 期望类型 'protein', 实际得到 '%s'", result[0].Type)
	}
}

func TestAssembleHelixFoldAAConstraint(t *testing.T) {
	// 测试正常约束组装
	constraints := []interface{}{
		map[string]interface{}{
			"type": "distance",
			"pairs": []map[string]interface{}{
				{
					"entity1":  1,
					"entity2":  2,
					"distance": 10.0,
				},
			},
		},
	}

	result := assembleHelixFoldAAConstraint(constraints)
	if len(result) != 1 {
		t.Errorf("assembleHelixFoldAAConstraint 期望长度 1, 实际得到 %d", len(result))
	}

	if result[0].Type != "distance" {
		t.Errorf("assembleHelixFoldAAConstraint 期望类型 'distance', 实际得到 '%s'", result[0].Type)
	}
}

// 测试工具函数
func TestKeepTwoDecimalPlaces(t *testing.T) {
	// 测试保留两位小数
	result := keepTwoDecimalPlaces(3.14159)
	if result != 3.14 {
		t.Errorf("keepTwoDecimalPlaces 期望 3.14, 实际得到 %f", result)
	}

	// 测试整数
	result = keepTwoDecimalPlaces(3.0)
	if result != 3.0 {
		t.Errorf("keepTwoDecimalPlaces 期望 3.0, 实际得到 %f", result)
	}

	// 测试负数
	result = keepTwoDecimalPlaces(-3.14159)
	if result != -3.14 {
		t.Errorf("keepTwoDecimalPlaces 期望 -3.14, 实际得到 %f", result)
	}
}
