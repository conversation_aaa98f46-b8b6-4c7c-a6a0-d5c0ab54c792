package controllers

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http/httptest"
	"testing"
	"time"

	"icode.baidu.com/baidu/gdp/ghttp"
	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/models"
)

// TestSubmitAdmit 测试SubmitAdmit函数，该函数用于提交审核申请。
// 参数：t *testing.T - 指向testing包中的*testing.T类型，表示单元测试的对象；
// 返回值：无返回值
func TestSubmitAdmit(t *testing.T) {
	ctx := context.Background()
	ctx = context.WithValue(ctx, "user_info", models.UserInfo{UserID: 1, Type: 1})

	input2, _ := json.Marshal(map[string]interface{}{
		"serial": "serialxxx",
	})
	input4, _ := json.Marshal(map[string]interface{}{
		"file_url": "xxx",
		"name":     "test002001",
	})

	caseList := []struct {
		input  io.Reader
		output int
	}{
		{nil, helpers.ParamErrorCode},
		{bytes.NewReader(input2), helpers.SuccessCode},
		{bytes.NewReader(input4), helpers.ParamErrorCode},
	}

	for _, v := range caseList {
		raw := httptest.NewRequest("POST", "/", v.input)
		req := ghttp.NewRequest(time.Now(), raw)

		resp := SubmitAdmet(ctx, req).(*ghttp.JSONResponse)
		data := resp.Data.(helpers.JsonResp)
		if data.Code != v.output {
			fmt.Println(data)
			t.Errorf("code=%d, want=%d", data.Code, v.output)
		}
	}
}

// TestSearchResult 测试函数，用于搜索结果的测试
// t *testing.T: 表示一个单元测试，它是一个指向 testing.T 类型的指针
// 返回值没有，如果发现错误会调用 t.Errorf 进行输出和记录
func TestSearchResult(t *testing.T) {
	ctx := context.Background()
	ctx = context.WithValue(ctx, "user_info", models.UserInfo{UserID: 1, Type: 1})

	input1, _ := json.Marshal(map[string]interface{}{
		"task_id": 0,
		"content": map[string][]string{"caco2_perm": {"1"}},
	})
	input3, _ := json.Marshal(map[string]interface{}{
		"task_id": 650,
		"content": map[string][]string{
			"caco2_permeability":    {"1"},
			"intestinal_absorption": {"1"},
			"oral_bioavailability":  {"1"},
			"barrier_permeant":      {"1"},
			"protein_binding":       {"1"},
			"oral_toxicity":         {"1"},
			"lipinski_law":          {"1"},
			"ghose_law":             {"1"},
			"carcinogenicity":       {"1"},
		},
	})
	caseList := []struct {
		input  io.Reader
		output int
	}{
		{nil, helpers.ParamErrorCode},
		{bytes.NewReader(input1), helpers.ParamErrorCode},
		{bytes.NewReader(input3), helpers.SuccessCode},
	}

	for _, v := range caseList {
		raw := httptest.NewRequest("POST", "/", v.input)
		req := ghttp.NewRequest(time.Now(), raw)

		resp := SearchResult(ctx, req).(*ghttp.JSONResponse)
		data := resp.Data.(helpers.JsonResp)
		if data.Code != v.output {
			fmt.Println(data)
			t.Errorf("code=%d, want=%d", data.Code, v.output)
		}
	}
}

// TestDownloadResult 测试函数，用于下载结果的测试
// t *testing.T: 类型为*testing.T，表示单元测试对象
// 返回值没有返回值
func TestDownloadResult(t *testing.T) {
	ctx := context.Background()
	ctx = context.WithValue(ctx, "user_info", models.UserInfo{UserID: 1, Type: 1})

	input1, _ := json.Marshal(map[string]int{"task_id": 12})
	input2, _ := json.Marshal(map[string]interface{}{"task_id": 12, "ids": []int{1, 2, 3}})
	input3, _ := json.Marshal(map[string]interface{}{
		"task_id": 650,
		"content": map[string][]string{"caco2_perm": {"1"}},
	})
	input4, _ := json.Marshal(map[string]interface{}{
		"task_id": 650,
		"notids":  []int{1, 2, 3},
		"content": map[string][]string{"caco2_permeability": {"1"}},
	})
	caseList := []struct {
		input  io.Reader
		output int
	}{
		{nil, helpers.ParamErrorCode},
		{bytes.NewReader(input1), helpers.SuccessCode},
		{bytes.NewReader(input2), helpers.SuccessCode},
		{bytes.NewReader(input3), helpers.SuccessCode},
		{bytes.NewReader(input4), helpers.SuccessCode},
	}

	for _, v := range caseList {
		raw := httptest.NewRequest("POST", "/", v.input)
		req := ghttp.NewRequest(time.Now(), raw)

		resp := DownloadResult(ctx, req).(*ghttp.JSONResponse)
		data := resp.Data.(helpers.JsonResp)
		if data.Code != v.output {
			fmt.Println(data)
			t.Errorf("code=%d, want=%d", data.Code, v.output)
		}
	}
}
