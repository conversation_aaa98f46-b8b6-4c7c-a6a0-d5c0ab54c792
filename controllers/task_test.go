package controllers

import (
	"bytes"
	"context"
	"encoding/json"
	"io"
	"net/http/httptest"
	"testing"
	"time"

	"icode.baidu.com/baidu/gdp/ghttp"
	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/models"
)

// TestGetTaskInfo 测试函数TestGetTaskInfo，用于获取任务信息
// 参数t *testing.T：表示测试对象，必须传入
// 返回值类型为空
func TestGetTaskInfo(t *testing.T) {
	ctx := context.Background()
	ctx = context.WithValue(ctx, "user_info", models.UserInfo{UserID: 1, Type: 1})

	input1, _ := json.Marshal(map[string]int{"task_id": 12})
	input2, _ := json.Marshal(map[string]int{"task_id": 121212})
	caseList := []struct {
		input  io.Reader
		output int
	}{
		{nil, helpers.ParamErrorCode},
		{bytes.NewReader(input1), helpers.SuccessCode},
		{bytes.NewReader(input2), helpers.SuccessCode},
	}

	for _, v := range caseList {
		raw := httptest.NewRequest("POST", "/", v.input)
		req := ghttp.NewRequest(time.Now(), raw)

		resp := GetTaskInfo(ctx, req).(*ghttp.JSONResponse)
		data := resp.Data.(helpers.JsonResp)
		if data.Code != v.output {
			t.Errorf("code=%d, want=%d", data.Code, v.output)
		}
	}
}

// TestSetTaskRead 测试函数，用于设置任务已读状态
// 参数t：*testing.T类型，表示当前测试用例的指针
// 返回值：无
func TestSetTaskRead(t *testing.T) {
	ctx := context.Background()
	ctx = context.WithValue(ctx, "user_info", models.UserInfo{UserID: 1, Type: 1})

	input1, _ := json.Marshal(map[string]int{"task_id": 12})
	caseList := []struct {
		input  io.Reader
		output int
	}{
		{nil, helpers.SuccessCode},
		{bytes.NewReader(input1), helpers.SuccessCode},
	}

	for _, v := range caseList {
		raw := httptest.NewRequest("POST", "/", v.input)
		req := ghttp.NewRequest(time.Now(), raw)

		resp := SetTaskRead(ctx, req).(*ghttp.JSONResponse)
		data := resp.Data.(helpers.JsonResp)
		if data.Code != v.output {
			t.Errorf("code=%d, want=%d", data.Code, v.output)
		}
	}
}

// TestGetHistoryTrainList 测试函数TestGetHistoryTrainList，用于获取历史训练列表
// 参数t：*testing.T类型，表示单元测试的对象指针
// 返回值：无
func TestGetHistoryTrainList(t *testing.T) {
	ctx := context.Background()
	ctx = context.WithValue(ctx, "user_info", models.UserInfo{UserID: 1, Type: 1})

	input1, _ := json.Marshal(map[string]int{"type": 120})
	input2, _ := json.Marshal(map[string]int{"type": 30})
	caseList := []struct {
		input  io.Reader
		output int
	}{
		{nil, helpers.SuccessCode},
		{bytes.NewReader(input1), helpers.SuccessCode},
		{bytes.NewReader(input2), helpers.SuccessCode},
	}

	for _, v := range caseList {
		raw := httptest.NewRequest("POST", "/", v.input)
		req := ghttp.NewRequest(time.Now(), raw)

		resp := GetHistoryTrainList(ctx, req).(*ghttp.JSONResponse)
		data := resp.Data.(helpers.JsonResp)
		if data.Code != v.output {
			t.Errorf("code=%d, want=%d", data.Code, v.output)
		}
	}
}

// TestGetTaskList 测试函数，用于获取任务列表
// 参数t：*testing.T类型，表示当前测试用例的指针
// 返回值无
func TestGetTaskList(t *testing.T) {
	ctx := context.Background()
	ctx = context.WithValue(ctx, "user_info", models.UserInfo{UserID: 1, Type: 1})

	input1, _ := json.Marshal(map[string]int{"status": 10})
	input2, _ := json.Marshal(map[string]int{"type_list": 10})
	input3, _ := json.Marshal(map[string]interface{}{"type_list": []int{30, 50}})
	input4, _ := json.Marshal(map[string]interface{}{"type_list": []int{51, 30}})

	caseList := []struct {
		input  io.Reader
		output int
	}{
		{nil, helpers.SuccessCode},
		{bytes.NewReader(input1), helpers.SuccessCode},
		{bytes.NewReader(input2), helpers.SuccessCode},
		{bytes.NewReader(input3), helpers.SuccessCode},
		{bytes.NewReader(input4), helpers.SuccessCode},
	}

	for _, v := range caseList {
		raw := httptest.NewRequest("POST", "/", v.input)
		req := ghttp.NewRequest(time.Now(), raw)

		resp := GetTaskList(ctx, req).(*ghttp.JSONResponse)
		data := resp.Data.(helpers.JsonResp)
		if data.Code != v.output {
			t.Errorf("code=%d, want=%d", data.Code, v.output)
		}
	}
}

// TestCancelTask 测试函数，用于取消任务
// 参数：*testing.T - 类型为*testing.T的指针，表示测试对象
// 返回值：无返回值
func TestCancelTask(t *testing.T) {
	ctx := context.Background()
	ctx = context.WithValue(ctx, "user_info", models.UserInfo{UserID: 1, Type: 1})

	input1, _ := json.Marshal(map[string]int{"task_id": 10})
	input2, _ := json.Marshal(map[string]int{"task_id": 12})
	caseList := []struct {
		input  io.Reader
		output int
	}{
		{nil, helpers.ParamErrorCode},
		{bytes.NewReader(input1), helpers.SuccessCode},
		{bytes.NewReader(input2), helpers.SuccessCode},
	}

	for _, v := range caseList {
		raw := httptest.NewRequest("POST", "/", v.input)
		req := ghttp.NewRequest(time.Now(), raw)

		resp := CancelTask(ctx, req).(*ghttp.JSONResponse)
		data := resp.Data.(helpers.JsonResp)
		if data.Code != v.output {
			t.Errorf("CancelTask code=%d, want=%d", data.Code, v.output)
		}
	}
}

// TestDelTask 测试函数，用于删除任务
// 参数：*testing.T - 类型为*testing.T的指针，表示测试对象
// 返回值：无
func TestDelTask(t *testing.T) {
	ctx := context.Background()
	ctx = context.WithValue(ctx, "user_info", models.UserInfo{UserID: 1, Type: 1})

	input1, _ := json.Marshal(map[string]int{"task_id": 10})
	input2, _ := json.Marshal(map[string]int{"task_id": 12})
	caseList := []struct {
		input  io.Reader
		output int
	}{
		{nil, helpers.ParamErrorCode},
		{bytes.NewReader(input1), helpers.SuccessCode},
		{bytes.NewReader(input2), helpers.SuccessCode},
	}

	for _, v := range caseList {
		raw := httptest.NewRequest("POST", "/", v.input)
		req := ghttp.NewRequest(time.Now(), raw)

		resp := DelTask(ctx, req).(*ghttp.JSONResponse)
		data := resp.Data.(helpers.JsonResp)
		if data.Code != v.output {
			t.Errorf("CancelTask code=%d, want=%d", data.Code, v.output)
		}
	}
}

// TestRenameTask 测试重命名任务函数TestRenameTask，该函数接收一个*testing.T类型的参数t，用于标识当前测试用例。
// 该函数首先创建一个context.Context类型的上下文ctx，并将其中的user_info字段设置为models.UserInfo类型的结构体，包含用户ID和类型。
// 然后使用json.Marshal函数将两个不同的输入参数转换成[]byte类型，分别是map[string]int类型和map[string]interface{}类型，用于模拟不同的请求场景。
// 接着定义了一个caseList切片，每个元素都包含一个io.Reader类型的input和一个int类型的output，用于存放不同的测试用例。
// 遍历caseList切片，对每个元素进行如下操作：
// 1. 创建一个httptest.NewRequest函数创建一个新的HTTP请求raw，方法为POST，路径为"/", 请求体为v.input。
// 2. 使用ghttp.NewRequest函数创建一个ghttp.Request类型的req，并传入时间now和raw作为参数。
// 3. 调用RenameTask函数，传入ctx和req作为参数，并将返回值保存在resp变量中，它是一个*ghttp.JSONResponse类型的指针。
// 4. 从resp中取出data，它是一个helpers.JsonResp类型的结构体，包含code字段，用于存放返回码。
// 5. 判断data.Code是否等于v.output，如果不相等则输出错误信息，否则继续下一个测试用
func TestRenameTask(t *testing.T) {
	ctx := context.Background()
	ctx = context.WithValue(ctx, "user_info", models.UserInfo{UserID: 1, Type: 1})

	input1, _ := json.Marshal(map[string]int{"task_id": 10})
	input2, _ := json.Marshal(map[string]interface{}{"task_id": 15, "name": "testxx"})
	caseList := []struct {
		input  io.Reader
		output int
	}{
		{nil, helpers.ParamErrorCode},
		{bytes.NewReader(input1), helpers.ParamErrorCode},
		{bytes.NewReader(input2), helpers.SuccessCode},
	}

	for _, v := range caseList {
		raw := httptest.NewRequest("POST", "/", v.input)
		req := ghttp.NewRequest(time.Now(), raw)

		resp := RenameTask(ctx, req).(*ghttp.JSONResponse)
		data := resp.Data.(helpers.JsonResp)
		if data.Code != v.output {
			t.Errorf("code=%d, want=%d", data.Code, v.output)
		}
	}
}

// TestSubmitTrain 测试SubmitTrain函数，用于提交训练任务。
// 参数t是*testing.T类型的指针，表示当前正在执行的单元测试。
// 返回值没有，但是该函数可能会使用t来输出错误信息。
func TestSubmitTrain(t *testing.T) {
	ctx := context.Background()
	ctx = context.WithValue(ctx, "user_info", models.UserInfo{UserID: 1, Type: 1})

	input1, _ := json.Marshal(map[string]interface{}{
		"type": 30,
	})
	input2, _ := json.Marshal(map[string]interface{}{
		"type":       40,
		"name":       "name1212",
		"file_url":   "bos:/xxxx/xx",
		"train_type": "12",
	})
	input3, _ := json.Marshal(map[string]interface{}{
		"type":     40,
		"name":     "name1212",
		"file_url": "bos:/bml-test-test/helix_upload/866fae6a-b842-4b0b-a6b8-7bbb366afdd8.txt",
	})
	input4, _ := json.Marshal(map[string]interface{}{
		"type":       30,
		"name":       "name1212",
		"file_url":   "bos:/bml-test-test/helix_upload/866fae6a-b842-4b0b-a6b8-7bbb366afdd8.txt",
		"train_type": "regression",
	})
	caseList := []struct {
		input  io.Reader
		output int
	}{
		{nil, helpers.ParamErrorCode},
		{bytes.NewReader(input1), helpers.ParamErrorCode},
		{bytes.NewReader(input2), helpers.ParamErrorCode},
		{bytes.NewReader(input3), helpers.SuccessCode},
		{bytes.NewReader(input4), helpers.SuccessCode},
	}

	for _, v := range caseList {
		raw := httptest.NewRequest("POST", "/", v.input)
		req := ghttp.NewRequest(time.Now(), raw)

		resp := SubmitTrain(ctx, req).(*ghttp.JSONResponse)
		data := resp.Data.(helpers.JsonResp)
		if data.Code != v.output {
			t.Errorf("code=%d, want=%d", data.Code, v.output)
		}
	}
}
