package controllers

import (
	"context"
	"encoding/json"
	"strings"

	"icode.baidu.com/baidu/gdp/ghttp"

	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/models"
	"icode.baidu.com/helix_web/services"
)

const (
	// 蛋白质结构预测 序列限制(普通用户)
	ProteinSerialLimit        = 100
	ProteinComplexSerialLimit = 300
	ProteinMaxSerialLimit     = 2500
)

var proteinLevelToLenMap = map[int]int{
	services.Level1: 1000,
	services.Level2: 3000,
	services.Level3: 100000,
}
var proteinComplexLevelToLenMap = map[int]int{
	services.Level1: 2000,
	services.Level2: 4000,
	services.Level3: 100000,
}

// 提交预测（蛋白复合物）
func SubmitProteinComplex(ctx context.Context, req ghttp.Request) ghttp.Response {
	params := helpers.GetBodyParams(ctx, req)
	fileUrl := helpers.GetStringParam(params, "file_url")
	serial := helpers.GetStringParam(params, "serial")
	name := helpers.GetStringParam(params, "name")

	// 参数校验
	name = getTaskName(name)
	err := checkTaskCommonParam(serial, fileUrl, name)
	if err != nil {
		return helpers.NewException(ctx, "PARAM_VALUE_ERR", err.Error())
	}

	// config
	conf, contentStr, err := getCommonTaskConf(serial, fileUrl)
	if err != nil {
		return helpers.NewException(ctx, "PARAM_VALUE_ERR", err.Error())
	}
	if len(contentStr) > ProteinMaxSerialLimit {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "protein serial len limit")
	}
	var needConf models.TaskProteinSingleConf
	needConf.Serial = conf.Serial
	needConf.FileUrl = conf.FileUrl
	needConf.SerialLen = int64(len(contentStr))

	level, isCharge := getProteinLevel(ctx, int64(models.TaskTypeProteinComplex), contentStr)
	chargeP := chargeParam{IsCharge: isCharge}
	chargeTab, err := checkCharge(ctx, int64(models.TaskTypeProteinComplex), level, chargeP)
	if err != nil {
		return helpers.NewException(ctx, "PARAM_VALUE_ERR", err.Error())
	}
	needConf.Level = int64(level)

	// 数据组装
	configByte, _ := json.Marshal(needConf)
	taskN := models.Task{
		Name:      name,
		UserId:    uint64(getUserId(ctx)),
		Type:      uint64(models.TaskTypeProteinComplex),
		FuncType:  uint64(models.FuncTypeForecast),
		Config:    string(configByte),
		ChargeTab: uint64(chargeTab),
		IsExample: uint64(checkSerialExample(ctx, int64(models.TaskTypeProteinComplex), contentStr)),
	}
	taskNew, err := taskN.Add(ctx, taskN)
	if err != nil {
		return helpers.NewException(ctx, "DB_ERR")
	}

	// 提交任务给调度
	services.PreSubmitTask(ctx, taskNew)
	result := map[string]any{
		"task_id": taskNew.ID,
	}
	return helpers.SuccReturn(ctx, result)
}

// 提交预测（蛋白质功能）
func SubmitProteinFunc(ctx context.Context, req ghttp.Request) ghttp.Response {
	params := helpers.GetBodyParams(ctx, req)
	name := helpers.GetStringParam(params, "name")
	pdbUrl := helpers.GetStringParam(params, "pdb_url")
	pdbName := helpers.GetStringParam(params, "pdb_name")

	// param 校验
	name = getTaskName(name)
	if len([]rune(name)) > MaxTaskNameLimit {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "name param limit")
	}
	if len(pdbUrl) <= 0 || len(pdbUrl) > MaxFileUrlLimit {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "pdb_url param error")
	}
	if len(pdbName) <= 0 || len(pdbName) > MaxTaskNameLimit {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "pdb_name param error")
	}

	// config 校验
	var needConfig models.TaskProteinFuncConf
	needConfig.PdbUrl = pdbUrl
	needConfig.PdbName = pdbName

	// 数据组装
	configByte, _ := json.Marshal(needConfig)
	taskN := models.Task{
		Name:     name,
		UserId:   uint64(getUserId(ctx)),
		Type:     uint64(models.TaskTypeProteinFunc),
		FuncType: uint64(models.FuncTypeForecast),
		Config:   string(configByte),
	}
	taskNew, err := taskN.Add(ctx, taskN)
	if err != nil {
		return helpers.FailReturn(ctx, helpers.LogicErrorCode, "data error, repeat")
	}

	// 提交任务给调度
	services.PreSubmitTask(ctx, taskNew)
	result := map[string]any{
		"task_id": taskNew.ID,
	}
	return helpers.SuccReturn(ctx, result)
}

// 提交预测
func SubmitProteinSingle(ctx context.Context, req ghttp.Request) ghttp.Response {
	params := helpers.GetBodyParams(ctx, req)
	fileUrl := helpers.GetStringParam(params, "file_url")
	serial := helpers.GetStringParam(params, "serial")
	name := helpers.GetStringParam(params, "name")

	// 参数校验
	name = getTaskName(name)
	err := checkTaskCommonParam(serial, fileUrl, name)
	if err != nil {
		return helpers.NewException(ctx, "PARAM_VALUE_ERR", err.Error())
	}

	// config
	conf, contentStr, err := getCommonTaskConf(serial, fileUrl)
	if err != nil {
		return helpers.NewException(ctx, "PARAM_VALUE_ERR", err.Error())
	}
	if len(contentStr) > ProteinMaxSerialLimit {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "protein serial len limit")
	}
	var needConf models.TaskProteinSingleConf
	needConf.Serial = conf.Serial
	needConf.FileUrl = conf.FileUrl

	// 计费check
	level, isCharge := getProteinLevel(ctx, int64(models.TaskTypeProteinSingle), contentStr)
	chargeP := chargeParam{IsCharge: isCharge}
	chargeTab, err := checkCharge(ctx, int64(models.TaskTypeProteinSingle), level, chargeP)
	if err != nil {
		return helpers.NewException(ctx, "PARAM_VALUE_ERR", err.Error())
	}
	needConf.Level = int64(level)
	needConf.SerialLen = int64(len(contentStr))

	// 数据组装
	configByte, _ := json.Marshal(needConf)
	taskN := models.Task{
		Name:      name,
		UserId:    uint64(getUserId(ctx)),
		Type:      uint64(models.TaskTypeProteinSingle),
		FuncType:  uint64(models.FuncTypeForecast),
		Config:    string(configByte),
		ChargeTab: uint64(chargeTab),
		IsExample: uint64(checkSerialExample(ctx, int64(models.TaskTypeProteinSingle), contentStr)),
	}
	taskNew, err := taskN.Add(ctx, taskN)
	if err != nil {
		return helpers.NewException(ctx, "DB_ERR")
	}

	// 提交任务给调度
	services.PreSubmitTask(ctx, taskNew)
	result := map[string]any{
		"task_id": taskNew.ID,
	}
	return helpers.SuccReturn(ctx, result)
}

// 提交预测
func SubmitProtein(ctx context.Context, req ghttp.Request) ghttp.Response {
	params := helpers.GetBodyParams(ctx, req)
	fileUrl := helpers.GetStringParam(params, "file_url")
	serial := helpers.GetStringParam(params, "serial")
	name := helpers.GetStringParam(params, "name")
	pdbUrl := helpers.GetStringParam(params, "pdb_url")
	pdbName := helpers.GetStringParam(params, "pdb_name")
	ensembling := helpers.GetIntParam(params, "ensembling", 1)
	relaxation := helpers.GetBoolParam(params, "relaxation", false)
	fold2 := helpers.GetBoolParam(params, "fold2", false)

	// 参数校验
	name = getTaskName(name)
	err := checkTaskCommonParam(serial, fileUrl, name)
	if err != nil {
		return helpers.NewException(ctx, "PARAM_VALUE_ERR", err.Error())
	}
	if len(pdbUrl) > MaxFileUrlLimit {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "pdb_url param error")
	}
	if len([]rune(pdbName)) > MaxTaskNameLimit {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "pdb_name param error")
	}

	// config 校验
	var needConf models.TaskProteinConf
	needConf.Ensembling = ensembling
	needConf.Relaxation = relaxation
	needConf.Fold2 = fold2
	if len(pdbUrl) > 0 {
		needConf.PdbUrl = pdbUrl
		needConf.PdbName = pdbName
	}

	conf, contentStr, err := getCommonTaskConf(serial, fileUrl)
	if err != nil {
		return helpers.NewException(ctx, "PARAM_VALUE_ERR", err.Error())
	}
	if len(contentStr) > ProteinMaxSerialLimit {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "protein serial len limit")
	}
	needConf.Serial = conf.Serial
	needConf.FileUrl = conf.FileUrl
	needConf.SerialLen = int64(len(contentStr))

	// 计费check
	level, isCharge := getProteinLevel(ctx, int64(models.TaskTypeProtein), contentStr)
	chargeP := chargeParam{IsCharge: isCharge}
	ChargeTab, err := checkCharge(ctx, int64(models.TaskTypeProtein), level, chargeP)
	if err != nil {
		return helpers.NewException(ctx, "PARAM_VALUE_ERR", err.Error())
	}
	needConf.Level = int64(level)

	// 数据组装
	configByte, _ := json.Marshal(needConf)
	taskN := models.Task{
		UserId:    uint64(getUserId(ctx)),
		Type:      uint64(models.TaskTypeProtein),
		Name:      name,
		FuncType:  uint64(models.FuncTypeForecast),
		Config:    string(configByte),
		ChargeTab: uint64(ChargeTab),
		IsExample: uint64(checkSerialExample(ctx, int64(models.TaskTypeProtein), contentStr)),
	}
	taskNew, err := taskN.Add(ctx, taskN)
	if err != nil {
		return helpers.FailReturn(ctx, helpers.LogicErrorCode, "data error, repeat")
	}

	// 提交任务给调度
	services.PreSubmitTask(ctx, taskNew)
	result := map[string]any{
		"task_id": taskNew.ID,
	}
	return helpers.SuccReturn(ctx, result)
}

// 获取蛋白序列长度
func getProteinLevel(ctx context.Context, taskType int64, contentStr string) (int, bool) {
	// 示例序列不限制
	serialM := models.Serial{}
	serialData, err := serialM.GetBySerial(ctx, taskType, contentStr)
	if err == nil && serialData.ID > 0 {
		return services.Level1, false
	}

	index := strings.Index(contentStr, "\n")
	realSerial := contentStr[index+1:]
	contentLen := len(realSerial) - 1
	if taskType == int64(models.TaskTypeProteinComplex) {
		if contentLen < proteinComplexLevelToLenMap[services.Level1]+500 {
			if contentLen < ProteinComplexSerialLimit {
				return services.Level1, false
			}
			return services.Level1, true
		} else if contentLen < proteinComplexLevelToLenMap[services.Level2]+500 {
			return services.Level2, true
		}
	} else {
		if contentLen < proteinLevelToLenMap[services.Level1] {
			if contentLen < ProteinSerialLimit {
				return services.Level1, false
			}
			return services.Level1, true
		} else if contentLen < proteinLevelToLenMap[services.Level2] {
			return services.Level2, true
		}
	}

	return services.Level3, true
}

// 提交预测（protein relaxation）
func SubmitProteinRelaxation(ctx context.Context, req ghttp.Request) ghttp.Response {
	params := helpers.GetBodyParams(ctx, req)
	proteinUrl := helpers.GetStringParam(params, "protein_url")
	name := helpers.GetStringParam(params, "name")

	// 参数校验
	name = getTaskName(name)
	if len([]rune(name)) > MaxTaskNameLimit {
		return helpers.NewException(ctx, "PARAM_VALUE_ERR", "name length limit")
	}
	err := checkFileContent(getFileContent(proteinUrl))
	if err != nil {
		return helpers.NewException(ctx, "PARAM_VALUE_ERR", err.Error())
	}

	// 数据组装
	needConfig := models.TaskProteinRelaxationConf{
		ProteinUrl: proteinUrl,
	}
	configByte, _ := json.Marshal(needConfig)
	taskN := models.Task{
		Name:     name,
		UserId:   uint64(getUserId(ctx)),
		Type:     uint64(models.TaskTypeProteinRelaxation),
		FuncType: uint64(models.FuncTypeForecast),
		Config:   string(configByte),
	}
	taskNew, err := taskN.Add(ctx, taskN)
	if err != nil {
		return helpers.NewException(ctx, "DB_ERR")
	}

	// 任务提交
	services.PreSubmitTask(ctx, taskNew)
	result := map[string]any{
		"task_id": taskNew.ID,
	}
	return helpers.SuccReturn(ctx, result)
}
