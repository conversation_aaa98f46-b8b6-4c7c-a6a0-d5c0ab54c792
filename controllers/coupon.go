package controllers

import (
	"context"

	"icode.baidu.com/baidu/gdp/ghttp"

	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/models"
)

// 获取优惠券
func GetCouponList(ctx context.Context, req ghttp.Request) ghttp.Response {
	params := helpers.GetBodyParams(ctx, req)
	taskType := helpers.GetIntParam(params, "task_type", 0)

	// 参数检验
	if !models.CheckTaskChargeType(int(taskType)) {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "task_type params error")
	}

	couponM := models.Coupon{}
	couponList, err := couponM.GetList(ctx, getUserId(ctx), taskType)
	if err != nil {
		return helpers.NewException(ctx, "DB_ERR")
	}

	// 数据组装
	var items []any
	for _, coupon := range couponList {
		items = append(items, coupon.SwapData())
	}

	result := map[string]any{
		"items": items,
	}
	return helpers.SuccReturn(ctx, result)
}

// 获取优惠券消费记录
func CouponCostList(ctx context.Context, req ghttp.Request) ghttp.Response {
	params := helpers.GetBodyParams(ctx, req)
	limit := helpers.GetIntParam(params, "limit", 20)
	page := helpers.GetIntParam(params, "page", 1)

	// 参数检验
	limit, page = formatPaging(limit, page)

	// 获取数据
	billM := models.Bill{}
	billCond := models.BillCond{
		UserId:   getUserId(ctx),
		NeedPush: []int{models.NeedPushFalse},
	}
	total, err := billM.Count(ctx, billCond)
	if err != nil {
		return helpers.NewException(ctx, "DB_ERR")
	}
	billList, err := billM.GetList(ctx, billCond, int(limit), int(page))
	if err != nil {
		return helpers.NewException(ctx, "DB_ERR")
	}

	var taskIdList []int64
	for _, bill := range billList {
		taskIdList = append(taskIdList, int64(bill.TaskId))
	}
	taskMap := getTaskMap(ctx, taskIdList)

	var items []any
	for _, bill := range billList {
		tmp := map[string]any{
			"task_id":           bill.TaskId,
			"task_type":         bill.TaskType,
			"charge_start_time": bill.StartTime.Unix(),
			"cost_type":         models.ChargeTabCoupon,
			"cost_amount":       bill.CostAmount,
		}

		tmp["task_name"] = ""
		if task, ok := taskMap[int(bill.TaskId)]; ok {
			tmp["task_name"] = task.Name
		}
		items = append(items, tmp)
	}

	result := map[string]any{
		"items": items,
		"total": total,
	}
	return helpers.SuccReturn(ctx, result)
}

// 获取优惠券发放记录
func CouponSendList(ctx context.Context, req ghttp.Request) ghttp.Response {
	params := helpers.GetBodyParams(ctx, req)
	limit := helpers.GetIntParam(params, "limit", 20)
	page := helpers.GetIntParam(params, "page", 1)

	// 参数检验
	limit, page = formatPaging(limit, page)

	// 获取数据
	couponM := models.Coupon{}
	total, err := couponM.Count(ctx, getUserId(ctx))
	if err != nil {
		return helpers.NewException(ctx, "DB_ERR")
	}
	couponList, err := couponM.GetUserList(ctx, getUserId(ctx), int(limit), int(page))
	if err != nil {
		return helpers.NewException(ctx, "DB_ERR")
	}

	// 数据组装
	var items []any
	for _, coupon := range couponList {
		items = append(items, coupon.SwapData())
	}

	result := map[string]any{
		"items": items,
		"total": total,
	}
	return helpers.SuccReturn(ctx, result)
}
