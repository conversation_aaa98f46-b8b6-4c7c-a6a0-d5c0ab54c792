package controllers

import (
	"bufio"
	"context"
	"encoding/json"
	"fmt"
	"math"
	"strings"

	"icode.baidu.com/baidu/gdp/ghttp"
	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/library/algorithm"
	"icode.baidu.com/helix_web/library/chpc"
	"icode.baidu.com/helix_web/library/ctxutils"
	"icode.baidu.com/helix_web/library/redis"
	"icode.baidu.com/helix_web/models"
	pb "icode.baidu.com/helix_web/proto/generate/proto"
	"icode.baidu.com/helix_web/services"
)

const (
	defaultAgabRecycle  = 3
	defaultAgabEnsemble = 2

	MAX_TOKEN_COUNT = 3000
)

type HF3AgabTask struct {
	JobName string `json:"job_name,omitempty"`
	Name    string `json:"name,omitempty"`
	Serial  string `json:"serial,omitempty"`
}

type HF3AgabConf struct {
	HF3AgabTask
	Cost             float64
	Tokens           uint64
	BillingUnitCount float64
}

// SubmitHF3Agab 提交helixfold3 agab任务，包括参数校验、数据组装和任务提交等操作
func SubmitHF3Agab(ctx context.Context, req ghttp.Request) ghttp.Response {
	// 获取参数
	params := helpers.GetBodyParams(ctx, req)
	serial := helpers.GetStringParam(params, "serial")
	name := helpers.GetStringParam(params, "name")
	isApi := 0
	if _, ok := ctx.Value("is_api").(int); ok {
		isApi = 1
	}
	// 检查账户状态
	if isApi == 1 {
		open := chpc.IsOpenCHPCService(ctxutils.GetIAMUserDomainID(ctx))
		if !open {
			return helpers.FailReturn(ctx, helpers.AccountErrorCode, "chpc service is not open")
		}
		debt, err := chpc.QueryIAMUserIsArrears(ctx, ctxutils.GetIAMUserDomainID(ctx))
		if err != nil {
			return helpers.FailReturn(ctx, helpers.AccountErrorCode, "query account fund status failure")
		}
		if debt {
			return helpers.FailReturn(ctx, helpers.AccountErrorCode, "account arrears，please recharge")
		}
	}

	// 参数校验
	name = getTaskName(name)
	if err := checkTaskCommonParam(serial, "", name); err != nil {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "task name is invalid")
	}
	// 提取蛋白序列
	sequences, err := parseFasta(serial)
	if err != nil {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "serial is invalid")
	}
	// 校验序列格式
	for _, seq := range sequences {
		if ok := checkProteinSequence(seq); !ok {
			return helpers.FailReturn(ctx, helpers.ParamErrorCode, "serial is invalid")
		}
	}
	// 计算token数量
	tokens := getAgabTokenCount(ctx, sequences)
	if tokens > MAX_TOKEN_COUNT {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "The range of token quantity for a single task is 1-3000.")
	}
	// 计算费用
	var (
		freezeCouponsStr string
		cost             float64
		billingUnitCount float64
		isSuccess        bool
	)
	// if isApi == 1 {
	// 	billingUnitCount = calculateT(tokens, defaultAgabEnsemble, defaultAgabRecycle)
	// 	cost = billingUnitCount * BILLING_UNIT_PRICE
	// 	freezeCouponsStr, cost, err = chargeUserBalance(ctx, cost, isApi)
	// 	defer func() {
	// 		if !isSuccess {
	// 			couponM := models.Coupon{}
	// 			_ = couponM.Return(ctx, freezeCouponsStr)
	// 		}
	// 	}()
	// 	if err != nil {
	// 		return helpers.FailReturn(ctx, helpers.AccountErrorCode, "compute task cost failure")
	// 	}
	// 	billingUnitCount = math.Ceil(cost / BILLING_UNIT_PRICE)
	// }

	newCtx := context.WithValue(ctx, "billing_unit_count", billingUnitCount)
	// 数据组装
	extendParams := models.TaskHF3AgabConf{
		TaskType: 383,
		RunMode:  "infer",
		FileUrl:  "",
		Smiles:   serial,
		DataType: "string",
	}
	extendParamsByte, _ := json.Marshal(extendParams)
	taskN := models.Task{
		Name:            name,
		UserId:          uint64(getUserId(ctx)),
		Type:            uint64(models.TaskTypeHF3Agab),
		FuncType:        uint64(models.FuncTypeForecast),
		Config:          string(extendParamsByte),
		IsApi:           uint64(isApi),
		IAMUserID:       ctxutils.GetIAMUserID(ctx),
		IAMUserDomainID: ctxutils.GetIAMUserDomainID(ctx),
		NTokens:         tokens,
		Coupons:         freezeCouponsStr,
		Balance:         billingUnitCount * BILLING_UNIT_PRICE,
	}
	taskNew, err := taskN.Add(ctx, taskN)
	defer func() {
		if !isSuccess {
			taskNew.Status = int64(models.TaskStatusFailed)
			_ = taskNew.Save(ctx)
		}
	}()
	if err != nil {
		return helpers.FailReturn(ctx, helpers.DBErrorCode, "create task failure")
	}

	// 提交任务
	lockKey := redis.UserBalanceLockPrefix + ctxutils.GetIAMUserDomainID(ctx)
	lockValue, ok := redis.Lock(ctx, lockKey, redis.UserBalanceLockExpireSeconds)
	if !ok {
		return helpers.FailReturn(ctx, helpers.RateLimitErrorCode, "request too fast")
	}
	balance, err := chpc.QueryIAMUserBalance(ctx, ctxutils.GetIAMUserDomainID(ctx))
	if err != nil {
		return helpers.FailReturn(ctx, helpers.AccountErrorCode, "query account balance failure")
	}
	if balance < cost {
		return helpers.FailReturn(ctx, helpers.AccountErrorCode, "account balance not enough")
	}
	_, err = services.PreSubmitTaskHF3Agab(newCtx, taskNew)
	if err != nil {
		return helpers.FailReturn(ctx, helpers.SubmitErrorCode, err.Error())
	}
	_ = redis.UnLock(ctx, lockKey, lockValue)

	// submit task success
	isSuccess = true
	result := map[string]any{
		"task_id": taskNew.ID,
	}
	return helpers.SuccReturn(ctx, result)
}

// BatchSubmitHF3Agab 批量提交任务
func BatchSubmitHF3Agab(ctx context.Context, req ghttp.Request) ghttp.Response {
	// 获取参数
	params := helpers.GetBodyParams(ctx, req)
	tasks := helpers.GetMapSliceParam(params, "tasks")
	if len(tasks) <= 0 {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "parm tasks is empty")
	}
	newTasks := assembleHF3AgabTask(tasks)
	if len(tasks) <= 0 {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "param tasks is empty")
	}
	if len(newTasks) > 20 {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "task count value out of range [1, 20]")
	}
	isApi := 0
	if _, ok := ctx.Value("is_api").(int); ok {
		isApi = 1
	}
	// 检查账户状态
	if err := checkAccountStatus(ctx, isApi); err != nil {
		return helpers.FailReturn(ctx, helpers.AccountErrorCode, err.Error())
	}
	// 计算费用
	var (
		taskIDs     = make([]uint64, 0)
		failTaskIDs = make([]uint64, 0)
		taskConfs   = make([]*HF3AgabConf, 0)
		taskCost    float64
	)
	for _, task := range newTasks {
		name := task.JobName
		if len(name) == 0 {
			name = task.Name
		}
		// 检查参数
		name = getTaskName(name)
		task.Name = name
		if err := checkTaskCommonParam(task.Serial, "", name); err != nil {
			return helpers.FailReturn(ctx, helpers.ParamErrorCode, "task param is invalid")
		}
		// 提取蛋白序列
		sequences, err := parseFasta(task.Serial)
		if err != nil {
			return helpers.FailReturn(ctx, helpers.ParamErrorCode, "serial is invalid")
		}
		// 校验序列格式
		for _, seq := range sequences {
			if ok := checkProteinSequence(seq); !ok {
				return helpers.FailReturn(ctx, helpers.ParamErrorCode, "serial is invalid")
			}
		}
		// 计算token数量
		tokens := getAgabTokenCount(ctx, sequences)
		if tokens > MAX_TOKEN_COUNT {
			return helpers.FailReturn(ctx, helpers.ParamErrorCode, "The range of token quantity for a single task is 1-3000.")
		}
		// 检查费用
		cost, billingUnitCount := 0.0, 0.0
		if isApi == 1 {
			billingUnitCount = calculateT(tokens, defaultAgabEnsemble, defaultAgabRecycle)
			cost = billingUnitCount * BILLING_UNIT_PRICE
		}
		taskConf := &HF3AgabConf{
			HF3AgabTask:      task,
			Cost:             cost,
			Tokens:           tokens,
			BillingUnitCount: billingUnitCount,
		}
		taskConfs = append(taskConfs, taskConf)
		taskCost += cost
	}
	// 校验余额是否充足，包括helix平台代金券、billing余额/代金券
	// if err := checkUserBalance(ctx, taskCost, isApi); err != nil {
	// 	return helpers.FailReturn(ctx, helpers.AccountErrorCode, err.Error())
	// }
	// 通过余额初步校验后，遍历提交任务
	freezeCouponsStrs := make([]string, 0)
	isSuccess := make([]bool, 0)
	defer func() {
		if isApi == 1 {
			couponM := models.Coupon{}
			for idx, freezeCouponStr := range freezeCouponsStrs {
				if taskIDs[idx] <= 0 || !isSuccess[idx] {
					_ = couponM.Return(ctx, freezeCouponStr)
				}
			}
			if len(failTaskIDs) > 0 {
				taskM := models.Task{}
				_ = taskM.BatchUpdate(ctx, failTaskIDs, map[string]any{"status": models.TaskStatusFailed})
			}
		}
	}()
	for _, taskConf := range taskConfs {
		var (
			freezeCouponsStr string
			billingUnitCount float64
			// cost             float64
			err error
		)
		// if isApi == 1 {
		// 	freezeCouponsStr, cost, err = chargeUserBalance(ctx, taskConf.Cost, isApi)
		// 	freezeCouponsStrs = append(freezeCouponsStrs, freezeCouponsStr)
		// 	if err != nil {
		// 		taskIDs = append(taskIDs, 0)
		// 		isSuccess = append(isSuccess, false)
		// 		continue
		// 	}
		// 	billingUnitCount = math.Ceil(cost / BILLING_UNIT_PRICE)
		// } else {
		// 	if ctxutils.GetUserInfo(ctx).Type == int64(models.UserTypeNormal) {
		// 		_ = checkNormalUserPerm(ctx, ctxutils.GetUserID(ctx), int64(models.TaskTypeHelixFoldAA))
		// 	}
		// }

		newCtx := context.WithValue(ctx, "billing_unit_count", billingUnitCount)
		// 组装数据
		extendParams := models.TaskHF3AgabConf{
			TaskType: 383,
			RunMode:  "infer",
			FileUrl:  "",
			Smiles:   taskConf.Serial,
			DataType: "string",
		}
		extendParamsByte, _ := json.Marshal(extendParams)
		taskN := models.Task{
			Name:            taskConf.Name,
			UserId:          uint64(getUserId(ctx)),
			Type:            uint64(models.TaskTypeHF3Agab),
			FuncType:        uint64(models.FuncTypeForecast),
			Config:          string(extendParamsByte),
			IsApi:           uint64(isApi),
			IAMUserID:       ctxutils.GetIAMUserID(ctx),
			IAMUserDomainID: ctxutils.GetIAMUserDomainID(ctx),
			NTokens:         taskConf.Tokens,
			Coupons:         freezeCouponsStr,
			Balance:         billingUnitCount * BILLING_UNIT_PRICE,
		}
		taskNew, err := taskN.Add(ctx, taskN)
		if err != nil {
			taskIDs = append(taskIDs, 0)
			isSuccess = append(isSuccess, false)
			continue
		}
		taskIDs = append(taskIDs, taskNew.ID)
		// 提交任务
		_, err = services.PreSubmitTaskHF3Agab(newCtx, taskNew)
		if err != nil {
			isSuccess = append(isSuccess, false)
			failTaskIDs = append(failTaskIDs, taskNew.ID)
			continue
		}
		isSuccess = append(isSuccess, true)
	}
	// 返回结果
	result := map[string]any{
		"task_ids": taskIDs,
	}
	return helpers.SuccReturn(ctx, result)
}

func checkAccountStatus(ctx context.Context, isApi int) error {
	open := chpc.IsOpenCHPCService(ctxutils.GetIAMUserDomainID(ctx))
	if isApi == 1 {
		if !open {
			return fmt.Errorf("chpc service is not open")
		}
		debt, err := chpc.QueryIAMUserIsArrears(ctx, ctxutils.GetIAMUserDomainID(ctx))
		if err != nil {
			return fmt.Errorf("query account fund status failure")
		}
		if debt {
			return fmt.Errorf("account arrears，please recharge")
		}
	} else {
		userType := ctxutils.GetUserInfo(ctx).Type
		if !open && userType != int64(models.UserTypeNormal) {
			return fmt.Errorf("chpc service is not open")
		}
		if open && userType == int64(models.UserTypeNormal) {
			// convert normal user if user open chpc service for web users
			userM := models.User{}
			user, err := userM.GetUserByID(ctx, ctxutils.GetUserID(ctx))
			if err != nil {
				return err
			}
			user.Type = uint64(models.UserTypeCharge)
			if err = user.Save(ctx); err != nil {
				return err
			}
			cacheKey := redis.IAMUserPrefix + ctxutils.GetUserInfo(ctx).IAMUserID
			_ = redis.Del(ctx, cacheKey)
			userInfo := ctxutils.GetUserInfo(ctx)
			userInfo.Type = int64(user.Type)
			ctx = context.WithValue(ctx, "user_info", userInfo)
			debt, err := chpc.QueryIAMUserIsArrears(ctx, ctxutils.GetIAMUserDomainID(ctx))
			if err != nil {
				return err
			}
			if debt {
				return fmt.Errorf("account arrears，please recharge")
			}
		}
	}
	return nil
}

// QuerySubmitHF3AgabTaskPrice 查询hf3agab任务金额
func QuerySubmitHF3AgabTaskPrice(ctx context.Context, req ghttp.Request) ghttp.Response {
	// 获取参数
	params := helpers.GetBodyParams(ctx, req)
	serial := helpers.GetStringParam(params, "serial")
	name := helpers.GetStringParam(params, "name")
	isApi := 0
	if _, ok := ctx.Value("is_api").(int); ok {
		isApi = 1
	}

	// 检查账户状态
	if isApi == 1 {
		open := chpc.IsOpenCHPCService(ctxutils.GetIAMUserDomainID(ctx))
		if !open {
			return helpers.FailReturn(ctx, helpers.AccountErrorCode, "chpc service is not open")
		}
		debt, err := chpc.QueryIAMUserIsArrears(ctx, ctxutils.GetIAMUserDomainID(ctx))
		if err != nil {
			return helpers.FailReturn(ctx, helpers.AccountErrorCode, "query account fund status failure")
		}
		if debt {
			return helpers.FailReturn(ctx, helpers.AccountErrorCode, "account arrears，please recharge")
		}
	}

	// 参数校验
	name = getTaskName(name)
	if err := checkTaskCommonParam(serial, "", name); err != nil {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "task name is invalid")
	}

	// 提取蛋白序列
	sequences, err := parseFasta(serial)
	if err != nil {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "serial is invalid")
	}

	// 校验序列格式
	for _, seq := range sequences {
		if ok := checkProteinSequence(seq); !ok {
			return helpers.FailReturn(ctx, helpers.ParamErrorCode, "serial is invalid")
		}
	}

	// 计算token数量
	tokens := getAgabTokenCount(ctx, sequences)
	if tokens > MAX_TOKEN_COUNT {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "The range of token quantity for a single task is 1-3000.")
	}

	// 计算费用
	var (
		cost             float64
		billingUnitCount float64
	)
	if isApi == 1 {
		billingUnitCount = calculateT(tokens, defaultAgabRecycle, defaultAgabRecycle)
		cost = billingUnitCount * BILLING_UNIT_PRICE
	}

	// 返回结果
	prices := make([]*TaskPriceInfo, 0)
	prices = append(prices, &TaskPriceInfo{
		Name:  name,
		Price: keepTwoDecimalPlaces(cost),
	})
	taskPriceInfos := TaskPriceInfos{
		Prices:     prices,
		TotalPrice: keepTwoDecimalPlaces(cost),
	}
	return helpers.SuccReturn(ctx, taskPriceInfos)
}

// QueryBatchSubmitHF3AgabTaskPrice 查询hf3agab多任务金额
func QueryBatchSubmitHF3AgabTaskPrice(ctx context.Context, req ghttp.Request) ghttp.Response {
	// get param from req
	params := helpers.GetBodyParams(ctx, req)
	tasks := helpers.GetMapSliceParam(params, "tasks")
	if len(tasks) <= 0 {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "parm tasks is empty")
	}
	newTasks := assembleHF3AgabTask(tasks)
	if len(tasks) <= 0 {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "param tasks is empty")
	}
	if len(newTasks) > 20 {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "task count value out of range [1, 20]")
	}
	isApi := 0
	if _, ok := ctx.Value("is_api").(int); ok {
		isApi = 1
	}
	// compute task cost
	totalPrice := 0.0
	prices := make([]*TaskPriceInfo, 0)
	for _, task := range newTasks {
		name := task.JobName
		if len(name) == 0 {
			name = task.Name
		}
		// check common param
		name = getTaskName(name)
		task.Name = name
		if err := checkTaskCommonParam(task.Serial, "", name); err != nil {
			return helpers.FailReturn(ctx, helpers.ParamErrorCode, "task param is invalid")
		}
		// 提取蛋白序列
		sequences, err := parseFasta(task.Serial)
		if err != nil {
			return helpers.FailReturn(ctx, helpers.ParamErrorCode, "serial is invalid")
		}
		// 校验序列格式
		for _, seq := range sequences {
			if ok := checkProteinSequence(seq); !ok {
				return helpers.FailReturn(ctx, helpers.ParamErrorCode, "serial is invalid")
			}
		}
		// 计算token数量
		tokens := getAgabTokenCount(ctx, sequences)
		if tokens > MAX_TOKEN_COUNT {
			return helpers.FailReturn(ctx, helpers.ParamErrorCode, "The range of token quantity for a single task is 1-3000.")
		}
		// check cost
		cost, billingUnitCount := 0.0, 0.0
		if isApi == 1 {
			billingUnitCount = calculateT(tokens, defaultAgabRecycle, defaultAgabRecycle)
			cost = billingUnitCount * BILLING_UNIT_PRICE
			cost = math.Ceil(cost/BILLING_UNIT_PRICE) * BILLING_UNIT_PRICE
		}
		cost = keepTwoDecimalPlaces(cost)
		prices = append(prices, &TaskPriceInfo{
			Name:  name,
			Price: cost,
		})
		totalPrice += cost
	}
	// 返回结果
	couponM := models.Coupon{}
	coupon, err := couponM.QueryAvailableCouponAmountForTask(ctx, ctxutils.GetUserID(ctx), models.TaskTypeHelixFoldAA)
	if err != nil {
		return helpers.FailReturn(ctx, helpers.DBErrorCode, "query available coupon failure")
	}
	_ = math.Max(totalPrice-coupon, 0)
	taskPriceInfos := TaskPriceInfos{
		Prices:     prices,
		TotalPrice: keepTwoDecimalPlaces(totalPrice),
	}
	return helpers.SuccReturn(ctx, taskPriceInfos)
}

// parseFasta 将fasta格式的序列提取出来
func parseFasta(fastaData string) ([]string, error) {
	var sequences []string
	var currentSeqID string
	var currentSeq string
	scanner := bufio.NewScanner(strings.NewReader(fastaData))

	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())
		if len(line) == 0 {
			continue
		}
		// 以 '>' 开头的是序列ID
		if line[0] == '>' {
			// 如果有当前序列，先保存
			if currentSeqID != "" {
				sequences = append(sequences, currentSeq)
				currentSeq = ""
			}
			// 提取序列ID（去掉 '>' 符号）
			currentSeqID = line[1:]
		} else {
			// 累加序列数据
			currentSeq += line
		}
	}
	// 添加最后一个序列
	if currentSeqID != "" {
		sequences = append(sequences, currentSeq)
	}

	return sequences, scanner.Err()
}

// checkHF3ProteinSequence 检查蛋白质序列是否合法，如果合法则返回true；如果不合法则返回false
func checkProteinSequence(sequence string) bool {
	if len(sequence) == 0 {
		return false
	}
	for _, token := range sequence {
		if _, ok := aminoAcidTable[token]; !ok {
			return false
		}
	}
	return true
}

// getAgabTokenCount 获取token数量
func getAgabTokenCount(ctx context.Context, sequences []string) uint64 {
	var pbEntities []*pb.Entity
	for _, seq := range sequences {
		pbEntity := pb.Entity{
			Type:     entityTypeProtein,
			Count:    1,
			Sequence: seq,
		}
		pbEntities = append(pbEntities, &pbEntity)
	}
	request := &pb.GetEntitiesTokenCountRequest{
		Entities: pbEntities,
	}
	resp, err := algorithm.GetSerialServiceClient().GetEntitiesTokenCount(ctx, request)
	if err != nil {
		helpers.HelixNotice(ctx, "----query entities token count from helix-algorithm-support service err---"+err.Error())
		return 0
	}
	return resp.Count
}

// assembleHF3AgabTask 将任务列表中的任务转换为HF3AgabTask类型，并返回新的任务列表
func assembleHF3AgabTask(tasks []any) []HF3AgabTask {
	var newTasks []HF3AgabTask
	for _, task := range tasks {
		// 解析name、recycle、ensemble
		t, _ := task.(map[string]any)
		var tt HF3AgabTask
		_ = mapToStruct(t, &tt)
	}
	return newTasks
}
