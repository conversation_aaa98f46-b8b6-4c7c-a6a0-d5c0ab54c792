package controllers

import (
	"context"
	"encoding/json"
	"errors"
	"strings"
	"time"

	"icode.baidu.com/baidu/gdp/ghttp"

	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/models"
	"icode.baidu.com/helix_web/services"
)

const (
	ResIdsLimit         = 200
	AdmetApiSerialLimit = 100
)

// SubmitSelfAdmet SubmitAdmet 提交预测
func SubmitSelfAdmet(ctx context.Context, req ghttp.Request) ghttp.Response {
	params := helpers.GetBodyParams(ctx, req)
	fileUrl := helpers.GetStringParam(params, "file_url")
	serial := helpers.GetStringParam(params, "serial")
	name := helpers.GetStringParam(params, "name")
	modelList := helpers.GetIntSliceParam(params, "model_list")
	taskId := helpers.GetIntParam(params, "task_id", 0)

	// 参数校验
	name = getTaskName(name)
	err := checkTaskCommonParam(serial, fileUrl, name)
	if err != nil {
		return helpers.NewException(ctx, "PARAM_VALUE_ERR", err.Error())
	}
	if taskId <= 0 {
		return helpers.NewException(ctx, "PARAM_VALUE_ERR", "task_id")
	}
	if len(modelList) > 100 {
		return helpers.NewException(ctx, "PARAM_VALUE_ERR", "model_list")
	}

	// config 检验
	conf, contentStr, err := getCommonTaskConf(serial, fileUrl)
	if err != nil {
		return helpers.NewException(ctx, "PARAM_VALUE_ERR", err.Error())
	}
	var needConfig models.TaskAdmetConf
	needConfig.Serial = conf.Serial
	needConfig.FileUrl = conf.FileUrl
	needConfig.ModelList = modelList

	// 判断自训练数据
	chargeTab := models.ChargeTabNo
	_, err = CheckUserPermission(ctx, int64(models.TaskTypeSelfAdmet), false)
	if err != nil {
		return helpers.NewException(ctx, "PARAM_VALUE_ERR", err.Error())
	}

	modelPath, err := getTrainModelPath(ctx, taskId)
	if err != nil {
		return helpers.NewException(ctx, "PARAM_VALUE_ERR", err.Error())
	}
	needConfig.ModelPath = modelPath
	needConfig.Level = services.Level1
	needConfig.Number = getSerialNumber(contentStr)

	// 数据组装
	configByte, _ := json.Marshal(needConfig)
	taskN := models.Task{
		Name:      name,
		UserId:    uint64(getUserId(ctx)),
		Type:      uint64(models.TaskTypeSelfAdmet),
		FuncType:  uint64(models.FuncTypeForecast),
		Config:    string(configByte),
		ChargeTab: uint64(chargeTab),
		ParentID:  uint64(taskId),
		IsExample: uint64(checkSerialExample(ctx, int64(models.TaskTypeSelfAdmet), contentStr)),
	}

	taskNew, err := taskN.Add(ctx, taskN)
	if err != nil {
		return helpers.NewException(ctx, "DB_ERR")
	}

	// 提交任务到调度
	services.PreSubmitTask(ctx, taskNew)
	result := map[string]any{
		"task_id": taskNew.ID,
	}
	return helpers.SuccReturn(ctx, result)
}

// SubmitAdmet 提交预测
func SubmitAdmet(ctx context.Context, req ghttp.Request) ghttp.Response {
	params := helpers.GetBodyParams(ctx, req)
	fileUrl := helpers.GetStringParam(params, "file_url")
	serial := helpers.GetStringParam(params, "serial")
	name := helpers.GetStringParam(params, "name")
	isApi := 0
	if _, ok := ctx.Value("is_api").(int); ok {
		isApi = 1
	}
	if isApi > 0 {
		serial = helpers.GetStringParam(params, "smiles")
	}

	// 参数校验
	name = getTaskName(name)
	err := checkTaskCommonParam(serial, fileUrl, name)
	if err != nil {
		return helpers.NewException(ctx, "PARAM_VALUE_ERR", err.Error())
	}

	// config 检验
	conf, contentStr, err := getCommonTaskConf(serial, fileUrl)
	if err != nil {
		return helpers.NewException(ctx, "PARAM_VALUE_ERR", err.Error())
	}
	var needConfig models.TaskAdmetConf
	needConfig.Serial = conf.Serial
	needConfig.FileUrl = conf.FileUrl

	// 判断自训练数据
	chargeP := chargeParam{IsCharge: false}
	if isApi > 0 {
		chargeP.IsApi = true
	}
	chargeTab, err := checkCharge(ctx, int64(models.TaskTypeAdmet), services.Level1, chargeP)
	if err != nil {
		return helpers.NewException(ctx, "PARAM_VALUE_ERR", err.Error())
	}
	needConfig.Level = services.Level1
	needConfig.Number = getSerialNumber(contentStr)
	if isApi > 0 && needConfig.Number > AdmetApiSerialLimit {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "limit 100 serial")
	}

	// 数据组装
	configByte, _ := json.Marshal(needConfig)
	taskN := models.Task{
		Name:      name,
		UserId:    uint64(getUserId(ctx)),
		Type:      uint64(models.TaskTypeAdmet),
		FuncType:  uint64(models.FuncTypeForecast),
		Config:    string(configByte),
		ChargeTab: uint64(chargeTab),
		IsApi:     uint64(isApi),
		IsExample: uint64(checkSerialExample(ctx, int64(models.TaskTypeAdmet), contentStr)),
	}

	taskNew, err := taskN.Add(ctx, taskN)
	if err != nil {
		return helpers.NewException(ctx, "DB_ERR")
	}

	// 提交任务到调度
	services.PreSubmitTask(ctx, taskNew)
	result := map[string]any{
		"task_id": taskNew.ID,
	}
	return helpers.SuccReturn(ctx, result)
}

// SearchResult 预测结果
func SearchResult(ctx context.Context, req ghttp.Request) ghttp.Response {
	params := helpers.GetBodyParams(ctx, req)
	limit := helpers.GetIntParam(params, "limit", 10)
	page := helpers.GetIntParam(params, "page", 1)
	taskId := helpers.GetIntParam(params, "task_id", 0)
	searchMap := helpers.GetMapParam(params, "content")
	if taskId <= 0 {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "task_id error")
	}
	taskM := models.Task{}
	taskInfo, err := taskM.GetTaskByUserAndId(ctx, getUserId(ctx), taskId)
	if taskInfo.ID <= 0 {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "task_id error")
	}

	searchCond := getSearchCond(searchMap)

	// 获取数据
	taskResultM := models.TaskResult{}
	total, err := taskResultM.CountByCond(ctx, taskId, searchCond)
	if err != nil {
		return helpers.NewException(ctx, "DB_ERR")
	}

	resList, err := taskResultM.GetListByCond(ctx, taskId, searchCond, int(limit), int(page), "id asc")
	if err != nil {
		return helpers.NewException(ctx, "DB_ERR")
	}

	// 数据组装
	var items []any
	for _, resData := range resList {
		items = append(items, resData.SwapData())
	}

	result := map[string]any{
		"total": total,
		"items": items,
	}
	return helpers.SuccReturn(ctx, result)
}

// 获取 download 数据
func DownloadResult(ctx context.Context, req ghttp.Request) ghttp.Response {
	params := helpers.GetBodyParams(ctx, req)
	ids := helpers.GetIntSliceParam(params, "ids")
	notids := helpers.GetIntSliceParam(params, "notids")
	taskId := helpers.GetIntParam(params, "task_id", 0)
	searchMap := helpers.GetMapParam(params, "content")
	if taskId <= 0 {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "task_id error")
	}
	if len(ids) > ResIdsLimit {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "ids num error")
	}
	searchCond := getSearchCond(searchMap)

	taskM := models.Task{}
	taskInfo, err := taskM.GetTaskByUserAndId(ctx, getUserId(ctx), taskId)
	if taskInfo.ID <= 0 {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "task_id error")
	}

	var resList []models.TaskResult
	taskResultM := models.TaskResult{}
	if len(ids) > 0 {
		resList, err = taskResultM.GetListByIds(ctx, ids)
		if err != nil {
			return helpers.NewException(ctx, "DB_ERR")
		}
	} else {
		resList, err = taskResultM.GetListByCond(ctx, taskId, searchCond, 1000, 1, "id asc")
		if err != nil {
			return helpers.NewException(ctx, "DB_ERR")
		}
	}

	notidsMap := make(map[int64]bool)
	if len(notids) > 0 {
		for _, val := range notids {
			notidsMap[val] = true
		}
	}

	// 数据组装
	var items []any
	for _, resData := range resList {
		if len(ids) == 0 && len(notidsMap) > 0 {
			has := notidsMap[int64(resData.ID)]
			if has {
				continue
			}
		}
		items = append(items, resData.SwapData())
	}

	result := map[string]any{
		"items": items,
	}
	return helpers.SuccReturn(ctx, result)
}

// 获取搜索内容
func getSearchCond(searchMap map[string]any) models.ResultSearchCond {
	var searchCond models.ResultSearchCond
	searchByte, err := json.Marshal(searchMap)
	if err != nil {
		return searchCond
	}
	if err := json.Unmarshal(searchByte, &searchCond); err != nil {
		return searchCond
	}

	return searchCond
}

// 获取训练模型地址
func getTrainModelPath(ctx context.Context, taskId int64) (string, error) {
	taskM := models.Task{}
	trainTask, err := taskM.GetTaskByUserAndId(ctx, getUserId(ctx), taskId)
	if err != nil {
		return "", errors.New("data error, please repeat")
	}

	// 判断自训练的类型及训练状态
	if trainTask.FuncType != uint64(models.FuncTypeTrain) ||
		trainTask.Status != int64(models.TaskStatusSucc) {
		return "", errors.New("task_id param error")
	}

	var taskResult map[string]any
	_ = json.Unmarshal([]byte(trainTask.Result), &taskResult)
	if len(taskResult) == 0 {
		return "", errors.New("task_id param error")
	}

	// 修改应用时间
	trainTask.UseTime = time.Now()
	err = trainTask.Save(ctx)
	if err != nil {
		return "", errors.New("data error, please repeat")
	}

	modelPath := taskResult["download_url"].(string)
	if len(modelPath) <= 0 {
		modelPath = taskResult["file_url"].(string)
	}

	return modelPath, nil
}

func getSerialNumber(contentStr string) int64 {
	number := 0
	contentArr := strings.Split(contentStr, "\n")
	for _, item := range contentArr {
		if len(helpers.Trim(item)) > 0 {
			number++
		}
	}

	return int64(number)
}
