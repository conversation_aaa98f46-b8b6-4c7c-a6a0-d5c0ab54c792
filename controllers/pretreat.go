package controllers

import (
	"context"
	"encoding/json"

	"icode.baidu.com/baidu/gdp/ghttp"

	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/models"
	"icode.baidu.com/helix_web/services"
)

const (
	KeywordLenLimit int = 100
)

// 搜索蛋白质pdb序列
func SearchProtein(ctx context.Context, req ghttp.Request) ghttp.Response {
	params := helpers.GetBodyParams(ctx, req)
	keyword := helpers.GetStringParam(params, "keyword")
	if len(keyword) == 0 || len(keyword) > KeywordLenLimit {
		return helpers.SuccReturn(ctx, nil)
	}

	proteinM := models.ProteinLib{}
	proteinList, err := proteinM.GetList(ctx, keyword, models.ProteinSearchLimit)
	if err != nil {
		return helpers.FailReturn(ctx, helpers.LogicErrorCode, "data error, repeat")
	}

	// 数据组装
	var items []string
	for _, resData := range proteinList {
		items = append(items, resData.Name)
	}

	result := map[string]any{
		"items": items,
	}
	return helpers.SuccReturn(ctx, result)
}

// 提交预处理
func SubmitPretreat(ctx context.Context, req ghttp.Request) ghttp.Response {
	params := helpers.GetBodyParams(ctx, req)
	fileUrl := helpers.GetStringParam(params, "file_url")
	serial := helpers.GetStringParam(params, "serial")
	tType := helpers.GetIntParam(params, "type", 0)

	// 参数校验
	if !checkPretreatType(int(tType)) {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "type param error")
	}

	// 上传文件校验
	err := checkTaskCommonParam(serial, fileUrl, "")
	if err != nil {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "param error")
	}
	needConfig, _, err := getCommonTaskConf(serial, fileUrl)
	if err != nil {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "param error")
	}

	// 数据组装
	config := models.PretreatConf{
		FileUrl: needConfig.FileUrl,
		Serial:  needConfig.Serial,
	}
	configByte, _ := json.Marshal(config)
	pretreatN := models.Pretreat{
		UserId: uint64(getUserId(ctx)),
		Type:   uint64(tType),
		Config: string(configByte),
	}

	pretreatNew, err := pretreatN.Add(ctx, pretreatN)
	if err != nil {
		return helpers.NewException(ctx, "DB_ERR")
	}

	// 提交任务到调度
	resp, err := services.SubmitPretreat(ctx, pretreatNew)
	if err != nil {
		go helpers.HelixNotice(ctx, "---pretreat submit scheduler failed---"+err.Error())
		pretreatNew.Status = int64(models.StatusDel)
		_ = pretreatNew.Save(ctx)

		return helpers.NewException(ctx, "DB_ERR")
	}
	pretreatNew.ServerTaskId = uint64(resp.TaskId)
	_ = pretreatNew.Save(ctx)

	// 返回数据
	result := map[string]any{
		"pretreat_id": pretreatNew.ID,
	}
	return helpers.SuccReturn(ctx, result)
}

// 获取预处理信息
func GetPretreatInfo(ctx context.Context, req ghttp.Request) ghttp.Response {
	params := helpers.GetBodyParams(ctx, req)
	pretreatId := helpers.GetIntParam(params, "pretreat_id", 0)
	if pretreatId <= 0 {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "pretreat_id param error")
	}

	pretreatM := models.Pretreat{}
	pretreatData, err := pretreatM.GetByUserAndId(ctx, getUserId(ctx), pretreatId)
	if err != nil {
		return helpers.NewException(ctx, "DB_ERR")
	}

	// 返回数据
	result := pretreatData.SwapData()
	return helpers.SuccReturn(ctx, result)
}
