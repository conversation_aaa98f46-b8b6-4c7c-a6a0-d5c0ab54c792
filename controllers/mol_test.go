package controllers

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http/httptest"
	"testing"
	"time"

	"icode.baidu.com/baidu/gdp/ghttp"
	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/models"
)

// TestSubmitMolActSerial 测试SubmitMolActSerial函数，该函数用于提交药物活性序列化任务
// 参数t：*testing.T类型，表示当前测试用例的对象指针
// 返回值：无
func TestSubmitMolActSerial(t *testing.T) {
	ctx := context.Background()
	ctx = context.WithValue(ctx, "user_info", models.UserInfo{UserID: 1, Type: 1})

	input2, _ := json.Marshal(map[string]interface{}{
		"file_url": "bos:/bml-test-test/helix_upload/fb5d8890-d7b0-41e0-ada1-da73270dba9b.txt",
		"protein":  "dsdsf",
		"config": map[string]interface{}{
			"use_bingdingdb": false,
			"use_chembl":     true,
		},
	})
	input3, _ := json.Marshal(map[string]interface{}{
		"name": "test002",
	})
	input4, _ := json.Marshal(map[string]interface{}{
		"file_url": "xxx",
		"name":     "test002001",
	})

	caseList := []struct {
		input  io.Reader
		output int
	}{
		{nil, helpers.ParamErrorCode},
		{bytes.NewReader(input2), helpers.SuccessCode},
		{bytes.NewReader(input3), helpers.ParamErrorCode},
		{bytes.NewReader(input4), helpers.ParamErrorCode},
	}

	for _, v := range caseList {
		raw := httptest.NewRequest("POST", "/", v.input)
		req := ghttp.NewRequest(time.Now(), raw)

		resp := SubmitMolActSerial(ctx, req).(*ghttp.JSONResponse)
		data := resp.Data.(helpers.JsonResp)
		if data.Code != v.output {
			fmt.Println(data)
			t.Errorf("code=%d, want=%d", data.Code, v.output)
		}
	}
}

// TestSubmitHelixVS TestSubmitHelixVS 是一个测试函数，用于测试SubmitHelixVS函数的功能。
// 该函数接受一个*testing.T类型的参数，返回值为void。
// 在测试过程中，会创建一个context对象，并设置一些额外的信息到context中，然后根据不同的输入情况，生成不同的请求对象，调用SubmitHelixVS函数进行处理，并检查返回结果是否符合预期。
func TestSubmitHelixVS(t *testing.T) {
	ctx := context.Background()
	ctx = context.WithValue(ctx, "user_info", models.UserInfo{UserID: 1, Type: 1})

	input1, _ := json.Marshal(map[string]interface{}{
		"name": "test001",
	})
	input3, _ := json.Marshal(map[string]interface{}{
		"pdb_url":  "bos:/bml-test-test/helix_upload/fb5d8890-d7b0-41e0-ada1-da73270dba9b.txt",
		"pdb_name": "test002",
	})
	input4, _ := json.Marshal(map[string]interface{}{
		"pdb_url":  "bos:/bml-test-test/helix_upload/fb5d8890-d7b0-41e0-ada1-da73270dba9b.txt",
		"pdb_name": "test002",
		"config": map[string]interface{}{
			"center_x":      23.45,
			"center_y":      23.45,
			"center_z":      23.25,
			"size_x":        23.45,
			"size_y":        23.45,
			"size_z":        23.45,
			"pocket_source": "234",
			"protein_type":  models.MolProteinTypeTarget,
		},
	})

	caseList := []struct {
		input  io.Reader
		output int
	}{
		{nil, helpers.ParamErrorCode},
		{bytes.NewReader(input1), helpers.ParamErrorCode},
		{bytes.NewReader(input3), helpers.SuccessCode},
		{bytes.NewReader(input4), helpers.SuccessCode},
	}

	for _, v := range caseList {
		raw := httptest.NewRequest("POST", "/", v.input)
		req := ghttp.NewRequest(time.Now(), raw)

		resp := SubmitHelixVS(ctx, req).(*ghttp.JSONResponse)
		data := resp.Data.(helpers.JsonResp)
		if data.Code != v.output {
			fmt.Println(data)
			t.Errorf("code=%d, want=%d", data.Code, v.output)
		}
	}
}

// TestSubmitMolFormation 测试SubmitMolFormation函数，该函数用于提交蛋白质形态的请求。
// 参数t是*testing.T类型，表示当前测试用例。
// 返回值没有，使用t.Errorf进行错误输出。
func TestSubmitMolFormation(t *testing.T) {
	ctx := context.Background()
	ctx = context.WithValue(ctx, "user_info", models.UserInfo{UserID: 1, Type: 1})

	input1 := map[string]interface{}{
		"name":       "xxs",
		"serial":     "test",
		"based_type": "ligand",
	}
	input1Byte, _ := json.Marshal(input1)

	input2 := map[string]interface{}{
		"name":       "xxs",
		"pdb_url":    "bos:/ss/ss",
		"pdb_name":   "bos:/ss/ss",
		"serial":     "test",
		"based_type": "target",
		"config": map[string]float64{
			"center_x": 23.45,
			"center_y": 23.45,
			"center_z": 23.25,
			"size_x":   23.45,
			"size_y":   23.45,
			"size_z":   23.45,
		},
	}
	input2Byte, _ := json.Marshal(input2)
	input3 := map[string]interface{}{
		"name":       "xxs",
		"pdb_url":    "bos:/ss/ss",
		"pdb_name":   "bos:/ss/ss",
		"serial":     "test",
		"based_type": "target",
		"pdb_url2":   "bos:/ss/ss",
		"pdb_name2":  "bos:/ss/ss",
		"config": map[string]interface{}{
			"center_x":      23.45,
			"center_y":      23.45,
			"center_z":      23.25,
			"size_x":        23.45,
			"size_y":        23.45,
			"size_z":        23.45,
			"pocket_source": "234",
			"protein_type":  models.MolProteinTypeTarget,
		},
		"config2": map[string]interface{}{
			"center_x":      23.45,
			"center_y":      23.45,
			"center_z":      23.25,
			"size_x":        23.45,
			"size_y":        23.45,
			"size_z":        23.45,
			"pocket_source": "234",
			"protein_type":  models.MolProteinTypeNotTarget,
		},
	}
	input3Byte, _ := json.Marshal(input3)

	caseList := []struct {
		input  io.Reader
		output int
	}{
		{nil, helpers.ParamErrorCode},
		{bytes.NewReader(input1Byte), helpers.ParamErrorCode},
		{bytes.NewReader(input2Byte), helpers.SuccessCode},
		{bytes.NewReader(input3Byte), helpers.SuccessCode},
	}

	for _, v := range caseList {
		raw := httptest.NewRequest("POST", "/", v.input)
		req := ghttp.NewRequest(time.Now(), raw)

		resp := SubmitMolFormation(ctx, req).(*ghttp.JSONResponse)
		data := resp.Data.(helpers.JsonResp)
		if data.Code != v.output {
			t.Errorf("code=%d, want=%d", data.Code, v.output)
		}
	}
}

// TestSubmitMolActStruct 测试SubmitMolActStruct函数，该函数用于提交药物活性结构体信息。
// 参数t：*testing.T类型，表示当前测试用例的对象指针。
// 返回值：无返回值
func TestSubmitMolActStruct(t *testing.T) {
	ctx := context.Background()
	ctx = context.WithValue(ctx, "user_info", models.UserInfo{UserID: 1, Type: 1})

	input1 := map[string]interface{}{
		"name":    "xxs",
		"mol_url": "bos:/bml-test-test/helix_upload/866fae6a-b842-4b0b-a6b8-7bbb366afdd8.txt",
	}
	input1Byte, _ := json.Marshal(input1)

	input2 := map[string]interface{}{
		"name":        "xxs",
		"protein_url": "bos:/bml-test-test/helix_upload/866fae6a-b842-4b0b-a6b8-7bbb366afdd8.txt",
	}
	input2Byte, _ := json.Marshal(input2)
	input3 := map[string]interface{}{
		"name":        "xxs",
		"mol_url":     "bos:/bml-test-test/helix_upload/866fae6a-b842-4b0b-a6b8-7bbb366afdd8.txt",
		"protein_url": "bos:/bml-test-test/helix_upload/866fae6a-b842-4b0b-a6b8-7bbb366afdd8.txt",
	}
	input3Byte, _ := json.Marshal(input3)

	caseList := []struct {
		input  io.Reader
		output int
	}{
		{nil, helpers.ParamErrorCode},
		{bytes.NewReader(input1Byte), helpers.ParamErrorCode},
		{bytes.NewReader(input2Byte), helpers.ParamErrorCode},
		{bytes.NewReader(input3Byte), helpers.SuccessCode},
	}

	for _, v := range caseList {
		raw := httptest.NewRequest("POST", "/", v.input)
		req := ghttp.NewRequest(time.Now(), raw)

		resp := SubmitMolActStruct(ctx, req).(*ghttp.JSONResponse)
		data := resp.Data.(helpers.JsonResp)
		if data.Code != v.output {
			t.Errorf("code=%d, want=%d", data.Code, v.output)
		}
	}
}

// TestSubmitMolDocking 测试SubmitMolDocking函数，该函数用于提交分子坐标点击探针任务
// 参数t：*testing.T类型，表示当前测试用例的上下文信息
// 返回值：无
func TestSubmitMolDocking(t *testing.T) {
	ctx := context.Background()
	ctx = context.WithValue(ctx, "user_info", models.UserInfo{UserID: 1, Type: 1})

	input1 := map[string]interface{}{
		"name":    "xxs",
		"pdb_url": "bos:/ss/ss",
	}
	input1Byte, _ := json.Marshal(input1)

	input2 := map[string]interface{}{
		"file_url": "bos:/ss/ss",
		"pdb_url":  "bos:/ss/ss",
	}
	input2Byte, _ := json.Marshal(input2)

	input3 := map[string]interface{}{
		"serial":   "test",
		"pdb_name": "test001",
		"pdb_url":  "bos:/ss/ss",
		"config": map[string]float64{
			"center_x": 23.45,
			"center_y": 23.45,
			"center_z": 23.25,
			"size_x":   23.45,
			"size_y":   23.45,
			"size_z":   23.45,
		},
	}
	input3Byte, _ := json.Marshal(input3)

	caseList := []struct {
		input  io.Reader
		output int
	}{
		{nil, helpers.ParamErrorCode},
		{bytes.NewReader(input1Byte), helpers.ParamErrorCode},
		{bytes.NewReader(input2Byte), helpers.ParamErrorCode},
		{bytes.NewReader(input3Byte), helpers.SuccessCode},
	}

	for _, v := range caseList {
		raw := httptest.NewRequest("POST", "/", v.input)
		req := ghttp.NewRequest(time.Now(), raw)

		resp := SubmitMolDocking(ctx, req).(*ghttp.JSONResponse)
		data := resp.Data.(helpers.JsonResp)
		if data.Code != v.output {
			fmt.Println(data)
			t.Errorf("code=%d, want=%d", data.Code, v.output)
		}
	}
}
