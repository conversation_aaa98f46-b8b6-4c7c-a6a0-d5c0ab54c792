package controllers

import (
	"context"
	"encoding/json"
	"errors"
	"os"
	"strconv"
	"strings"
	"time"

	"icode.baidu.com/baidu/gdp/env"
	"icode.baidu.com/baidu/gdp/ghttp"

	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/library/chpc"
	"icode.baidu.com/helix_web/library/ctxutils"
	"icode.baidu.com/helix_web/library/redis"
	"icode.baidu.com/helix_web/models"
	"icode.baidu.com/helix_web/services"
)

const (
	ContentLengthLimit = 1024
	NameLengthLimit    = 64
	EmailLengthLimit   = 128
	PhoneLengthLimit   = 13
	CompanyLengthLimit = 128

	CaptchaLength      = 4
	CaptchaTokenLength = 20

	CameoTaskLimit = 200

	MaxLimitNum  = 500
	MaxPageNum   = 100
	DefaultLimit = 3
	DefaultPage  = 1

	EmailCodeLimit     = 6
	UserSubscribeLimit = 10
)

// 获取动态栏列表
func VerifyGithub(ctx context.Context, req ghttp.Request) ghttp.Response {
	params := helpers.GetBodyParams(ctx, req)
	code := helpers.GetStringParam(params, "code")
	if code == "" {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "code is empty")
	}

	// 获取令牌
	accessToken, err := services.GitAccessToken(ctx, code)
	if err != nil || accessToken == "" {
		return helpers.FailReturn(ctx, helpers.LogicErrorCode, "get access token error, please repeat")
	}

	// 获取用户信息
	userResp, err := services.GitUserInfo(ctx, accessToken)
	if err != nil || userResp.LoginName == "" {
		return helpers.FailReturn(ctx, helpers.LogicErrorCode, "get github user error, please repeat")
	}

	// 入库
	userM := models.User{}
	userInfo, err := userM.GetUserByRealID(ctx, userResp.Id, "", int64(models.SourceGitHub))
	if err != nil {
		return helpers.NewException(ctx, "DB_ERR")
	}
	if userInfo.ID == 0 {
		displayName := userResp.Name
		if displayName == "" {
			displayName = userResp.LoginName
		}
		userNew := models.User{
			Username:    userResp.LoginName,
			DisplayName: displayName,
			RealID:      uint64(userResp.Id),
			Type:        uint64(models.UserTypeNormal),
			Source:      uint64(models.SourceGitHub),
		}
		_, _ = userNew.Add(ctx, userNew)
	}

	// 写cookie
	cookieValue := helpers.GetRandomString(20)
	redis.Set(ctx, redis.UserCookiePrefix+cookieValue, userInfo.ID, 86400)

	result := map[string]string{
		"cookie": cookieValue,
	}
	return helpers.SuccReturn(ctx, result)
}

// 获取动态栏列表
func GetFeedbarShow(ctx context.Context, req ghttp.Request) ghttp.Response {
	params := helpers.GetBodyParams(ctx, req)
	limit := helpers.GetIntParam(params, "limit", 0)

	// 参数check
	if limit <= 0 || limit > MaxLimitNum {
		limit = DefaultLimit
	}

	feedbarM := models.Feedbar{}
	feedbarList, err := feedbarM.GetList(ctx, int(limit), 1, "sort asc, display_time desc")
	if err != nil {
		return helpers.NewException(ctx, "DB_ERR")
	}

	// 数据组装
	var items []any
	for _, feedbar := range feedbarList {
		items = append(items, feedbar.SwapData())
	}

	result := map[string]any{
		"items": items,
	}
	return helpers.SuccReturn(ctx, result)
}

// 获取学术成果列表
func GetAchievementShow(ctx context.Context, req ghttp.Request) ghttp.Response {
	params := helpers.GetBodyParams(ctx, req)
	limit := helpers.GetIntParam(params, "limit", 0)

	// 参数check
	if limit <= 0 || limit > MaxLimitNum {
		limit = DefaultLimit
	}

	achievementM := models.Achievement{}
	achievementList, err := achievementM.GetList(ctx, int(limit), 1)
	if err != nil {
		return helpers.NewException(ctx, "DB_ERR")
	}

	// 数据组装
	var items []any
	for _, achievement := range achievementList {
		items = append(items, achievement.SwapData())
	}

	result := map[string]any{
		"items": items,
	}
	return helpers.SuccReturn(ctx, result)
}

// 获取序列列表
func GetSerialList(ctx context.Context, req ghttp.Request) ghttp.Response {
	params := helpers.GetBodyParams(ctx, req)
	tType := helpers.GetIntParam(params, "type", 0)
	isAll := helpers.GetBoolParam(params, "is_all", false)
	funcType := helpers.GetIntParam(params, "func_type", 0)
	dataType := helpers.GetIntParam(params, "data_type", 0)
	if !checkSerialType(int(tType)) {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "type param error")
	}
	// 如果是拿所有就不传这俩参数了
	if !isAll {
		if !checkSerialFuncType(int(funcType)) {
			return helpers.FailReturn(ctx, helpers.ParamErrorCode, "func_type param error")
		}
		if !checkSerialDataType(int(dataType)) {
			return helpers.FailReturn(ctx, helpers.ParamErrorCode, "data_type param error")
		}
	}

	// 从数据库获取数据信息
	serialM := models.Serial{}
	serialList, err := serialM.GetList(ctx, tType, funcType, dataType)
	if err != nil {
		return helpers.NewException(ctx, "DB_ERR")
	}

	// 数据组装
	var items []any
	for _, serial := range serialList {
		items = append(items, serial.SwapData())
	}

	result := map[string]any{
		"items": items,
	}
	return helpers.SuccReturn(ctx, result)
}

// 获取验证码及token
func GetCaptcha(ctx context.Context, req ghttp.Request) ghttp.Response {
	imgBase, str, err := helpers.GenerateCaptcha(CaptchaLength)
	if err != nil {
		go helpers.HelixNotice(ctx, "---generate captcha fail---"+err.Error())
		return helpers.NewException(ctx, "DB_ERR")
	}

	token := helpers.GetRandomString(CaptchaTokenLength)
	cacheKey := redis.CaptchaPrefix + token
	redis.Set(ctx, cacheKey, str, redis.CaptchaLimitTime)

	result := map[string]any{
		"image": imgBase,
		"token": token,
	}
	return helpers.SuccReturn(ctx, result)
}

// cameo 提交蛋白质结构预测接口
// 该接口不需要鉴权
func SubmitProteinForCameo(ctx context.Context, req ghttp.Request) ghttp.Response {
	serial, _ := req.PostForm("sequence")
	title, _ := req.PostForm("title")
	email, _ := req.PostForm("email")
	tType := int64(models.TaskTypeProtein)
	realId := int64(models.DefaultCameoRealID)

	// 参数校验
	serialLen := len([]rune(serial))
	emailLen := len([]rune(email))
	titleLen := len([]rune(title))
	if serialLen <= 0 || serialLen > MaxSerialLimit {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "sequence length error")
	}
	if emailLen <= 0 || emailLen > EmailLengthLimit {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "email length error")
	}
	if titleLen <= 0 || titleLen > MaxTaskNameLimit {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "title length error")
	}

	userM := models.User{}
	user, err := userM.GetUserByRealID(ctx, realId, "")
	if err != nil {
		return helpers.NewException(ctx, "DB_ERR")
	}

	// 任务数限制一下
	cond := models.TaskCond{
		UserId:      int64(user.ID),
		GtCreatedAt: time.Now().Format("2006-01-02"),
	}
	taskM := models.Task{}
	taskCount, err := taskM.CountTaskByCond(ctx, cond)
	if err != nil {
		return helpers.NewException(ctx, "DB_ERR")
	}
	if taskCount > CameoTaskLimit {
		return helpers.FailReturn(ctx, helpers.LogicErrorCode, "today task limit")
	}

	var needConfig models.TaskProteinConf
	serial = ">" + title + "\n" + serial
	needConfig.Serial = serial
	needConfig.IsCameo = true
	needConfig.Relaxation = true
	needConfig.Ensembling = 8
	confByte, _ := json.Marshal(needConfig)

	// 数据组装
	taskN := models.Task{
		UserId:   user.ID,
		Type:     uint64(tType),
		Name:     title,
		FuncType: uint64(models.FuncTypeForecast),
		Config:   string(confByte),
	}

	taskNew, err := taskM.Add(ctx, taskN)
	if err != nil {
		return helpers.NewException(ctx, "DB_ERR")
	}

	// 提交任务给调度
	services.PreSubmitTask(ctx, taskNew)

	// 将邮箱写入Redis
	cacheKey := redis.TaskEmailPrefix + strconv.Itoa(int(taskNew.ID))
	redis.Set(ctx, cacheKey, email, redis.TaskEmailLimitTime)

	return helpers.SuccReturn(ctx, nil)
}

// 发送邮件
func SendEmail(ctx context.Context, req ghttp.Request) ghttp.Response {
	params := helpers.GetBodyParams(ctx, req)
	email := helpers.GetHTMLStringParam(params, "email")
	language := helpers.GetHTMLStringParam(params, "language")

	emailLen := len(email)
	if emailLen <= 0 || emailLen > EmailLengthLimit {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "email length error")
	}
	if !checkLanguage(language) {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "language param error")
	}

	// 限制一下发邮件的频率
	cacheK := redis.UserEmailPrefix + email
	countStr := redis.Get(ctx, cacheK)
	if countStr != "" {
		count, _ := strconv.Atoi(countStr)
		if count >= UserSubscribeLimit {
			return helpers.FailReturn(ctx, helpers.LogicErrorCode, "Try too often, please try again later")
		}
		redis.Incr(ctx, cacheK)
	} else {
		redis.Set(ctx, cacheK, 1, 600)
	}

	// 生成验证码
	code := helpers.GetRandomString(EmailCodeLimit)
	cacheKey := redis.TaskEmailPrefix + email
	redis.Set(ctx, cacheKey, code, redis.SubscribeEmailTime)

	contentByte, err := os.ReadFile(env.DataDir() + "/template/code_" + language + ".html")
	if err != nil {
		go helpers.HelixNotice(ctx, "---email code template error:"+err.Error())
		contentByte = []byte("Hi:\n\n 邮箱验证码是：code-code\n\n Thanks")
	}

	// 发送邮件
	subject := "PaddleHelix 订阅验证码"
	content := strings.ReplaceAll(string(contentByte), "code-code", code)
	content = strings.ReplaceAll(content, "xxxemail", email)
	helpers.SendEmailByBaidu(helpers.FromUserBaidu, email, subject, content)

	return helpers.SuccReturn(ctx, nil)
}

// 订阅邮件
func SubscribeMsg(ctx context.Context, req ghttp.Request) ghttp.Response {
	params := helpers.GetBodyParams(ctx, req)
	email := helpers.GetHTMLStringParam(params, "email")
	code := helpers.GetHTMLStringParam(params, "code")
	language := helpers.GetHTMLStringParam(params, "language")

	emailLen := len(email)
	if emailLen <= 0 || emailLen > EmailLengthLimit {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "email length error")
	}
	if len([]rune(code)) != EmailCodeLimit {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "code param error")
	}
	if !checkLanguage(language) {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "language param error")
	}

	cacheKey := redis.TaskEmailPrefix + email
	validCode := redis.Get(ctx, cacheKey)
	if validCode != code {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "code param invalid")
	}
	redis.Del(ctx, cacheKey)

	// 写入db
	subscribeM := models.Subscribe{}
	subscribeInfo, err := subscribeM.GetByEmail(ctx, email)
	if err != nil {
		return helpers.NewException(ctx, "DB_ERR")
	}
	if subscribeInfo.ID > 0 {
		subscribeInfo.Content = "{\"news\":1, \"module\":1}"
		subscribeInfo.Save(ctx)
	} else {
		subscribeNew := models.Subscribe{
			Email:   email,
			Content: "{\"news\":1, \"module\":1}",
		}
		_, err := subscribeNew.Add(ctx, subscribeNew)
		if err != nil {
			return helpers.NewException(ctx, "DB_ERR")
		}
	}

	contentByte, err := os.ReadFile(env.DataDir() + "/template/subscribe_" + language + ".html")
	if err != nil {
		go helpers.HelixNotice(ctx, "---email code template error:"+err.Error())
		contentByte = []byte("Hi:\n\n 订阅成功。\n\n Thanks")
	}

	subject := "PaddleHelix 订阅成功"
	content := strings.ReplaceAll(string(contentByte), "xxxemail", email)
	helpers.SendEmailByBaidu(helpers.FromUserBaidu, email, subject, content)

	return helpers.SuccReturn(ctx, nil)
}

// 删除订阅
func UnSubscribeMsg(ctx context.Context, req ghttp.Request) ghttp.Response {
	params := helpers.GetBodyParams(ctx, req)
	email := helpers.GetHTMLStringParam(params, "email")
	code := helpers.GetHTMLStringParam(params, "code")
	language := helpers.GetHTMLStringParam(params, "language")
	contentType := helpers.GetIntParam(params, "content_type", 0)

	emailLen := len(email)
	if emailLen <= 0 || emailLen > EmailLengthLimit {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "email length error")
	}
	if len([]rune(code)) != EmailCodeLimit {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "code param error")
	}
	if contentType != 1 && contentType != 2 && contentType != 3 {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "content_type param error")
	}
	if !checkLanguage(language) {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "language param error")
	}

	cacheKey := redis.TaskEmailPrefix + email
	validCode := redis.Get(ctx, cacheKey)
	if validCode != code {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "code param invalid")
	}
	redis.Del(ctx, cacheKey)

	// 修改db
	subscribeM := models.Subscribe{}
	subscribeInfo, err := subscribeM.GetByEmail(ctx, email)
	if err != nil {
		return helpers.NewException(ctx, "DB_ERR")
	}

	if subscribeInfo.ID > 0 {
		var contentConf models.ContentConf
		_ = json.Unmarshal([]byte(subscribeInfo.Content), &contentConf)

		switch contentType {
		case 1:
			subscribeInfo.Status = int64(models.StatusDel)
			break
		case 2:
			contentConf.Module = 0
			break
		case 3:
			contentConf.News = 0
			break
		}
		if contentConf.Module == 0 && contentConf.News == 0 {
			subscribeInfo.Status = int64(models.StatusDel)
		}
		contentByte, _ := json.Marshal(contentConf)
		subscribeInfo.Content = string(contentByte)
		subscribeInfo.Save(ctx)
	}

	contentByte, err := os.ReadFile(env.DataDir() + "/template/unsubscribe_" + language + ".html")
	if err != nil {
		go helpers.HelixNotice(ctx, "---email code template error:"+err.Error())
		contentByte = []byte("Hi:\n\n 取消订阅成功。\n\n Thanks")
	}

	subject := "PaddleHelix 取消订阅"
	content := strings.ReplaceAll(string(contentByte), "xxxemail", email)
	helpers.SendEmailByBaidu(helpers.FromUserBaidu, email, subject, content)

	return helpers.SuccReturn(ctx, nil)
}

// 该接口不需要鉴权
func SubmitProteinForCasp(ctx context.Context, req ghttp.Request) ghttp.Response {
	serial, _ := req.PostForm("sequence")
	title, _ := req.PostForm("title")
	email, _ := req.PostForm("email")
	tType := int64(models.TaskTypeProtein)
	realId := int64(models.DefaultCameoRealID)

	// 参数校验
	serialLen := len([]rune(serial))
	emailLen := len([]rune(email))
	titleLen := len([]rune(title))
	if serialLen <= 0 || serialLen > MaxSerialLimit {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "sequence length error")
	}
	if emailLen <= 0 || emailLen > EmailLengthLimit {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "email length error")
	}
	if titleLen <= 0 || titleLen > MaxTaskNameLimit {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "title length error")
	}
	userM := models.User{}
	user, err := userM.GetUserByRealID(ctx, realId, "")
	if err != nil {
		return helpers.NewException(ctx, "DB_ERR")
	}

	// 任务数限制一下
	cond := models.TaskCond{
		UserId:      int64(user.ID),
		GtCreatedAt: time.Now().Format("2006-01-02"),
	}
	taskM := models.Task{}
	taskCount, err := taskM.CountTaskByCond(ctx, cond)
	if err != nil {
		return helpers.NewException(ctx, "DB_ERR")
	}
	if taskCount > CameoTaskLimit {
		return helpers.FailReturn(ctx, helpers.LogicErrorCode, "today task limit")
	}
	serial = strings.ReplaceAll(serial, "\\n", "\n")

	var needConfig models.TaskProteinConf
	needConfig.Serial = serial
	needConfig.IsCameo = true
	needConfig.IsCasp = true
	needConfig.Relaxation = true
	needConfig.Ensembling = 8
	confByte, _ := json.Marshal(needConfig)

	// 数据组装
	taskN := models.Task{
		UserId:   uint64(user.ID),
		Type:     uint64(tType),
		Name:     title,
		FuncType: uint64(models.FuncTypeForecast),
		Config:   string(confByte),
	}
	taskNew, err := taskM.Add(ctx, taskN)
	if err != nil {
		return helpers.NewException(ctx, "DB_ERR")
	}

	// 发送邮件给主办方
	autoEmail := "<EMAIL>"
	if email == "<EMAIL>" || email == "<EMAIL>" {
		autoEmail = email
	}
	subject := title + " - query received by hFold"
	emailContent := "Received success"
	err = helpers.SendMail(helpers.FromUser, autoEmail, subject, emailContent, "")
	if err != nil {
		helpers.LogError(ctx, err)
		helpers.FailReturn(ctx, helpers.LogicErrorCode, "data error, please repeat")
	}

	// 提交任务给调度
	services.PreSubmitTask(ctx, taskNew)

	// 将邮箱写入Redis
	cacheKey := redis.TaskEmailPrefix + strconv.Itoa(int(taskNew.ID))
	redis.Set(ctx, cacheKey, email, redis.TaskEmailLimitTime)

	return helpers.SuccReturn(ctx, nil)
}

func checkLanguage(language string) bool {
	if language == "en" || language == "cn" {
		return true
	}

	return false
}

// 获得列表
func GetOverallList(ctx context.Context, req ghttp.Request) ghttp.Response {
	overallM := models.HomeModule{}
	overallList, err := overallM.GetAll(ctx)
	if err != nil {
		helpers.FailReturn(ctx, helpers.LogicErrorCode, "data error, please repeat")
	}

	// 数据组装
	var items []any
	for _, overall := range overallList {
		items = append(items, overall.SwapData())
	}

	result := map[string]any{
		"items": items,
	}
	return helpers.SuccReturn(ctx, result)
}

// 获得列表
func GetSkillList(ctx context.Context, req ghttp.Request) ghttp.Response {
	skillM := models.Skill{}
	skillList, err := skillM.GetAll(ctx)
	if err != nil {
		helpers.FailReturn(ctx, helpers.LogicErrorCode, "data error, please repeat")
	}

	// 数据组装
	var items []any
	for _, skill := range skillList {
		items = append(items, skill.SwapData())
	}

	result := map[string]any{
		"items": items,
	}
	return helpers.SuccReturn(ctx, result)
}

// DataDot 数据打点
func DataDot(ctx context.Context, req ghttp.Request) ghttp.Response {
	params := helpers.GetBodyParams(ctx, req)
	tType := helpers.GetIntParam(params, "type", 0)
	content := helpers.GetMapParam(params, "content")
	cookie, _ := req.Cookie("BAIDUID")
	if !checkDotType(tType) {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "type params error")
	}
	if len(content) > ContentLengthLimit {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "content params error")
	}

	// 存储数据
	contentByte, _ := json.Marshal(content)
	dotNew := models.DotData{
		Type:    uint64(tType),
		Host:    cookie.Value,
		Content: string(contentByte),
		UserId:  uint64(getUserId(ctx)),
	}
	_, err := dotNew.Add(ctx, dotNew)
	if err != nil {
		helpers.FailReturn(ctx, helpers.LogicErrorCode, "data error, please repeat")
	}

	return helpers.SuccReturn(ctx, nil)
}

func checkDotType(tType int64) bool {
	if tType == models.DotTypeNor ||
		tType == models.DotTypeHome ||
		tType == models.DotTypePV ||
		tType == models.DotTypeHQDL ||
		tType == models.DotTypeWCDL {
		return true
	}
	return false
}

// checkUserBalance 检查用户余额，如果不足则返回错误
// ctx: context.Context 上下文对象
// cost: float64 消费金额
// 返回值 error 错误信息，如果余额充足则为nil
func checkUserBalance(ctx context.Context, cost float64, taskType int) error {
	if cost == 0 {
		return nil
	}
	userType := ctxutils.GetUserInfo(ctx).Type
	switch userType {
	case int64(models.UserTypeTry):
		// 试用账户校验代金券和余额
		balance, err := chpc.QueryIAMUserBalance(ctx, ctxutils.GetIAMUserDomainID(ctx))
		if err != nil {
			return err
		}
		couponM := models.Coupon{}
		coupons, err := couponM.QueryAvailableCouponAmountForTask(ctx, ctxutils.GetUserID(ctx), taskType)
		if err != nil {
			return err
		}
		amount := balance + coupons
		if amount < cost {
			return errors.New("account balance is not enough")
		}
		return nil
	case int64(models.UserTypeCharge):
		// 商业账户校验余额
		balance, err := chpc.QueryAvailableBalance(ctx, ctxutils.GetIAMUserDomainID(ctx))
		if err != nil {
			return err
		}
		if balance < cost {
			return errors.New("account balance is not enough")
		}
		return nil
	case int64(models.UserTypeInside), int64(models.UserTypeNormal):
		// 内部用户和普通用户无需校验
		return nil
	default:
		return errors.New("user type not support")
	}
}
