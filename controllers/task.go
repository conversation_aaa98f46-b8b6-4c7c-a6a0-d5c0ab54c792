package controllers

import (
	"context"
	"encoding/json"
	"fmt"

	"icode.baidu.com/baidu/gdp/ghttp"
	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/library/ctxutils"
	"icode.baidu.com/helix_web/models"
	"icode.baidu.com/helix_web/services"
)

const (
	DefaultNameLength = 12
	MaxTaskNameLimit  = 200
	MaxFileUrlLimit   = 512
	MaxSerialLimit    = 60000

	MaxBeamSizeLimit = 200
	MaxScoreLimit    = 10
	MaxTaskIdLimit   = 100

	MaxDrugLimit = 300

	// 最大标点数
	MaxPointLimit = 5

	// file 大小限制 50M
	MaxFileContentLimit = 51200000
)

var (
	taskStatusMessage = map[int]string{
		models.TaskStatusCancel: "取消",
		models.TaskStatusSucc:   "完成",
		models.TaskStatusFailed: "失败",
	}
)

// 获取任务详细
func GetTaskInfo(ctx context.Context, req ghttp.Request) ghttp.Response {
	params := helpers.GetBodyParams(ctx, req)
	taskId := helpers.GetIntParam(params, "task_id", 0)
	if taskId <= 0 {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "task_id param error")
	}

	// 获取数据
	taskM := models.Task{}
	taskData, err := taskM.GetTaskByUserAndId(ctx, getUserId(ctx), taskId)
	if err != nil {
		return helpers.NewException(ctx, "DB_ERR")
	}

	// 返回数据
	result := taskData.SwapData()
	if taskData.ID > 0 {
		trainId := 0
		trainName := ""
		if taskData.ParentID > 0 {
			trainData, err := taskM.GetTaskById(ctx, int64(taskData.ParentID))
			if err != nil {
				return helpers.NewException(ctx, "DB_ERR")
			}

			trainId = int(trainData.ID)
			trainName = trainData.Name
		}
		result["train_id"] = trainId
		result["train_name"] = trainName
	}
	return helpers.SuccReturn(ctx, result)
}

// 更新任务已读
// 通过 userId 将用户所有的task 都置为已读
func SetTaskRead(ctx context.Context, req ghttp.Request) ghttp.Response {
	params := helpers.GetBodyParams(ctx, req)
	taskIdList := helpers.GetIntSliceParam(params, "task_ids")
	if len(taskIdList) > MaxTaskIdLimit {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "param error")
	}
	cond := models.TaskCond{
		UserId:      getUserId(ctx),
		StatusList:  []int64{int64(models.TaskStatusSucc)},
		GtCreatedAt: UnreadStartTime,
		HadRead:     []int64{int64(models.HadReadFalse)},
		TaskIDList:  taskIdList,
	}

	// 获取数据
	taskM := models.Task{}
	taskList, err := taskM.GetTaskByCond(ctx, cond, 200, 1)
	if err != nil {
		return helpers.NewException(ctx, "DB_ERR")
	}

	var taskIdL []uint64
	for _, task := range taskList {
		taskIdL = append(taskIdL, task.ID)
	}

	// 批量更新
	if len(taskIdL) > 0 {
		_ = taskM.BatchUpdate(ctx, taskIdL, map[string]any{"had_read": models.HadReadTrue})
	}
	return helpers.SuccReturn(ctx, nil)
}

// 获取历史训练列表
// 只有admet、分子活性支持自训练
func GetHistoryTrainList(ctx context.Context, req ghttp.Request) ghttp.Response {
	params := helpers.GetBodyParams(ctx, req)
	tType := helpers.GetIntParam(params, "type", 0)
	limit := helpers.GetIntParam(params, "limit", 20)
	page := helpers.GetIntParam(params, "page", 1)
	keyword := helpers.GetStringParam(params, "keyword")
	if len(keyword) > KeywordLenLimit {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "keyword length limit")
	}

	// 检索条件
	typeList := []int64{tType}
	if tType == int64(models.TaskTypeAdmet) || tType == int64(models.TaskTypeSelfAdmet) {
		typeList = []int64{int64(models.TaskTypeAdmet), int64(models.TaskTypeSelfAdmet)}
	}
	if !checkTrainTaskType(int(tType)) {
		typeList = []int64{int64(models.TaskTypeAdmet), int64(models.TaskTypeSelfAdmet), int64(models.TaskTypeMolActivity)}
	}
	cond := models.TaskCond{
		UserId:     getUserId(ctx),
		TypeList:   typeList,
		FuncType:   int64(models.FuncTypeTrain),
		StatusList: []int64{int64(models.TaskStatusSucc)},
		Keyword:    keyword,
	}

	taskM := models.Task{}
	total, err := taskM.CountTaskByCond(ctx, cond)
	if err != nil {
		return helpers.NewException(ctx, "DB_ERR")
	}

	taskList, err := taskM.GetTaskByCond(ctx, cond, int(limit), int(page), "use_time desc")
	if err != nil {
		return helpers.NewException(ctx, "DB_ERR")
	}

	// 数据组装
	var items []any
	for _, taskData := range taskList {
		items = append(items, taskData.SwapData())
	}

	result := map[string]any{
		"total": total,
		"items": items,
	}
	return helpers.SuccReturn(ctx, result)
}

// 获取任务列表
// 任务管理页
func GetTaskList(ctx context.Context, req ghttp.Request) ghttp.Response {
	params := helpers.GetBodyParams(ctx, req)
	typeList := helpers.GetIntSliceParam(params, "type_list")
	keyword := helpers.GetStringParam(params, "keyword")
	trainId := helpers.GetIntParam(params, "train_id", 0)
	status := helpers.GetIntParam(params, "status", 0)
	isTrain := helpers.GetBoolParam(params, "is_train", false)
	limit := helpers.GetIntParam(params, "limit", 10)
	page := helpers.GetIntParam(params, "page", 1)

	// 参数验证
	if len(keyword) > KeywordLenLimit {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "keyword length limit")
	}
	limit, page = formatPaging(limit, page)

	// IAM主账号可以查询当前主账号及所有子账号的任务信息
	// IAM子账号只可以查询当前子账号的任务信息
	iamUserID := ctxutils.GetIAMUserID(ctx)
	iamDomainID := ctxutils.GetIAMUserDomainID(ctx)
	var iamUserIDs []string
	if iamUserID != "" && iamDomainID != "" {
		if iamUserID != iamDomainID {
			iamUserIDs = append(iamUserIDs, iamUserID)
		} else {
			userM := models.User{}
			userIds, err := userM.GetUserIDsByDomainID(ctx, iamDomainID)
			if err != nil {
				return helpers.NewException(ctx, "DB_ERR")
			}
			for _, userId := range userIds {
				iamUserIDs = append(iamUserIDs, userId)
			}
		}
	}
	cond := models.TaskCond{
		UserId:     getUserId(ctx),
		IAMUserIDs: iamUserIDs,
		TypeList:   typeList,
		FuncType:   int64(models.FuncTypeForecast),
	}
	if status > 0 {
		cond.StatusList = []int64{status}
	}
	if trainId >= 0 {
		cond.ParentID = []int64{trainId}
	}
	if len(keyword) > 0 {
		cond.Keyword = keyword
	}
	if isTrain {
		cond.FuncType = int64(models.FuncTypeTrain)
		if helpers.CheckInSliceByInt64(int64(models.TaskTypeSelfAdmet), typeList) {
			cond.TypeList = append(typeList, int64(models.TaskTypeAdmet))
		}
	}

	// 获取数据
	taskM := models.Task{}
	total, err := taskM.CountTaskByCond(ctx, cond)
	if err != nil {
		return helpers.NewException(ctx, "DB_ERR")
	}

	taskList, err := taskM.GetTaskByCond(ctx, cond, int(limit), int(page))
	if err != nil {
		return helpers.NewException(ctx, "DB_ERR")
	}

	// 获取map
	var parentIdList []int64
	for _, taskData := range taskList {
		parentIdList = append(parentIdList, int64(taskData.ParentID))
	}
	trainList, err := taskM.GetListByIds(ctx, parentIdList, "id asc")
	if err != nil {
		return helpers.NewException(ctx, "DB_ERR")
	}
	trainMap := make(map[int]models.Task, len(trainList))
	for _, trainData := range trainList {
		trainMap[int(trainData.ID)] = trainData
	}

	// 数据组装
	var items []any
	for _, taskData := range taskList {
		tmp := taskData.SwapData()
		tmp["train_name"] = ""
		if val, ok := trainMap[int(taskData.ParentID)]; ok {
			tmp["train_name"] = val.Name
		}

		items = append(items, tmp)
	}

	result := map[string]any{
		"total": total,
		"items": items,
	}
	return helpers.SuccReturn(ctx, result)
}

// 取消任务
func CancelTask(ctx context.Context, req ghttp.Request) ghttp.Response {
	params := helpers.GetBodyParams(ctx, req)
	taskId := helpers.GetIntParam(params, "task_id", 0)
	if taskId <= 0 {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "task_id param error")
	}

	taskM := models.Task{}
	taskData, err := taskM.GetTaskByUserAndId(ctx, getUserId(ctx), taskId)
	if err != nil {
		return helpers.NewException(ctx, "DB_ERR")
	}

	if taskData.ID > 0 && checkTaskStatusCancel(int(taskData.Status)) {
		// 取消任务
		err = services.KillTask(ctx, taskData)
		if err != nil {
			return helpers.FailReturn(ctx, helpers.TaskCancelFailureErrorCode, "task cancel failure")
		}
		taskData.Status = int64(models.TaskStatusCancel)
		err = taskData.Save(ctx)
		if err != nil {
			return helpers.FailReturn(ctx, helpers.DBErrorCode, "task cancel failure")
		}
		return helpers.SuccReturn(ctx, nil)
	}
	return helpers.FailReturn(ctx, helpers.TaskCancelFailureErrorCode,
		fmt.Sprintf("task status is: %s not support cancel", taskStatusMessage[int(taskData.Status)]))
}

// 删除任务
func DelTask(ctx context.Context, req ghttp.Request) ghttp.Response {
	params := helpers.GetBodyParams(ctx, req)
	taskId := helpers.GetIntParam(params, "task_id", 0)
	taskIds := helpers.GetIntSliceParam(params, "task_ids")
	if taskId <= 0 && len(taskIds) <= 0 {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "task_id param error")
	}
	taskIds = append(taskIds, taskId)

	for _, taskId := range taskIds {
		taskM := models.Task{}
		taskData, err := taskM.GetTaskByUserAndId(ctx, getUserId(ctx), taskId)
		if err != nil {
			return helpers.NewException(ctx, "DB_ERR")
		}

		if taskData.ID > 0 && checkTaskStatusDel(int(taskData.Status)) {
			taskData.Status = int64(models.StatusDel)
			err = taskData.Save(ctx)
			if err != nil {
				return helpers.NewException(ctx, "DB_ERR")
			}
		}
	}

	return helpers.SuccReturn(ctx, nil)
}

// 修改任务名称
func RenameTask(ctx context.Context, req ghttp.Request) ghttp.Response {
	params := helpers.GetBodyParams(ctx, req)
	taskId := helpers.GetIntParam(params, "task_id", 0)
	name := helpers.GetStringParam(params, "name")
	if taskId <= 0 {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "task_id param")
	}

	nameLen := len([]rune(name))
	if nameLen <= 0 || nameLen > MaxTaskNameLimit {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "name param error")
	}

	taskM := models.Task{}
	taskData, err := taskM.GetTaskByUserAndId(ctx, getUserId(ctx), taskId)
	if err != nil {
		return helpers.NewException(ctx, "DB_ERR")
	}
	if taskData.ID > 0 {
		taskData.Name = name
		err = taskData.Save(ctx)
		if err != nil {
			return helpers.NewException(ctx, "DB_ERR")
		}
	}

	return helpers.SuccReturn(ctx, nil)
}

// 提交训练
// 只有 admet 和 分子活性预测 支持自训练
func SubmitTrain(ctx context.Context, req ghttp.Request) ghttp.Response {
	params := helpers.GetBodyParams(ctx, req)
	tType := helpers.GetIntParam(params, "type", 0)
	name := helpers.GetStringParam(params, "name")
	fileUrl := helpers.GetStringParam(params, "file_url")
	trainType := helpers.GetStringParam(params, "train_type")
	if !checkTrainTaskType(int(tType)) {
		return helpers.NewException(ctx, "PARAM_VALUE_ERR", "type")
	}

	nameLen := len([]rune(name))
	if nameLen <= 0 || nameLen > MaxTaskNameLimit {
		return helpers.NewException(ctx, "PARAM_VALUE_ERR", "name")
	}
	fileUrlLen := len([]rune(fileUrl))
	if fileUrlLen <= 0 || fileUrlLen > MaxFileUrlLimit {
		return helpers.NewException(ctx, "PARAM_VALUE_ERR", "file_url")
	}
	if tType == int64(models.TaskTypeSelfAdmet) && !checkTrainAlgorithm(trainType) {
		return helpers.NewException(ctx, "PARAM_VALUE_ERR", "train_type")
	}

	// 上传文件校验
	err := checkFileContent(getFileContent(fileUrl))
	if err != nil {
		return helpers.NewException(ctx, "PARAM_VALUE_ERR", err.Error())
	}
	// 用户权限校验
	_, err = CheckUserPermission(ctx, tType, false)
	if err != nil {
		return helpers.NewException(ctx, "PARAM_VALUE_ERR", err.Error())
	}

	// 数据组装
	config := map[string]string{"file_url": fileUrl}
	if tType == int64(models.TaskTypeSelfAdmet) {
		config["train_type"] = trainType
	}

	configByte, _ := json.Marshal(config)
	taskN := models.Task{
		UserId:   uint64(getUserId(ctx)),
		Type:     uint64(tType),
		Name:     name,
		FuncType: uint64(models.FuncTypeTrain),
		Config:   string(configByte),
	}

	taskNew, err := taskN.Add(ctx, taskN)
	if err != nil {
		return helpers.NewException(ctx, "DB_ERR")
	}

	// 提交任务到调度
	services.PreSubmitTask(ctx, taskNew)
	result := map[string]any{
		"task_id": taskNew.ID,
	}
	return helpers.SuccReturn(ctx, result)
}

// GetTaskInfos 获取任务信息，可以通过task_id或者task_ids参数来指定需要查询的任务
// ctx: 上下文对象，包含了请求的一些必要信息，如请求ID等。可以使用ghttp.GetCtx(req)获取。
// req: HTTP请求，包含了HTTP方法、URL、Header和Body等信息。
// 返回值：ghttp.Response类型，包含了响应状态码、响应头和响应体三个部分。
func GetTaskInfos(ctx context.Context, req ghttp.Request) ghttp.Response {
	params := helpers.GetBodyParams(ctx, req)
	taskId := helpers.GetIntParam(params, "task_id", 0)
	taskIds := helpers.GetIntSliceParam(params, "task_ids")
	if taskId <= 0 && len(taskIds) <= 0 {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "task_id param error")
	}

	// 获取数据
	taskIds = append(taskIds, taskId)
	results := make([]map[string]any, len(taskIds))
	taskM := models.Task{}
	for _, taskId := range taskIds {
		taskData, err := taskM.GetTaskByUserAndId(ctx, getUserId(ctx), taskId)
		if err != nil {
			return helpers.NewException(ctx, "DB_ERR")
		}

		result := taskData.SwapData()
		if taskData.ID > 0 {
			trainId := 0
			trainName := ""
			if taskData.ParentID > 0 {
				trainData, err := taskM.GetTaskById(ctx, int64(taskData.ParentID))
				if err != nil {
					return helpers.NewException(ctx, "DB_ERR")
				}

				trainId = int(trainData.ID)
				trainName = trainData.Name
			}
			result["train_id"] = trainId
			result["train_name"] = trainName
		}
		results = append(results, result)
	}
	return helpers.SuccReturn(ctx, results)
}
