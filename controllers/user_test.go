package controllers

import (
	"context"
	"io"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"icode.baidu.com/baidu/gdp/ghttp"
	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/models"
)

// TestUserCommon TestUserCommon 是一个测试函数，用于测试用户相关接口的功能。
// 参数 t *testing.T 是 testing 包提供的用于标记单元测试的函数指针，表示需要进行单元测试。
// 返回值没有返回值
func TestUserCommon(t *testing.T) {
	ctx := context.Background()
	ctx = context.WithValue(ctx, "user_info", models.UserInfo{UserID: 1, Type: 1})
	raw := httptest.NewRequest("POST", "/", nil)
	req := ghttp.NewRequest(time.Now(), raw)

	funcList := map[string]func(context.Context, ghttp.Request) ghttp.Response{
		"GetUserInfo":   GetUserInfo,
		"GetUserNotify": GetUserNotify,
		"GetFlowList":   GetFlowList,
		"GetBosAuth":    GetBosAuth,
		"GetUserTask":   GetAccountInfo,
	}

	for k, v := range funcList {
		resp := v(ctx, req).(*ghttp.JSONResponse)
		data := resp.Data.(helpers.JsonResp)
		if data.Code != helpers.SuccessCode {
			t.Errorf("func=%s, code=%d, want=0", k, data.Code)
		}
	}
}

// TestGetBosUrl 测试函数，用于获取BOS URL
// 参数：*testing.T - 类型为*testing.T的指针，表示测试对象
// 返回值：无返回值
func TestGetBosUrl(t *testing.T) {
	ctx := context.Background()
	ctx = context.WithValue(ctx, "user_info", models.UserInfo{UserID: 1, Type: 1})

	caseList := []struct {
		input  io.Reader
		output int
	}{
		{strings.NewReader("{\"bos_list\":[\"bos:/xxx\"]}"), helpers.SuccessCode},
		{nil, helpers.ParamErrorCode},
	}

	for _, v := range caseList {
		raw := httptest.NewRequest("POST", "/", v.input)
		req := ghttp.NewRequest(time.Now(), raw)

		resp := GetBosUrl(ctx, req).(*ghttp.JSONResponse)
		data := resp.Data.(helpers.JsonResp)
		if data.Code != v.output {
			t.Errorf("code=%d, want=%d", data.Code, v.output)
		}
	}
}
