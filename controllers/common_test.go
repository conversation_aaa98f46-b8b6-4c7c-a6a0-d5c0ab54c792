package controllers

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"icode.baidu.com/baidu/gdp/ghttp"
	"icode.baidu.com/helix_web/helpers"
)

func TestVerifyGithub(t *testing.T) {
	ctx := context.Background()

	//input1, _ := json.Marshal(map[string]string{"code":"dddd"})
	caseList := []struct {
		input  io.Reader
		output int
	}{
		{nil, helpers.ParamErrorCode},
	}

	for _, v := range caseList {
		raw := httptest.NewRequest("POST", "/", v.input)
		req := ghttp.NewRequest(time.Now(), raw)

		resp := VerifyGithub(ctx, req).(*ghttp.JSONResponse)
		data := resp.Data.(helpers.JsonResp)
		if data.Code != v.output {
			fmt.Println(data)
			t.Errorf("code=%d, want=%d", data.Code, v.output)
		}
	}
}

func TestGetFeedbarShow(t *testing.T) {
	ctx := context.Background()

	input1, _ := json.Marshal(map[string]int{"limit": 10})
	caseList := []struct {
		input  io.Reader
		output int
	}{
		{nil, helpers.SuccessCode},
		{bytes.NewReader(input1), helpers.SuccessCode},
	}

	for _, v := range caseList {
		raw := httptest.NewRequest("POST", "/", v.input)
		req := ghttp.NewRequest(time.Now(), raw)

		resp := GetFeedbarShow(ctx, req).(*ghttp.JSONResponse)
		data := resp.Data.(helpers.JsonResp)
		if data.Code != v.output {
			fmt.Println(data)
			t.Errorf("code=%d, want=%d", data.Code, v.output)
		}
	}
}

func TestGetAchievementShow(t *testing.T) {
	ctx := context.Background()

	input1, _ := json.Marshal(map[string]int{"limit": 10})
	caseList := []struct {
		input  io.Reader
		output int
	}{
		{nil, helpers.SuccessCode},
		{bytes.NewReader(input1), helpers.SuccessCode},
	}

	for _, v := range caseList {
		raw := httptest.NewRequest("POST", "/", v.input)
		req := ghttp.NewRequest(time.Now(), raw)

		resp := GetAchievementShow(ctx, req).(*ghttp.JSONResponse)
		data := resp.Data.(helpers.JsonResp)
		if data.Code != v.output {
			fmt.Println(data)
			t.Errorf("code=%d, want=%d", data.Code, v.output)
		}
	}
}

func TestGetSerialList(t *testing.T) {
	ctx := context.Background()

	input1, _ := json.Marshal(map[string]int{"type": 10})
	input2, _ := json.Marshal(map[string]int{"type": 10, "func_type": 10, "data_type": 10})
	caseList := []struct {
		input  io.Reader
		output int
	}{
		{nil, helpers.ParamErrorCode},
		{bytes.NewReader(input1), helpers.ParamErrorCode},
		{bytes.NewReader(input2), helpers.SuccessCode},
	}

	for _, v := range caseList {
		raw := httptest.NewRequest("POST", "/", v.input)
		req := ghttp.NewRequest(time.Now(), raw)

		resp := GetSerialList(ctx, req).(*ghttp.JSONResponse)
		data := resp.Data.(helpers.JsonResp)
		if data.Code != v.output {
			fmt.Println(data)
			t.Errorf("code=%d, want=%d", data.Code, v.output)
		}
	}
}

func TestCommon(t *testing.T) {
	ctx := context.Background()
	raw := httptest.NewRequest("POST", "/", nil)
	req := ghttp.NewRequest(time.Now(), raw)

	funcList := map[string]func(context.Context, ghttp.Request) ghttp.Response{
		"GetCaptcha": GetCaptcha,
	}

	for k, v := range funcList {
		resp := v(ctx, req).(*ghttp.JSONResponse)
		data := resp.Data.(helpers.JsonResp)
		if data.Code != helpers.SuccessCode {
			t.Errorf("func=%s, code=%d, want=0", k, data.Code)
		}
	}
}

type feedbackReq struct {
	Content string `json:"content"`
	Email   string `json:"email"`
	Name    string `json:"name"`
	Captcha string `json:"captcha"`
	Token   string `json:"token"`
	Company string `json:"company"`
	Phone   string `json:"phone"`
}

func TestSubmitFeedback(t *testing.T) {
	ctx := context.Background()

	input1 := feedbackReq{
		Content: "test",
		Email:   "test",
		Name:    "test",
		Captcha: "test",
		Token:   "test",
	}
	input1Byte, _ := json.Marshal(input1)

	//token := helpers.GetRandomString(CaptchaTokenLength)
	//cacheKey := redis.CaptchaPrefix + token
	//redis.Set(ctx, cacheKey, "testx", redis.CaptchaLimitTime)
	//input1.Captcha = "testx"
	//input1.Token   = token
	//input2Byte, _ := json.Marshal(input1)

	caseList := []struct {
		input  io.Reader
		output int
	}{
		{nil, helpers.ParamErrorCode},
		{bytes.NewReader(input1Byte), helpers.ParamErrorCode},
		//{bytes.NewReader(input2Byte), helpers.SuccessCode},
	}

	for _, v := range caseList {
		raw := httptest.NewRequest("POST", "/", v.input)
		req := ghttp.NewRequest(time.Now(), raw)

		resp := SubmitFeedback(ctx, req).(*ghttp.JSONResponse)
		data := resp.Data.(helpers.JsonResp)
		if data.Code != v.output {
			fmt.Println(data)
			t.Errorf("code=%d, want=%d", data.Code, v.output)
		}
	}
}

func TestSubmitApply(t *testing.T) {
	ctx := context.Background()

	input1 := feedbackReq{
		Content: "test",
		Email:   "test",
		Name:    "test",
		Phone:   "test",
		Company: "test",
		Captcha: "test",
		Token:   "test",
	}
	input1Byte, _ := json.Marshal(input1)

	//token := helpers.GetRandomString(CaptchaTokenLength)
	//cacheKey := redis.CaptchaPrefix + token
	//redis.Set(ctx, cacheKey, "testx", redis.CaptchaLimitTime)
	//input1.Captcha = "testx"
	//input1.Token   = token
	//input2Byte, _ := json.Marshal(input1)

	caseList := []struct {
		input  io.Reader
		output int
	}{
		{nil, helpers.ParamErrorCode},
		{bytes.NewReader(input1Byte), helpers.ParamErrorCode},
		//{bytes.NewReader(input2Byte), helpers.SuccessCode},
	}

	for _, v := range caseList {
		raw := httptest.NewRequest("POST", "/", v.input)
		req := ghttp.NewRequest(time.Now(), raw)

		resp := SubmitApply(ctx, req).(*ghttp.JSONResponse)
		data := resp.Data.(helpers.JsonResp)
		if data.Code != v.output {
			fmt.Println(data)
			t.Errorf("code=%d, want=%d", data.Code, v.output)
		}
	}
}

func TestSubmitProteinForCameo(t *testing.T) {
	ctx := context.Background()

	input1 := map[string][]string{
		"sequence": {"test"},
		"title":    {"test"},
	}
	input2 := map[string][]string{
		"sequence": {"test"},
	}
	input3 := map[string][]string{
		"sequence": {"test"},
		"title":    {"test"},
		"email":    {"test"},
	}

	caseList := []struct {
		input  map[string][]string
		output int
	}{
		{nil, helpers.ParamErrorCode},
		{input1, helpers.ParamErrorCode},
		{input2, helpers.ParamErrorCode},
		{input3, helpers.SuccessCode},
	}

	for _, v := range caseList {
		raw := http.Request{PostForm: v.input}
		req := ghttp.NewRequest(time.Now(), &raw)

		resp := SubmitProteinForCameo(ctx, req).(*ghttp.JSONResponse)
		data := resp.Data.(helpers.JsonResp)
		if data.Code != v.output {
			fmt.Println(data)
			t.Errorf("cameo code=%d, want=%d", data.Code, v.output)
		}
	}

	for _, v := range caseList {
		raw := http.Request{PostForm: v.input}
		req := ghttp.NewRequest(time.Now(), &raw)

		resp := SubmitProteinForCasp(ctx, req).(*ghttp.JSONResponse)
		data := resp.Data.(helpers.JsonResp)
		if data.Code != v.output {
			t.Errorf("casp code=%d, want=%d", data.Code, v.output)
		}
	}
}

func TestSendEmail(t *testing.T) {
	ctx := context.Background()

	input1, _ := json.Marshal(map[string]interface{}{
		"email": "serialxxx",
	})
	input2, _ := json.Marshal(map[string]interface{}{
		"language": "en",
	})
	input3, _ := json.Marshal(map[string]interface{}{
		"email":    "serialxxx.com",
		"language": "cn",
	})
	input4, _ := json.Marshal(map[string]interface{}{
		"email":    "serialxxx.com",
		"language": "en",
	})

	caseList := []struct {
		input  []byte
		output int
	}{
		{nil, helpers.ParamErrorCode},
		{input1, helpers.ParamErrorCode},
		{input2, helpers.ParamErrorCode},
		{input3, helpers.SuccessCode},
		{input4, helpers.SuccessCode},
	}

	for _, v := range caseList {
		raw := httptest.NewRequest("POST", "/", bytes.NewReader(v.input))
		req := ghttp.NewRequest(time.Now(), raw)

		resp := SendEmail(ctx, req).(*ghttp.JSONResponse)
		data := resp.Data.(helpers.JsonResp)
		if data.Code != v.output {
			fmt.Println(data)
			t.Errorf("code=%d, want=%d", data.Code, v.output)
		}
	}
}

func TestSubscribeMsg(t *testing.T) {
	ctx := context.Background()

	input1, _ := json.Marshal(map[string]interface{}{
		"email": "serialxxx",
		"code":  "sss",
	})
	input2, _ := json.Marshal(map[string]interface{}{
		"language": "en",
	})
	input3, _ := json.Marshal(map[string]interface{}{
		"email":    "serialxxx.com",
		"language": "cn",
		"code":     "xxxxxx",
	})
	input4, _ := json.Marshal(map[string]interface{}{
		"email":    "serialxxx.com",
		"language": "en",
		"code":     "xxxxxx",
	})

	caseList := []struct {
		input  []byte
		output int
	}{
		{nil, helpers.ParamErrorCode},
		{input1, helpers.ParamErrorCode},
		{input2, helpers.ParamErrorCode},
		{input3, helpers.ParamErrorCode},
		{input4, helpers.ParamErrorCode},
		//{input3, helpers.SuccessCode},
		//{input4, helpers.SuccessCode},
	}

	for _, v := range caseList {
		raw := httptest.NewRequest("POST", "/", bytes.NewReader(v.input))
		req := ghttp.NewRequest(time.Now(), raw)

		// 伪造
		//code := "xxxxxx"
		//cacheKey := redis.TaskEmailPrefix + "serialxxx.com"
		//redis.Set(ctx, cacheKey, code, redis.SubscribeEmailTime)

		resp := SubscribeMsg(ctx, req).(*ghttp.JSONResponse)
		data := resp.Data.(helpers.JsonResp)
		if data.Code != v.output {
			fmt.Println(data)
			t.Errorf("code=%d, want=%d", data.Code, v.output)
		}
	}
}

func TestUnSubscribeMsg(t *testing.T) {
	ctx := context.Background()

	input1, _ := json.Marshal(map[string]interface{}{
		"email": "serialxxx",
		"code":  "sss",
	})
	input2, _ := json.Marshal(map[string]interface{}{
		"language": "en",
	})
	input3, _ := json.Marshal(map[string]interface{}{
		"email":    "serialxxx.com",
		"language": "cn",
		"code":     "xxxxxx",
	})
	input4, _ := json.Marshal(map[string]interface{}{
		"email":        "serialxxx.com",
		"language":     "en",
		"code":         "xxxxxx",
		"content_type": 1,
	})

	caseList := []struct {
		input  []byte
		output int
	}{
		{nil, helpers.ParamErrorCode},
		{input1, helpers.ParamErrorCode},
		{input2, helpers.ParamErrorCode},
		{input3, helpers.ParamErrorCode},
		{input4, helpers.ParamErrorCode},
	}

	for _, v := range caseList {
		raw := httptest.NewRequest("POST", "/", bytes.NewReader(v.input))
		req := ghttp.NewRequest(time.Now(), raw)

		// 伪造
		//code := "xxxxxx"
		//cacheKey := redis.TaskEmailPrefix + "serialxxx.com"
		//redis.Set(ctx, cacheKey, code, redis.SubscribeEmailTime)

		resp := UnSubscribeMsg(ctx, req).(*ghttp.JSONResponse)
		data := resp.Data.(helpers.JsonResp)
		if data.Code != v.output {
			fmt.Println(data)
			t.Errorf("code=%d, want=%d", data.Code, v.output)
		}
	}
}
