{
    "name": "",                                    # 可选, string
    "sequence": "AUGCGGC",                         # 可选，string。sequence_file_url与sequence 必须二选一。
    "sequence_file_url": "",                       # 可选，string。
    "need_fold": true,                             # 必要，bool，对于linearfold，固定传true
    "need_partition": false,                       # 可选，bool，对于linearfold，如果包含该字段需要固定传false，也可以不包含该字段。
    "fold_config": {                               # need_fold为true时必要
        "beam_size": 100,                          # 必要，int, 范围[1, 200]
        "use_constraints": true,                   # 必要，bool.
        "constraint_str": "???????",               # 可选，string. use_constraints为true时必要。
        "zuker_score": 5.5,                        # 可选，float, 范围[0, 10]
    }
}