package controllers

import (
	"context"
	"math"
	"strconv"
	"strings"
	"time"

	"icode.baidu.com/helix_web/library/bceaa"
	"icode.baidu.com/helix_web/library/chpc"
	"icode.baidu.com/helix_web/library/ctxutils"

	"icode.baidu.com/baidu/gdp/ghttp"

	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/library/bce"
	"icode.baidu.com/helix_web/library/redis"
	"icode.baidu.com/helix_web/models"
	"icode.baidu.com/helix_web/services"
)

const (
	UnreadStartTime = "2021-06-25"

	ChargeTypeCreateNum = 30
	ChargeTypeCreateMol = 40
	ChargeTypeTimes     = 50

	AdmetApiTaskType = 31
)

// 获取用户信息
// 从 ctx 中获取
func GetUserInfo(ctx context.Context, req ghttp.Request) ghttp.Response {
	userInfo := ctx.Value("user_info").(models.UserInfo)

	needPopup := false
	cacheKey := redis.UserPopupPrefix + strconv.Itoa(int(userInfo.UserID))
	resp := redis.Get(ctx, cacheKey)
	if resp == "" {
		needPopup = true
		redis.Set(ctx, cacheKey, true, 86400*365)
	}
	userInfo.NeedPopup = needPopup

	// 用户提交处理
	portraitM := models.Portrait{}
	row, err := portraitM.GetByUserId(ctx, userInfo.UserID)
	if err != nil {
		return helpers.NewException(ctx, "DB_ERR")
	}
	if row.ID <= 0 {
		params := helpers.GetBodyParams(ctx, req)
		code := helpers.GetHTMLStringParam(params, "code")
		portraitData, _ := portraitM.GetByCode(ctx, code)
		if portraitData.ID > 0 && portraitData.UserId <= 0 {
			// 发放奖励
			awardKey := redis.UserAwardPrefix + strconv.Itoa(int(userInfo.UserID))
			countStr := redis.Get(ctx, awardKey)
			count, _ := strconv.Atoi(countStr)
			count = count + portraitAwardNum
			redis.Set(ctx, awardKey, count, redis.TaskUserNumTime)

			// 写入数据
			portraitData.UserId = uint64(userInfo.UserID)
			portraitData.AwardNum = portraitAwardNum
			err = portraitData.Save(ctx)
			if err != nil {
				return helpers.NewException(ctx, "DB_ERR")
			}
			row = portraitData
		}
	}

	// 用户画像
	needBox := false
	hadPortrait := false
	if row.ID > 0 {
		hadPortrait = true
	} else {
		cacheKey := redis.UserPortraitBoxPrefix + strconv.Itoa(int(userInfo.UserID))
		boxResp := redis.Get(ctx, cacheKey)
		if boxResp == "" {
			needBox = true
		}
	}

	userInfo.NeedBox = needBox
	userInfo.HadPortrait = hadPortrait
	return helpers.SuccReturn(ctx, userInfo)
}

// 获取用户的通知
// 包括训练中、已完成未读、message 统计
func GetUserNotify(ctx context.Context, req ghttp.Request) ghttp.Response {
	userId := getUserId(ctx)

	// 训练中数据
	cond1 := models.TaskCond{
		UserId:     userId,
		StatusList: []int64{int64(models.TaskStatusDoing), int64(models.TaskStatusNew)},
	}

	taskM := models.Task{}
	doingCount, err := taskM.CountTaskByCond(ctx, cond1)
	if err != nil {
		return helpers.NewException(ctx, "DB_ERR")
	}

	// 未读数据
	cond2 := models.TaskCond{
		UserId:      userId,
		StatusList:  []int64{int64(models.TaskStatusSucc)},
		GtCreatedAt: UnreadStartTime,
		HadRead:     []int64{int64(models.HadReadFalse)},
	}

	// 获取未读的数据 数量
	unreadCount, err := taskM.CountTaskByCond(ctx, cond2)
	if err != nil {
		return helpers.NewException(ctx, "DB_ERR")
	}

	// messages 信息
	messages, err := services.GetUserMessage(ctx, userId)
	if err != nil {
		return helpers.FailReturn(ctx, helpers.LogicErrorCode, err.Error())
	}

	result := map[string]any{
		"doing_count":  doingCount,
		"unread_count": unreadCount,
		"messages":     messages,
	}
	return helpers.SuccReturn(ctx, result)
}

// 获取用户任务信息
func GetUserTaskInfo(ctx context.Context, req ghttp.Request) ghttp.Response {
	params := helpers.GetBodyParams(ctx, req)
	taskType := helpers.GetIntParam(params, "type", -1)
	taskInfo := make(map[string]any)
	userInfo := ctx.Value("user_info").(models.UserInfo)
	if userInfo.Type != int64(models.UserTypeInside) {
		var needTypeList []int64
		var tryData []any
		if userInfo.Type != int64(models.UserTypeNormal) {
			trialM := models.Trial{}
			trialList, err := trialM.GetTrial(ctx, userInfo.UserID)
			if err != nil {
				return helpers.NewException(ctx, "DATA_ERR")
			}

			typeMap := make(map[int]bool)
			for _, trial := range trialList {
				typeMap[int(trial.TaskType)] = true

				tmp := map[string]any{
					"trial_type": trial.Type,
					"task_type":  trial.TaskType,
					"start_time": models.SwapFieldUnix(trial.StartTime.Unix()),
				}
				if trial.Type == uint64(models.TrialTypeNum) {
					tmp["used_num"] = trial.UsedNum
					tmp["limit_num"] = trial.LimitNum
				} else {
					tmp["used_day"] = (time.Now().Unix() - trial.StartTime.Unix()) / 86400
					tmp["limit_day"] = (trial.EndTime.Unix() - trial.StartTime.Unix()) / 86400
				}
				tryData = append(tryData, tmp)
			}

			for key := range models.TaskTypeList {
				if typeMap[key] {
					continue
				}
				needTypeList = append(needTypeList, int64(key))
			}
		}

		// task 使用次数获取
		taskInfo = map[string]any{
			"total_num": getUserFreeNum(ctx, userInfo.UserID, int(taskType)),
			"used_num":  getUserUsedNum(ctx, userInfo.UserID, int(taskType)),
			"try_list":  tryData,
		}
	}

	taskInfo["user_type"] = userInfo.Type
	return helpers.SuccReturn(ctx, taskInfo)
}

// 获取账户信息
func GetAccountInfo(ctx context.Context, req ghttp.Request) ghttp.Response {
	userInfo := ctx.Value("user_info").(models.UserInfo)

	// 获取账户信息
	accountFund, _ := services.GetAccountFund(ctx, userInfo.RealID, userInfo.Source)

	// 获取量包信息
	packageList, err := services.GetPackageList(ctx, userInfo.RealID, userInfo.Source, services.PackageApiList)
	if err != nil {
		return helpers.NewException(ctx, "DB_ERR")
	}
	var items []any
	for _, item := range packageList {
		tmp := map[string]any{
			"type":  helpers.StrToInt(item.ConsoleApiId),
			"total": helpers.StrToInt(item.Total),
			"rest":  helpers.StrToInt(item.Total) - helpers.StrToInt(item.Usage),
		}
		items = append(items, tmp)
	}

	// 查询是否开通后付费&或者欠费
	isDebt := false
	var openList []int64
	orderList, _ := services.GetOrderStatus(ctx, userInfo.RealID, userInfo.Source, services.AllApiList)
	for apiId, order := range orderList {
		if order.Status == services.OrderStatusOpened {
			openList = append(openList, apiId)
		}
		if order.Status == services.OrderStatusStopped {
			isDebt = true
		}
	}

	// 修改为计费用户
	if userInfo.Type == int64(models.UserTypeNormal) && (len(packageList) > 0 || len(openList) > 0) {
		userM := models.User{}
		userData, _ := userM.GetUserByID(ctx, userInfo.UserID)
		userData.Type = uint64(models.UserTypeCharge)
		if userData.ChangeTime.Unix() <= 0 {
			userData.ChangeTime = time.Now()
		}
		_ = userData.Save(ctx)
	}

	accountInfo := map[string]any{
		"open_list":    openList,
		"amount":       accountFund.Cash,
		"package_list": items,
		"is_debt":      isDebt,
	}
	return helpers.SuccReturn(ctx, accountInfo)
}

// 获取账单列表
func GetBillList(ctx context.Context, req ghttp.Request) ghttp.Response {
	params := helpers.GetBodyParams(ctx, req)
	accountId := helpers.GetIntParam(params, "account_id", 0)
	limit := helpers.GetIntParam(params, "limit", 20)
	page := helpers.GetIntParam(params, "page", 1)
	if accountId <= 0 {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "account_id params error")
	}

	billM := models.Bill{}
	billCond := models.BillCond{
		RealId:   accountId,
		NeedPush: []int{models.NeedPushTrue},
	}
	total, err := billM.Count(ctx, billCond)
	if err != nil {
		return helpers.NewException(ctx, "DB_ERR")
	}

	billList, err := billM.GetList(ctx, billCond, int(limit), int(page))
	if err != nil {
		return helpers.NewException(ctx, "DB_ERR")
	}

	// 获取taskMap
	var taskIdList []int64
	for _, bill := range billList {
		taskIdList = append(taskIdList, int64(bill.TaskId))
	}
	taskMap := getTaskMap(ctx, taskIdList)

	var items []any
	for _, bill := range billList {
		if bill.Level < 1 {
			bill.Level = 1
		}
		taskLevel := strconv.Itoa(int(bill.TaskType)) + "-" + strconv.Itoa(int(bill.Level))
		tmp := map[string]any{
			"task_id":         bill.TaskId,
			"task_type":       bill.TaskType,
			"charge_time":     bill.ChargeDuration,
			"charge_num":      bill.ChargeNum,
			"charge_type":     bill.ChargeType,
			"settlement_time": bill.CreatedAt.Unix(),
			"log_id":          bill.LogId,
			"specs":           services.LevelToSpecsMap[taskLevel],
		}
		if bill.TaskType == uint64(models.TaskTypeLinearDesign) {
			tmp["charge_type"] = ChargeTypeCreateNum
		}
		if bill.TaskType == uint64(models.TaskTypeAdmet) {
			tmp["charge_type"] = ChargeTypeCreateMol
		}
		if bill.TaskType == uint64(models.TaskTypeUTR) {
			tmp["charge_type"] = ChargeTypeTimes
		}
		if tmp["charge_type"] == uint64(models.ChargeTypeDuration) {
			tmp["charge_num"] = math.Ceil(float64(bill.ChargeDuration) / 60)
		}

		tmp["name"] = "--"
		tmp["resource"] = "--"
		if task, ok := taskMap[int(bill.TaskId)]; ok {
			tmp["name"] = task.Name
			tmp["resource"] = task.Resource
			if task.IsApi > 0 {
				tmp["task_type"] = AdmetApiTaskType
			}
		}
		items = append(items, tmp)
	}

	result := map[string]any{
		"total": total,
		"items": items,
	}
	return helpers.SuccReturn(ctx, result)
}

// 获取用户流量统计
func GetFlowList(ctx context.Context, req ghttp.Request) ghttp.Response {
	return helpers.SuccReturn(ctx, nil)
}

// 获取 bos 临时信息
func GetBosAuth(ctx context.Context, req ghttp.Request) ghttp.Response {
	params := helpers.GetBodyParams(ctx, req)
	bosType := helpers.GetIntParam(params, "type", 0)
	userInfo := ctx.Value("user_info").(models.UserInfo)

	var err error
	var resMap map[string]interface{}
	switch bosType {
	case 1:
		resMap, err = bceaa.GetSessionToken(userInfo.UserID)
	default:
		resMap, err = bce.GetSessionToken(userInfo.UserID)
	}
	if err != nil {
		go helpers.HelixNotice(ctx, "----- bos error ---"+err.Error())
		return helpers.NewException(ctx, "DB_ERR")
	}
	return helpers.SuccReturn(ctx, resMap)
}

// 获取 bos 访问链接
// 生产有时间限制的 bos url
func GetBosUrl(ctx context.Context, req ghttp.Request) ghttp.Response {
	params := helpers.GetBodyParams(ctx, req)
	bosList := helpers.GetStringSliceParam(params, "bos_list")
	bosType := helpers.GetIntParam(params, "type", 0)
	if len(bosList) <= 0 {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "bos_list params error")
	}

	var err error
	var bosUrl string
	var result []string
	userId := getUserId(ctx)
	taskM := models.Task{}
	for _, bosPath := range bosList {
		bucket, object := helpers.DealBosFileUrl(bosPath)
		if bucket != "" && len(object) >= 6 {
			switch bosType {
			case 1:
				bosUrl, err = bceaa.GenerateObjectURL(bucket, object, bce.ObjectUrlExpireTime)
			default:
				bosUrl, err = bce.GenerateObjectUrl(bucket, object, bce.ObjectUrlExpireTime)
			}
			if err != nil {
				go helpers.HelixNotice(ctx, "--- bos GenerateObject err ----"+err.Error())
			}
		}

		taskId := getIdByObject(object)
		if taskId > 0 {
			taskInfo, _ := taskM.GetTaskByUserAndId(ctx, userId, int64(taskId))
			if taskInfo.ID <= 0 {
				bosUrl = ""
			}
		}
		bosUserId := getUserIdByObject(object)
		if bosUserId > 0 && bosUserId != int(userId) {
			bosUrl = ""
		}
		result = append(result, bosUrl)
	}

	return helpers.SuccReturn(ctx, result)
}

// 获取output，id
func getIdByObject(object string) int {
	arr := strings.Split(object, "/")

	id, outputKey := 0, -1
	for key, val := range arr {
		if val == "output" || val == "helix_output" {
			outputKey = key
		}
	}
	if outputKey >= 0 && len(arr) > outputKey+2 {
		id, _ = strconv.Atoi(arr[outputKey+1])
	}

	return id
}

// 获取output，id
func getUserIdByObject(object string) int {
	arr := strings.Split(object, "/")

	id, outputKey := 0, -1
	for key, val := range arr {
		if val == "helix_upload" {
			outputKey = key
		}
	}
	if outputKey >= 0 && len(arr) > outputKey+2 {
		id, _ = strconv.Atoi(arr[outputKey+1])
	}

	return id
}

// GetAccountBalance 获取账户余额，返回一个ghttp.Response类型的响应对象
func GetAccountBalance(ctx context.Context, req ghttp.Request) ghttp.Response {
	balance, _ := chpc.QueryIAMUserBalance(ctx, ctxutils.GetIAMUserDomainID(ctx))
	return helpers.SuccReturn(ctx, balance)
}

// IsOpenCHPCService IsOpenCHPCService 判断是否开启了CHPC服务，返回一个ghttp.Response类型的响应值
// ctx context.Context 上下文信息，包含请求的相关信息和操作者的身份信息
// req ghttp.Request 请求对象，包含请求的方法、路由等信息
// 返回值 ghttp.Response 响应对象，包含处理结果和相关信息
func IsOpenCHPCService(ctx context.Context, req ghttp.Request) ghttp.Response {
	return helpers.SuccReturn(ctx, chpc.IsOpenCHPCService(ctxutils.GetIAMUserDomainID(ctx)))
}
