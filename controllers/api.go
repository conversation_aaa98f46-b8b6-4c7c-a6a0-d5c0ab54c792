package controllers

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"

	"icode.baidu.com/baidu/gdp/ghttp"
	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/library/bce"
	"icode.baidu.com/helix_web/library/bceaa"
	"icode.baidu.com/helix_web/library/chpc"
	"icode.baidu.com/helix_web/library/ctxutils"
	"icode.baidu.com/helix_web/library/redis"
	"icode.baidu.com/helix_web/models"
)

const (
	ApiTaskStatusSucc        = 1
	ApiTaskStatusDoing       = 2
	ApiTaskStatusFailed      = -1
	ApiTaskStatusCancel      = -2
	FileTempURLTimeoutSecond = 30 * 24 * 60 * 60 // 一月
)

var apiTaskStatusMap = map[int]int{
	models.TaskStatusSucc:   ApiTaskStatusSucc,
	models.TaskStatusDoing:  ApiTaskStatusDoing,
	models.TaskStatusNew:    ApiTaskStatusFailed,
	models.TaskStatusFailed: ApiTaskStatusFailed,
	models.TaskStatusCancel: ApiTaskStatusCancel,
}

type TaskResult struct {
	DownloadURL  string `json:"download_url"`
	FileURL      string `json:"file_url"`
	LogPath      string `json:"log_path"`
	ErrorCode    string `json:"error_code"`
	RudderTaskId string `json:"rudder_task_id"`
}

func ApiSubmitTask(ctx context.Context, req ghttp.Request) ghttp.Response {
	newCtx := context.WithValue(ctx, "is_api", 1)
	return SubmitAdmet(newCtx, req)
}

// ApiSubmitHelixFold3Task 提交HelixFold3任务，返回ghttp.Response类型的响应结果
func ApiSubmitHelixFold3Task(ctx context.Context, req ghttp.Request) ghttp.Response {
	newCtx := context.WithValue(ctx, "is_api", 1)
	if ctxutils.GetUserInfo(ctx).Type == int64(models.UserTypeNormal) {
		open := chpc.IsOpenCHPCService(ctxutils.GetIAMUserDomainID(ctx))
		if !open {
			helpers.LogError(ctx, fmt.Errorf("chpc service is not open, account_id: %s", ctxutils.GetIAMUserDomainID(ctx)))
			return helpers.FailReturn(ctx, helpers.AccountErrorCode, "chpc service is not open")
		}
		userM := models.User{}
		user, err := userM.GetUserByID(ctx, ctxutils.GetUserID(ctx))
		if err != nil {
			return helpers.FailReturn(ctx, helpers.DBErrorCode, err.Error())
		}
		user.Type = uint64(models.UserTypeCharge)
		err = user.Save(ctx)
		if err != nil {
			return helpers.FailReturn(ctx, helpers.DBErrorCode, err.Error())
		}
		cacheKey := redis.IAMUserPrefix + ctxutils.GetUserInfo(ctx).IAMUserID
		_ = redis.Del(ctx, cacheKey)
		userInfo := ctxutils.GetUserInfo(ctx)
		userInfo.Type = int64(user.Type)
		newCtx = context.WithValue(newCtx, "user_info", userInfo)
	}
	return SubmitHelixfoldaa(newCtx, req)
}

// ApiQuerySubmitHelixFold3TaskPrice 查询helixfold3任务金额
func ApiQuerySubmitHelixFold3TaskPrice(ctx context.Context, req ghttp.Request) ghttp.Response {
	newCtx := context.WithValue(ctx, "is_api", 1)
	return QuerySubmitHelixFold3TaskPrice(newCtx, req)
}

// ApiQueryBatchSubmitHelixFold3TaskPrice 查询helixfold3多任务金额
func ApiQueryBatchSubmitHelixFold3TaskPrice(ctx context.Context, req ghttp.Request) ghttp.Response {
	newCtx := context.WithValue(ctx, "is_api", 1)
	return QueryBatchSubmitHelixFold3TaskPrice(newCtx, req)
}

// ApiBatchSubmitHelixFold3Task 批量提交HelixFold3任务，返回ghttp.Response类型的响应结果
func ApiBatchSubmitHelixFold3Task(ctx context.Context, req ghttp.Request) ghttp.Response {
	newCtx := context.WithValue(ctx, "is_api", 1)
	if ctxutils.GetUserInfo(ctx).Type == int64(models.UserTypeNormal) {
		open := chpc.IsOpenCHPCService(ctxutils.GetIAMUserDomainID(ctx))
		if !open {
			helpers.LogError(ctx, fmt.Errorf("chpc service is not open, account_id: %s", ctxutils.GetIAMUserDomainID(ctx)))
			return helpers.FailReturn(ctx, helpers.AccountErrorCode, "chpc service is not open")
		}
		userM := models.User{}
		user, err := userM.GetUserByID(ctx, ctxutils.GetUserID(ctx))
		if err != nil {
			return helpers.FailReturn(ctx, helpers.DBErrorCode, err.Error())
		}
		user.Type = uint64(models.UserTypeCharge)
		err = user.Save(ctx)
		if err != nil {
			return helpers.FailReturn(ctx, helpers.DBErrorCode, err.Error())
		}
		cacheKey := redis.IAMUserPrefix + ctxutils.GetUserInfo(ctx).IAMUserID
		_ = redis.Del(ctx, cacheKey)
		userInfo := ctxutils.GetUserInfo(ctx)
		userInfo.Type = int64(user.Type)
		newCtx = context.WithValue(newCtx, "user_info", userInfo)
	}
	return BatchSubmitHelixFold3(newCtx, req)
}

// ApiSubmitHF3AgabTask 提交HF3Agab任务，返回ghttp.Response类型的响应结果
func ApiSubmitHF3AgabTask(ctx context.Context, req ghttp.Request) ghttp.Response {
	newCtx := context.WithValue(ctx, "is_api", 1)
	if ctxutils.GetUserInfo(ctx).Type == int64(models.UserTypeNormal) {
		open := chpc.IsOpenCHPCService(ctxutils.GetIAMUserDomainID(ctx))
		if !open {
			helpers.LogError(ctx, fmt.Errorf("chpc service is not open, account_id: %s", ctxutils.GetIAMUserDomainID(ctx)))
			return helpers.FailReturn(ctx, helpers.AccountErrorCode, "chpc service is not open")
		}
		userM := models.User{}
		user, err := userM.GetUserByID(ctx, ctxutils.GetUserID(ctx))
		if err != nil {
			return helpers.FailReturn(ctx, helpers.DBErrorCode, err.Error())
		}
		user.Type = uint64(models.UserTypeCharge)
		err = user.Save(ctx)
		if err != nil {
			return helpers.FailReturn(ctx, helpers.DBErrorCode, err.Error())
		}
		cacheKey := redis.IAMUserPrefix + ctxutils.GetUserInfo(ctx).IAMUserID
		_ = redis.Del(ctx, cacheKey)
		userInfo := ctxutils.GetUserInfo(ctx)
		userInfo.Type = int64(user.Type)
		newCtx = context.WithValue(newCtx, "user_info", userInfo)
	}
	return SubmitHF3Agab(newCtx, req)
}

// ApiQuerySubmitHF3AgabTaskPrice 查询HF3Agab任务金额
func ApiQuerySubmitHF3AgabTaskPrice(ctx context.Context, req ghttp.Request) ghttp.Response {
	newCtx := context.WithValue(ctx, "is_api", 1)
	return QuerySubmitHF3AgabTaskPrice(newCtx, req)
}

// ApiQueryBatchSubmitHF3AgabTaskPrice 查询HF3Agab多任务金额
func ApiQueryBatchSubmitHF3AgabTaskPrice(ctx context.Context, req ghttp.Request) ghttp.Response {
	newCtx := context.WithValue(ctx, "is_api", 1)
	return QueryBatchSubmitHF3AgabTaskPrice(newCtx, req)
}

// ApiBatchSubmitHF3AgabTask 批量提交HF3Agab任务，返回ghttp.Response类型的响应结果
func ApiBatchSubmitHF3AgabTask(ctx context.Context, req ghttp.Request) ghttp.Response {
	newCtx := context.WithValue(ctx, "is_api", 1)
	if ctxutils.GetUserInfo(ctx).Type == int64(models.UserTypeNormal) {
		open := chpc.IsOpenCHPCService(ctxutils.GetIAMUserDomainID(ctx))
		if !open {
			helpers.LogError(ctx, fmt.Errorf("chpc service is not open, account_id: %s", ctxutils.GetIAMUserDomainID(ctx)))
			return helpers.FailReturn(ctx, helpers.AccountErrorCode, "chpc service is not open")
		}
		userM := models.User{}
		user, err := userM.GetUserByID(ctx, ctxutils.GetUserID(ctx))
		if err != nil {
			return helpers.FailReturn(ctx, helpers.DBErrorCode, err.Error())
		}
		user.Type = uint64(models.UserTypeCharge)
		err = user.Save(ctx)
		if err != nil {
			return helpers.FailReturn(ctx, helpers.DBErrorCode, err.Error())
		}
		cacheKey := redis.IAMUserPrefix + ctxutils.GetUserInfo(ctx).IAMUserID
		_ = redis.Del(ctx, cacheKey)
		userInfo := ctxutils.GetUserInfo(ctx)
		userInfo.Type = int64(user.Type)
		newCtx = context.WithValue(newCtx, "user_info", userInfo)
	}
	return BatchSubmitHF3Agab(newCtx, req)
}

// ApiSubmitKYKTTask 提交KYKT任务，用于API请求
func ApiSubmitKYKTTask(ctx context.Context, req ghttp.Request) ghttp.Response {
	newCtx := context.WithValue(ctx, "is_api", 1)
	return SubmitKYKT(newCtx, req)
}

// ApiCancelTask ApiCancelTask 取消任务接口，用于API请求
// ctx context.Context 上下文信息
// req ghttp.Request HTTP请求对象
// 返回值 ghttp.Response HTTP响应对象
func ApiCancelTask(ctx context.Context, req ghttp.Request) ghttp.Response {
	newCtx := context.WithValue(ctx, "is_api", 1)
	return CancelTask(newCtx, req)
}

// ApiGetTaskInfo 获取任务结果
func ApiGetTaskInfo(ctx context.Context, req ghttp.Request) ghttp.Response {
	params := helpers.GetBodyParams(ctx, req)
	taskId := helpers.GetIntParam(params, "task_id", 0)
	if taskId <= 0 {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "task_id param error")
	}

	// 获取任务
	taskM := models.Task{}
	taskData, err := taskM.GetTaskByUserAndId(ctx, getUserId(ctx), taskId)
	if err != nil {
		return helpers.NewException(ctx, "DB_ERR")
	}

	// 获取任务结果
	if taskData.Type == uint64(models.TaskTypeAdmet) { // 特殊处理，向前兼容Admet任务逻辑
		return ApiGetAdemtTaskInfo(ctx, req)
	}
	var result TaskResult
	_ = json.Unmarshal([]byte(taskData.Result), &result)
	convertResultBosUrl(taskData.Type, &result)
	resultByte, _ := json.Marshal(result)
	res := map[string]any{
		"status":   apiTaskStatusMap[int(taskData.Status)],
		"run_time": taskData.ChargeEndTime.Unix() - taskData.ChargeStartTime.Unix(),
		"result":   string(resultByte),
	}
	return helpers.SuccReturn(ctx, res)
}

// convertResultBosUrl 函数名: convertResultBosUrl
// 参数: taskType uint64 (uint64类型) - 任务类型，用于区分是否使用bceaa.GenerateObjectURL
//
//	result *TaskResult (指针类型) - 结果对象指针，包含需要转换的字段，如DownloadURL、FileURL和LogPath
//
// 返回值: 无返回值
// 功能：将结果中的BOS文件下载地址（DownloadURL）、文件地址（FileURL）和日志路径（LogPath）进行转换，生成临时访问URL，并更新到结果对象中
func convertResultBosUrl(taskType uint64, result *TaskResult) {
	f := bce.GenerateObjectUrl
	if int(taskType) == models.TaskTypeHelixFoldAA || int(taskType) == models.TaskTypeHelixFold3S1 {
		f = bceaa.GenerateObjectURL
	}
	if result.DownloadURL != "" {
		bucket, object := helpers.DealBosFileUrl(result.DownloadURL)
		downloadURL, _ := f(bucket, object, FileTempURLTimeoutSecond)
		result.DownloadURL = downloadURL
	}
	if result.FileURL != "" {
		bucket, object := helpers.DealBosFileUrl(result.FileURL)
		fileURL, _ := f(bucket, object, FileTempURLTimeoutSecond)
		result.FileURL = fileURL
	}
	if result.LogPath != "" {
		bucket, object := helpers.DealBosFileUrl(result.LogPath)
		logPath, _ := f(bucket, object, FileTempURLTimeoutSecond)
		result.LogPath = logPath
	}
}

// ApiGetAdemtTaskInfo 获取任务结果
func ApiGetAdemtTaskInfo(ctx context.Context, req ghttp.Request) ghttp.Response {
	params := helpers.GetBodyParams(ctx, req)
	taskId := helpers.GetIntParam(params, "task_id", 0)
	if taskId <= 0 {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "task_id param error")
	}

	// 获取数据
	taskM := models.Task{}
	taskData, err := taskM.GetTaskByUserAndId(ctx, getUserId(ctx), taskId)
	if err != nil {
		return helpers.NewException(ctx, "DB_ERR")
	}
	if taskData.Type != uint64(models.TaskTypeAdmet) {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "task_id param error")
	}

	// 返回数据
	res := map[string]any{
		"status":   ApiTaskStatusSucc,
		"run_time": taskData.ChargeEndTime.Unix() - taskData.ChargeStartTime.Unix(),
		"result":   nil,
	}
	if taskData.Status != int64(models.TaskStatusSucc) {
		res["status"] = apiTaskStatusMap[int(taskData.Status)]
		return helpers.SuccReturn(ctx, res)
	}

	// 成功
	taskResultM := models.TaskResult{}
	resultList, err := taskResultM.GetListByCond(ctx, int64(taskData.ID), models.ResultSearchCond{}, 1000, 1)
	if err != nil || len(resultList) <= 0 {
		return helpers.FailReturn(ctx, helpers.LogicErrorCode, "data error")
	}

	var list []any
	for _, item := range resultList {
		var detail [][]any
		_ = json.Unmarshal([]byte(item.Detail), &detail)

		content := make(map[string]any, len(detail)+1)
		content["smiles"] = item.Serial
		for _, item := range detail {
			if len(item) < 5 {
				continue
			}
			key := strings.ReplaceAll(strings.ToLower(item[3].(string)), " ", "_")
			content[key] = item[4]
			if val, ok := item[4].(float64); ok {
				content[key] = helpers.FormatFloat(val, 2)
			}
		}
		list = append(list, content)
	}

	res["result"] = list
	return helpers.SuccReturn(ctx, res)
}
