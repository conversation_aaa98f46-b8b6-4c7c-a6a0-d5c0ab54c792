package controllers

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http/httptest"
	"testing"
	"time"

	"icode.baidu.com/baidu/gdp/ghttp"
	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/models"
)

// TestSubmitComment TestSubmitComment 是一个测试函数，用于提交评论。
// 它接收一个指向 testing.T 的指针作为参数，并返回 void。
// 该函数使用 context.Background() 创建一个上下文，并将 user_info 类型的值设置为 models.UserInfo{UserID: 1, Type: 1}。
// 然后，它创建了两个 JSON 编码的字符串，分别表示 type=10, score=5 和 type=20, score=4, content="smiles"，extra_comment=[{"type":50,"score":3,"content":"xxx"}]。
// 接着，它定义了一个 caseList 切片，包含三个元素：nil、input1 和 input2。每个元素都包含一个 io.Reader 和一个期望的输出（int）。
// 最后，对于每个 caseList 中的元素，它创建了一个新的请求，并使用 SubmitComment 函数处理该请求。
// 如果处理结果的 code 不等于期望的输出，则会调用 t.Errorf 打印错误信息并退出测试。
func TestSubmitComment(t *testing.T) {
	ctx := context.Background()
	ctx = context.WithValue(ctx, "user_info", models.UserInfo{UserID: 1, Type: 1})

	input1, _ := json.Marshal(map[string]interface{}{
		"type":  10,
		"score": 5,
	})
	input2, _ := json.Marshal(map[string]interface{}{
		"type":    20,
		"score":   4,
		"content": "smiles",
		"extra_comment": []map[string]interface{}{
			{
				"type":    50,
				"score":   3,
				"content": "xxx",
			},
		},
	})

	caseList := []struct {
		input  io.Reader
		output int
	}{
		{nil, helpers.ParamErrorCode},
		{bytes.NewReader(input1), helpers.SuccessCode},
		{bytes.NewReader(input2), helpers.SuccessCode},
	}

	for _, v := range caseList {
		raw := httptest.NewRequest("POST", "/", v.input)
		req := ghttp.NewRequest(time.Now(), raw)

		time.Sleep(time.Second * 3)
		resp := SubmitComment(ctx, req).(*ghttp.JSONResponse)
		data := resp.Data.(helpers.JsonResp)
		if data.Code != v.output {
			fmt.Println(data)
			t.Errorf("code=%d, want=%d", data.Code, v.output)
		}
	}
}
