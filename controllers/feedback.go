package controllers

import (
	"context"
	"encoding/json"
	"net"
	"strconv"
	"strings"
	"time"

	"icode.baidu.com/baidu/gdp/env"
	"icode.baidu.com/baidu/gdp/ghttp"

	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/library/redis"
	"icode.baidu.com/helix_web/models"
)

const (
	portraitContentLimit = 128
	commentContentLimit  = 256
	openContentLimit     = 1024

	commentTimeLimit = 300

	portraitAwardNum = 5
	commentAwardNum  = 2

	coopNameLimit   = 128
	coopDemandLimit = 512

	FeedbackEmailDebug  = "<EMAIL>"
	FeedbackEmailTest   = "<EMAIL>"
	FeedbackEmailOnline = "<EMAIL>"
)

var FeedbackEmailMap = map[string]string{
	env.RunModeDebug:   FeedbackEmailDebug,
	env.RunModeTest:    FeedbackEmailTest,
	env.RunModeRelease: FeedbackEmailOnline,
}

func checkCoopType(coopType int64) bool {
	if coopType == models.CoopType1 ||
		coopType == models.CoopType10 ||
		coopType == models.CoopType20 ||
		coopType == models.CoopType30 ||
		coopType == models.CoopType40 {
		return true
	}
	return false
}

func checkCustomerType(customerType int64) bool {
	if customerType == models.CustomerTypeJG ||
		customerType == models.CustomerTypeGR {
		return true
	}
	return false
}

func checkBudgeType(budgetType int64) bool {
	if budgetType == models.BudgetType10 ||
		budgetType == models.BudgetType20 ||
		budgetType == models.BudgetType30 ||
		budgetType == models.BudgetType40 ||
		budgetType == models.BudgetType50 {
		return true
	}
	return false
}

func checkIndustry(industry int64) bool {
	if industry == models.Industry1 ||
		industry == models.Industry10 ||
		industry == models.Industry20 ||
		industry == models.Industry30 ||
		industry == models.Industry40 ||
		industry == models.Industry50 ||
		industry == models.Industry60 {
		return true
	}
	return false
}

// SubmitCoop 提交合作
func SubmitCoop(ctx context.Context, req ghttp.Request) ghttp.Response {
	params := helpers.GetBodyParams(ctx, req)
	coopType := helpers.GetIntParam(params, "coop_type", 0)
	productList := helpers.GetStringSliceParam(params, "product_list")
	demand := helpers.GetHTMLStringParam(params, "demand")
	customerType := helpers.GetIntParam(params, "customer_type", 0)
	industry := helpers.GetIntParam(params, "industry", 0)
	budgetType := helpers.GetIntParam(params, "budget_type", 0)
	companyName := helpers.GetHTMLStringParam(params, "company_name")
	name := helpers.GetHTMLStringParam(params, "name")
	position := helpers.GetHTMLStringParam(params, "position")
	phone := helpers.GetHTMLStringParam(params, "phone")
	email := helpers.GetHTMLStringParam(params, "email")
	captcha := helpers.GetHTMLStringParam(params, "captcha")
	token := helpers.GetHTMLStringParam(params, "token")

	// 参数校验
	if !checkCoopType(coopType) || len(productList) < 0 {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "coop_type or product_list param error")
	}
	if !checkCustomerType(customerType) {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "customer_type param error")
	}

	if len([]rune(companyName)) > coopNameLimit {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "coop_name param error")
	}
	demandLen := len([]rune(demand))
	if demandLen <= 0 || demandLen > coopDemandLimit {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "demand param error")
	}
	nameLen := len([]rune(name))
	if nameLen <= 0 || nameLen > coopNameLimit {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "contact_info param error")
	}
	phoneLen := len([]rune(phone))
	if phoneLen <= 0 || phoneLen > PhoneLengthLimit {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "phone param error")
	}
	if len([]rune(email)) > EmailLengthLimit {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "email param error")
	}
	if customerType == models.CustomerTypeJG {
		positionLen := len([]rune(position))
		if positionLen <= 0 || positionLen > coopNameLimit {
			return helpers.FailReturn(ctx, helpers.ParamErrorCode, "position param error")
		}
		if !checkIndustry(industry) {
			return helpers.FailReturn(ctx, helpers.ParamErrorCode, "industry param error")
		}
	}

	userId := getUserId(ctx)
	if userId <= 0 {
		// 限制频率
		host, _, _ := net.SplitHostPort(req.RemoteAddr())
		lockKey := redis.UserPortraitLock + host
		res := redis.SetNX(ctx, lockKey, true, redis.TaskUserNumLockTime)
		if !res {
			return helpers.FailReturn(ctx, helpers.ParamErrorCode, "request too fast, please repeat")
		}

		// check 验证码
		cacheKey := redis.CaptchaPrefix + token
		rightCaptcha := redis.Get(ctx, cacheKey)
		redis.Del(ctx, cacheKey)
		if len(captcha) <= 0 || strings.ToLower(captcha) != strings.ToLower(rightCaptcha) {
			return helpers.FailReturn(ctx, helpers.ParamErrorCode, "captcha param error")
		}
	} else {
		lockKey := redis.UserPortraitLock + strconv.Itoa(int(userId))
		res := redis.SetNX(ctx, lockKey, true, redis.TaskUserNumLockTime)
		if !res {
			return helpers.FailReturn(ctx, helpers.ParamErrorCode, "request too fast, please repeat")
		}
	}
	productListByte, _ := json.Marshal(productList)

	// 发送邮件
	feedbackEmail := FeedbackEmailMap[env.RunMode()]
	subject := "PaddleHelix 合作咨询"
	emailContent := "合作类型：" + models.CoopTypeMap[int(coopType)] + "<br>合作方向：" + string(productListByte) +
		"<br>需求描述：" + demand + "<br>合作身份：" + models.CustomerTypeMap[int(customerType)] +
		"<br>所在行业：" + models.IndustryMap[int(industry)] + "<br>机构名称：" + companyName +
		"<br>预算范围：" + models.BudgetTypeMap[int(budgetType)] + "<br>联系人姓名：" + name +
		"<br>联系人职位：" + position + "<br>手机号：" + phone + "<br>邮箱：" + email
	go helpers.SendEmailByBaidu(helpers.FromUserBaidu, feedbackEmail, subject, emailContent)

	// 落库
	coopM := models.Coop{
		UserId:       uint64(userId),
		Demand:       demand,
		CustomerType: uint64(customerType),
		CoopType:     uint64(coopType),
		Industry:     uint64(industry),
		BudgetType:   uint64(budgetType),
		ProductList:  string(productListByte),
		Name:         name,
		Position:     position,
		Phone:        phone,
		Email:        email,
		Company:      companyName,
	}
	_, err := coopM.Add(ctx, coopM)
	if err != nil {
		return helpers.NewException(ctx, "DATA_ERR")
	}

	return helpers.SuccReturn(ctx, nil)
}

// 关闭弹框
func ClosePortrait(ctx context.Context, req ghttp.Request) ghttp.Response {
	userId := getUserId(ctx)
	if userId > 0 {
		cacheKey := redis.UserPortraitBoxPrefix + strconv.Itoa(int(userId))

		t := time.Now().Add(time.Hour * 24)
		addTime := time.Date(t.Year(), t.Month(), t.Day(), 0, 0, 0, 0, t.Location())
		expire := addTime.Unix() - time.Now().Unix()
		redis.Set(ctx, cacheKey, true, int(expire))
	}

	return helpers.SuccReturn(ctx, nil)
}

// 评价奖励信息
func CommentAwardInfo(ctx context.Context, req ghttp.Request) ghttp.Response {
	t := time.Now()
	currMonthTime := time.Date(t.Year(), t.Month(), 1, 0, 0, 0, 0, t.Location())
	commentM := models.Comment{}

	var items []map[string]any
	for _, commentType := range models.AwardTypeList {
		tmp := map[string]any{
			"type":  commentType,
			"award": false,
		}

		row, _ := commentM.GetLastAward(ctx, getUserId(ctx), int64(commentType))
		if row.CreatedAt.Unix() < currMonthTime.Unix() {
			tmp["award"] = true // 可以奖励
		}
		items = append(items, tmp)
	}

	result := map[string]any{
		"items": items,
	}
	return helpers.SuccReturn(ctx, result)
}

// 任务评价信息
func TaskComment(ctx context.Context, req ghttp.Request) ghttp.Response {
	params := helpers.GetBodyParams(ctx, req)
	taskId := helpers.GetIntParam(params, "task_id", 0)
	if taskId <= 0 {
		return helpers.SuccReturn(ctx, nil)
	}

	commentM := models.Comment{}
	row, err := commentM.GetByTaskId(ctx, getUserId(ctx), taskId)
	if err != nil {
		return helpers.NewException(ctx, "DATA_ERR")
	}
	if row.ID <= 0 || row.Type != models.CommentType40 {
		return helpers.SuccReturn(ctx, nil)
	}

	result := map[string]any{
		"created_at": row.CreatedAt.Unix(),
		"score":      row.Score,
		"task_id":    row.TaskId,
		"task_type":  row.TaskType,
	}
	return helpers.SuccReturn(ctx, result)
}

// 提交用户画像
func SubmitUserPortrait(ctx context.Context, req ghttp.Request) ghttp.Response {
	params := helpers.GetBodyParams(ctx, req)
	channel := helpers.GetHTMLStringParam(params, "channel")
	demandList := helpers.GetStringSliceParam(params, "demand_list")
	industry := helpers.GetHTMLStringParam(params, "industry")
	career := helpers.GetHTMLStringParam(params, "career")
	captcha := helpers.GetHTMLStringParam(params, "captcha")
	token := helpers.GetHTMLStringParam(params, "token")
	code := helpers.GetHTMLStringParam(params, "code")

	// 参数校验
	channelLen := len([]rune(channel))
	if channelLen <= 0 || channelLen > portraitContentLimit {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "channel param error")
	}
	demandLen := len(demandList)
	if demandLen <= 0 || demandLen > 10 {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "demand param error")
	}
	demandByte, _ := json.Marshal(demandList)

	industryLen := len([]rune(industry))
	if industryLen <= 0 || industryLen > portraitContentLimit {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "industry param error")
	}
	careerLen := len([]rune(career))
	if careerLen <= 0 || careerLen > portraitContentLimit {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "career param error")
	}

	awardNum := 0
	userId := getUserId(ctx)
	if userId <= 0 {
		cookie, _ := req.Cookie("BAIDUID")
		lockKey := redis.UserPortraitLock + cookie.Value
		res := redis.SetNX(ctx, lockKey, true, redis.TaskUserNumLockTime)
		if !res {
			return helpers.FailReturn(ctx, helpers.ParamErrorCode, "request too fast, please repeat")
		}

		// check 验证码
		cacheKey := redis.CaptchaPrefix + token
		rightCaptcha := redis.Get(ctx, cacheKey)
		redis.Del(ctx, cacheKey)
		if strings.ToLower(captcha) != strings.ToLower(rightCaptcha) {
			return helpers.FailReturn(ctx, helpers.ParamErrorCode, "captcha param error")
		}

		if len(code) > 0 {
			portraitM := models.Portrait{}
			row, _ := portraitM.GetByCode(ctx, code)
			if row.ID > 0 {
				code = ""
			}
		}
	} else {
		lockKey := redis.UserPortraitLock + strconv.Itoa(int(userId))
		res := redis.SetNX(ctx, lockKey, true, redis.TaskUserNumLockTime)
		if !res {
			return helpers.FailReturn(ctx, helpers.ParamErrorCode, "request too fast, please repeat")
		}

		portraitM := models.Portrait{}
		row, err := portraitM.GetByUserId(ctx, userId)
		if err != nil {
			return helpers.NewException(ctx, "DATA_ERR")
		}
		if row.ID > 0 {
			return helpers.FailReturn(ctx, helpers.LogicErrorCode, "portrait has already been submitted")
		}

		// 发放奖励
		awardKey := redis.UserAwardPrefix + strconv.Itoa(int(userId))
		countStr := redis.Get(ctx, awardKey)
		count, _ := strconv.Atoi(countStr)
		count = count + portraitAwardNum
		redis.Set(ctx, awardKey, count, redis.TaskUserNumTime)

		awardNum = portraitAwardNum
	}

	userInfo := ctx.Value("user_info").(models.UserInfo)
	portraitNew := models.Portrait{
		UserId:   uint64(userId),
		Username: userInfo.DisplayName,
		Channel:  channel,
		Demand:   string(demandByte),
		Industry: industry,
		Career:   career,
		AwardNum: uint64(awardNum),
		Code:     code,
	}
	_, err := portraitNew.Add(ctx, portraitNew)
	if err != nil {
		return helpers.NewException(ctx, "DATA_ERR")
	}

	return helpers.SuccReturn(ctx, nil)
}

// 提交评价
func SubmitComment(ctx context.Context, req ghttp.Request) ghttp.Response {
	params := helpers.GetBodyParams(ctx, req)
	typ := helpers.GetIntParam(params, "type", 0)
	taskId := helpers.GetIntParam(params, "task_id", 0)
	score := helpers.GetFloatParam(params, "score", 0)
	content := helpers.GetStringSliceParam(params, "content")
	openContent := helpers.GetHTMLStringParam(params, "open_content")
	ExtraComment := helpers.GetMapSliceParam(params, "extra_comment")
	captcha := helpers.GetHTMLStringParam(params, "captcha")
	token := helpers.GetHTMLStringParam(params, "token")

	// 参数校验
	if !checkCommentType(typ) {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "type param error")
	}
	if score <= 0 || score > 5 {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "score param error")
	}

	contentStr := ""
	if len(content) > 0 {
		contentByte, _ := json.Marshal(content)
		contentStr = string(contentByte)
	}
	if len([]rune(contentStr)) > commentContentLimit {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "content param error")
	}
	openContentLen := len([]rune(openContent))
	if openContentLen > openContentLimit {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "open_content param error")
	}

	extraStr := ""
	if len(ExtraComment) > 0 {
		extraData, _ := json.Marshal(getExtraComment(ExtraComment))
		extraStr = string(extraData)
	}

	awardNum := 0
	userId := getUserId(ctx)
	if userId <= 0 {
		if typ != models.CommentType10 {
			return helpers.FailReturn(ctx, helpers.ParamErrorCode, "param error")
		}

		// 限制频率
		host, _, _ := net.SplitHostPort(req.RemoteAddr())
		lockKey := redis.UserPortraitLock + host
		res := redis.SetNX(ctx, lockKey, true, redis.TaskUserNumLockTime)
		if !res {
			return helpers.FailReturn(ctx, helpers.ParamErrorCode, "request too fast, please repeat")
		}

		// check 验证码
		cacheKey := redis.CaptchaPrefix + token
		rightCaptcha := redis.Get(ctx, cacheKey)
		redis.Del(ctx, cacheKey)
		if strings.ToLower(captcha) != strings.ToLower(rightCaptcha) {
			return helpers.FailReturn(ctx, helpers.CaptchaErrorCode, "captcha param error")
		}
	} else {
		lockKey := redis.UserCommentLock + strconv.Itoa(int(userId))
		res := redis.SetNX(ctx, lockKey, true, redis.UserCommentLockTime)
		if !res {
			return helpers.FailReturn(ctx, helpers.ParamErrorCode, "request too fast, please repeat")
		}

		// 发放奖励
		t := time.Now()
		currentMonthTime := time.Date(t.Year(), t.Month(), 1, 0, 0, 0, 0, t.Location())

		commentM := models.Comment{}
		record, _ := commentM.GetLastAward(ctx, userId, typ)
		if openContentLen > 0 && record.CreatedAt.Unix() < currentMonthTime.Unix() {
			awardKey := redis.UserAwardPrefix + strconv.Itoa(int(userId))
			countStr := redis.Get(ctx, awardKey)
			count, _ := strconv.Atoi(countStr)
			count = count + commentAwardNum
			redis.Set(ctx, awardKey, count, redis.TaskUserNumTime)

			awardNum = commentAwardNum
		}

		row, err := commentM.GetByUserId(ctx, userId, typ, taskId)
		if err != nil {
			return helpers.NewException(ctx, "DATA_ERR")
		}
		if row.ID > 0 && row.CreatedAt.Unix() > time.Now().Unix()-commentTimeLimit {
			row.Type = uint64(typ)
			row.Score = score
			row.Content = contentStr
			row.OpenContent = openContent
			row.ExtraComment = extraStr
			row.AwardNum = uint64(awardNum)
			row.CreatedAt = time.Now()
			_ = row.Save(ctx)

			return helpers.SuccReturn(ctx, nil)
		}
		if row.ID > 0 && taskId > 0 {
			return helpers.SuccReturn(ctx, nil)
		}
	}

	userInfo := ctx.Value("user_info").(models.UserInfo)
	commentNew := models.Comment{
		UserId:       uint64(userId),
		Username:     userInfo.DisplayName,
		Type:         uint64(typ),
		Score:        score,
		Content:      contentStr,
		OpenContent:  openContent,
		ExtraComment: extraStr,
		AwardNum:     uint64(awardNum),
	}
	if taskId > 0 && userId > 0 {
		taskM := models.Task{}
		taskInfo, _ := taskM.GetTaskById(ctx, taskId)
		commentNew.TaskId = taskInfo.ID
		commentNew.TaskType = taskInfo.Type
	}

	_, err := commentNew.Add(ctx, commentNew)
	if err != nil {
		return helpers.NewException(ctx, "DATA_ERR")
	}
	return helpers.SuccReturn(ctx, nil)
}

// 提交反馈表单
func SubmitFeedback(ctx context.Context, req ghttp.Request) ghttp.Response {
	params := helpers.GetBodyParams(ctx, req)
	content := helpers.GetHTMLStringParam(params, "content")
	email := helpers.GetHTMLStringParam(params, "email")
	name := helpers.GetHTMLStringParam(params, "name")
	captcha := helpers.GetHTMLStringParam(params, "captcha")
	token := helpers.GetHTMLStringParam(params, "token")

	contentLen := len([]rune(content))
	emailLen := len([]rune(email))
	nameLen := len([]rune(name))
	if contentLen <= 0 || contentLen > ContentLengthLimit {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "content param error")
	}
	if emailLen <= 0 || emailLen > EmailLengthLimit {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "email param error")
	}
	if nameLen > NameLengthLimit {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "name param error")
	}

	if len([]rune(captcha)) != CaptchaLength {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "captcha param error")
	}
	if len([]rune(token)) != CaptchaTokenLength {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "captcha param error")
	}

	// 获取验证码
	cacheKey := redis.CaptchaPrefix + token
	rightCaptcha := redis.Get(ctx, cacheKey)
	redis.Del(ctx, cacheKey)
	if strings.ToLower(captcha) != strings.ToLower(rightCaptcha) {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "captcha param error")
	}

	// 发送邮件
	feedbackEmail := FeedbackEmailMap[env.RunMode()]
	subject := "PaddleHelix 问题反馈"
	emailContent := "反馈问题：" + content + "\n邮箱：" + email + "\n姓名：" + name
	go helpers.SendEmailByBaidu(helpers.FromUserBaidu, feedbackEmail, subject, emailContent)

	// 数据落库
	feedbackNew := models.Feedback{
		Name:    name,
		Email:   email,
		Content: content,
	}
	_, err := feedbackNew.Add(ctx, feedbackNew)
	if err != nil {
		return helpers.NewException(ctx, "DB_ERR")
	}

	return helpers.SuccReturn(ctx, nil)
}

// 提交计费申请表单
func SubmitApply(ctx context.Context, req ghttp.Request) ghttp.Response {
	params := helpers.GetBodyParams(ctx, req)
	content := helpers.GetHTMLStringParam(params, "content")
	email := helpers.GetHTMLStringParam(params, "email")
	phone := helpers.GetHTMLStringParam(params, "phone")
	name := helpers.GetHTMLStringParam(params, "name")
	company := helpers.GetHTMLStringParam(params, "company")
	captcha := helpers.GetHTMLStringParam(params, "captcha")
	token := helpers.GetHTMLStringParam(params, "token")

	contentLen := len([]rune(content))
	emailLen := len([]rune(email))
	phoneLen := len([]rune(phone))
	if contentLen <= 0 || contentLen > ContentLengthLimit {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "content param error")
	}
	if emailLen <= 0 || emailLen > EmailLengthLimit {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "email param error")
	}
	if phoneLen <= 0 || phoneLen > PhoneLengthLimit {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "phone param error")
	}

	companyLen := len([]rune(company))
	nameLen := len([]rune(name))
	if companyLen <= 0 || companyLen > CompanyLengthLimit {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "company param error")
	}
	if nameLen > NameLengthLimit {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "name param error")
	}

	if len([]rune(captcha)) != CaptchaLength {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "captcha error")
	}
	if len([]rune(token)) != CaptchaTokenLength {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "captcha error")
	}

	// 获取验证码
	cacheKey := redis.CaptchaPrefix + token
	rightCaptcha := redis.Get(ctx, cacheKey)
	redis.Del(ctx, cacheKey)
	if strings.ToLower(captcha) != strings.ToLower(rightCaptcha) {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "captcha error")
	}

	// 获取邮件配置
	confMap, err := helpers.ReadTomlFile(env.ConfDir() + "/biz.toml")
	if err != nil {
		go helpers.LogError(ctx, err)
		go helpers.HelixNotice(ctx, err.Error())
	}
	chargeApplyEmail, ok := confMap["ChargeApplyEmail"]
	if !ok || len(chargeApplyEmail.(string)) <= 0 {
		go helpers.HelixNotice(ctx, "bos.toml ServerOutputPath empty")
	} else {
		// 发送邮件
		subject := "PaddleHelix 商业账户申请"
		emailContent := "商业账户申请：\n合作内容：" + content + "\n邮箱：" +
			email + "\n手机号：" + phone + "\n公司：" + company + "\n姓名：" + name
		go helpers.SendEmailByBaidu(helpers.FromUserBaidu, chargeApplyEmail.(string), subject, emailContent)
	}

	// 数据落库
	feedbackNew := models.Feedback{
		Name:    name,
		Phone:   phone,
		Email:   email,
		Company: company,
		Content: content,
	}
	_, _ = feedbackNew.Add(ctx, feedbackNew)

	return helpers.SuccReturn(ctx, nil)
}

func checkCommentType(typ int64) bool {
	if typ == models.CommentType10 ||
		typ == models.CommentType20 ||
		typ == models.CommentType30 ||
		typ == models.CommentType40 ||
		typ == models.CommentType50 ||
		typ == models.CommentType60 {
		return true
	}

	return false
}

type extraCommentStruct struct {
	Typ         int64    `json:"type"`
	Score       float64  `json:"score"`
	Content     []string `json:"content"`
	OpenContent string   `json:"open_content"`
}

func getExtraComment(extraComment []any) []any {
	extraByte, err := json.Marshal(extraComment)
	if err != nil {
		return []any{}
	}

	var extraData []extraCommentStruct
	if err := json.Unmarshal(extraByte, &extraData); err != nil {
		return []any{}
	}

	var items []any
	for _, item := range extraData {
		tmp := map[string]any{
			"type":         item.Typ,
			"score":        item.Score,
			"content":      item.Content,
			"open_content": item.OpenContent,
		}
		items = append(items, tmp)
	}
	return items
}
