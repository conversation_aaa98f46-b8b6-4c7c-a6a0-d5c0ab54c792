package controllers

import (
	"context"
	"encoding/json"
	"fmt"

	"icode.baidu.com/baidu/gdp/ghttp"
	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/library/ctxutils"
	"icode.baidu.com/helix_web/models"
	"icode.baidu.com/helix_web/services"
)

const (
	maxTargetSequenceLength = 1000
	defaultBinderMinLength  = 50
	defaultBinderMaxLength  = 100
	minBinderLengthDiff     = 5
)

var (
	binderMinLengthRange = []int{3, 245}
	binderMaxLengthRange = []int{8, 250}

	miniProteinDesignModeMap = map[string]struct{}{
		"basic": {},
	}
)

type MiniProteinDesignTask struct {
	JobName         string `json:"job_name,omitempty"`
	TargetSequence  string `json:"target_sequence,omitempty"`
	BinderMinLength int64  `json:"binder_min_length,omitempty"`
	BinderMaxLength int64  `json:"binder_max_length,omitempty"`
	DesignMode      string `json:"design_mode,omitempty"`
}

type MiniProteinDesignConf struct {
	MiniProteinDesignTask
	Cost   float64
	Tokens uint64
}

// BatchSubmitHelixFold3 批量提交任务
func BatchSubmitMiniProteinDesignTask(ctx context.Context, req ghttp.Request) ghttp.Response {
	// 获取参数
	params := helpers.GetBodyParams(ctx, req)
	tasks := helpers.GetMapSliceParam(params, "tasks")
	if len(tasks) <= 0 {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "parm tasks is empty")
	}
	newTasks := assembleMiniProteinDesignTask(tasks)
	if len(newTasks) <= 0 {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "param tasks is empty")
	}
	// 校验任务参数
	if err := checkMiniProteinDesignTaskParam(ctx, newTasks); err != nil {
		// 返还试用次数
		returnTrialTime(ctx)
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, err.Error())
	}
	// TODO 校验权限
	// 计算任务费用并组装任务
	taskConfs, _, err := figureMiniProteinDesignTasksCost(ctx, newTasks, true)
	if err != nil {
		// 返还试用次数
		returnTrialTime(ctx)
		return helpers.FailReturn(ctx, helpers.ServiceErrorCode, err.Error())
	}
	// TODO 校验余额是否充足
	// 提交调度任务
	taskIDs := submitMiniProteinDesignTasks(ctx, taskConfs)
	// 返回结果
	result := map[string]any{
		"task_ids": taskIDs,
	}
	return helpers.SuccReturn(ctx, result)
}

func assembleMiniProteinDesignTask(tasks []any) []MiniProteinDesignTask {
	var miniProteinDesignTasks []MiniProteinDesignTask
	for _, task := range tasks {
		t, _ := task.(map[string]any)
		var tt MiniProteinDesignTask
		_ = mapToStruct(t, &tt)
		tt.JobName = getTaskName(tt.JobName)
		if tt.BinderMinLength == 0 {
			tt.BinderMinLength = int64(defaultBinderMinLength)
		}
		if tt.BinderMaxLength == 0 {
			tt.BinderMaxLength = int64(defaultBinderMaxLength)
		}
		miniProteinDesignTasks = append(miniProteinDesignTasks, tt)
	}
	return miniProteinDesignTasks
}

func checkMiniProteinDesignTaskParam(ctx context.Context, tasks []MiniProteinDesignTask) error {
	for _, task := range tasks {
		// name
		if len([]rune(task.JobName)) > MaxTaskNameLimit || !IsValidTaskName(task.JobName) {
			return fmt.Errorf("task %s param error: job_name/name is invalid. The job_name/name "+
				"should not exceed %d characters in length", task.JobName, MaxTaskNameLimit)
		}
		// target_sequence
		if len(task.TargetSequence) > maxTargetSequenceLength {
			return fmt.Errorf("task %s param error: the length of target_sequence should <= %d", task.JobName, maxTargetSequenceLength)
		}
		// binder_min_length
		if task.BinderMinLength < int64(binderMinLengthRange[0]) || task.BinderMinLength > int64(binderMinLengthRange[1]) {
			return fmt.Errorf("task %s param error: the value of binder_min_length should >= %d and <= %d",
				task.JobName, binderMinLengthRange[0], binderMinLengthRange[1])
		}
		// binder_max_length
		if task.BinderMaxLength < int64(binderMaxLengthRange[0]) || task.BinderMaxLength > int64(binderMaxLengthRange[1]) {
			return fmt.Errorf("task %s param error: the value of binder_max_length should >= %d and <= %d",
				task.JobName, binderMaxLengthRange[0], binderMaxLengthRange[1])
		}
		// max - min >= 30
		if task.BinderMaxLength-task.BinderMinLength < minBinderLengthDiff {
			return fmt.Errorf("task %s param error: the value of binder_max_length - binder_min_length should >= %d", task.JobName, minBinderLengthDiff)
		}
		// design_mode
		if _, ok := miniProteinDesignModeMap[task.DesignMode]; !ok {
			return fmt.Errorf("task %s param error: the value of design_mode is invalid", task.JobName)
		}
	}
	return nil
}

func figureMiniProteinDesignTasksCost(ctx context.Context, tasks []MiniProteinDesignTask,
	isTrial bool) ([]*MiniProteinDesignConf, float64, error) {
	var taskConfs []*MiniProteinDesignConf
	taskCost := 0.0
	for _, task := range tasks {
		if task.JobName == "" {
			task.JobName = getTaskName(task.JobName)
		}
		// 计算费用，本期不计费
		cost := 0.0
		tokens := 0
		taskConf := &MiniProteinDesignConf{
			MiniProteinDesignTask: task,
			Cost:                  cost,
			Tokens:                uint64(tokens),
		}
		taskConfs = append(taskConfs, taskConf)
		taskCost += cost
	}
	return taskConfs, taskCost, nil
}

func submitMiniProteinDesignTasks(ctx context.Context, taskConfs []*MiniProteinDesignConf) []uint64 {
	isApi := 0
	if _, ok := ctx.Value("is_api").(int); ok {
		isApi = 1
	}
	if _, ok := ctx.Value("is_api").(int); ok {
		isApi = 1
	}
	var taskIDs []uint64
	for _, taskConf := range taskConfs {
		// TODO 冻结代金券
		billingUnitCount := 0.0
		newCtx := context.WithValue(ctx, "billing_unit_count", billingUnitCount)
		// 组装参数
		extendParams := models.TaskMiniProteinDesignConf{
			TargetSequence:  taskConf.TargetSequence,
			BinderMinLength: taskConf.BinderMinLength,
			BinderMaxLength: taskConf.BinderMaxLength,
			DesignMode:      taskConf.DesignMode,
			Tokens:          taskConf.Tokens,
		}
		extendParamsByte, _ := json.Marshal(extendParams)
		taskN := models.Task{
			Name:            taskConf.JobName,
			UserId:          uint64(getUserId(ctx)),
			Type:            uint64(models.TaskTypeMiniProteinDesign),
			FuncType:        uint64(models.FuncTypeForecast),
			Config:          string(extendParamsByte),
			IsApi:           uint64(isApi),
			IAMUserID:       ctxutils.GetIAMUserID(ctx),
			IAMUserDomainID: ctxutils.GetIAMUserDomainID(ctx),
			NTokens:         taskConf.Tokens,
			Balance:         billingUnitCount * BILLING_UNIT_PRICE,
		}
		taskNew, err := taskN.Add(ctx, taskN)
		if err != nil {
			taskIDs = append(taskIDs, 0)
			// 返还代金券或试用次数
			returnTrialTime(ctx)
			continue
		}
		taskIDs = append(taskIDs, taskNew.ID)
		// submit task
		if err = services.PreSubmitTaskChpc(newCtx, taskNew); err != nil {
			// 返还代金券或试用次数
			returnTrialTime(ctx)
			// 更新任务状态和失败原因
			taskNew.Status = int64(models.TaskStatusFailed)
			taskNew.JobFailReason = err.Error()
			_ = taskNew.Save(ctx)
			continue
		}
	}
	return taskIDs
}
