package controllers

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"math"
	"math/rand"
	"os"
	"path/filepath"
	"reflect"
	"regexp"
	"strconv"
	"strings"
	"unicode"

	"icode.baidu.com/baidu/gdp/ghttp"
	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/library/algorithm"
	"icode.baidu.com/helix_web/library/bce"
	"icode.baidu.com/helix_web/library/bceaa"
	"icode.baidu.com/helix_web/library/chpc"
	"icode.baidu.com/helix_web/library/ctxutils"
	"icode.baidu.com/helix_web/library/redis"
	"icode.baidu.com/helix_web/library/tool"
	"icode.baidu.com/helix_web/models"
	pb "icode.baidu.com/helix_web/proto/generate/proto"
	"icode.baidu.com/helix_web/services"
)

const (
	DEFAULT_ENSEMBLE = 1
	DEFAULT_RECYCLE  = 10

	BILLING_UNIT_PRICE = 0.068704549

	MAX_BASE_TOKEN_COUNT = 3000
	MAX_S1_TOKEN_COUNT   = 1000

	maxConstraints = 10

	maxS1SampleConstraints = 10

	defaultModelType = modelTypeBase
)

const (
	constraintTypeDistance = "distance"

	constraintLevelResidue = "residue"
	constraintLevelChain   = "chain"

	entityTypeDNA     = "dna"
	entityTypeProtein = "protein"
	entityTypeRNA     = "rna"
	entityTypeIon     = "ion"
	entityTypeLigand  = "ligand"

	modelTypeBase = "HelixFold3"
	modelTypeS1   = "HelixFold-S1"
)

var (
	randomSeedGenerator = rand.New(rand.NewSource(1))
	entityType          = map[string]struct{}{
		"protein": {}, "dna": {}, "rna": {}, "ion": {}, "ligand": {},
	}
	aminoAcidTable = map[rune]struct{}{
		'A': {}, 'C': {}, 'D': {}, 'E': {}, 'F': {},
		'G': {}, 'H': {}, 'I': {}, 'K': {}, 'L': {},
		'M': {}, 'N': {}, 'P': {}, 'Q': {}, 'R': {},
		'S': {}, 'T': {}, 'V': {}, 'W': {}, 'Y': {},
	}
	ccdNameTable = map[string]struct{}{
		"MG": {}, "ZN": {}, "CL": {}, "CA": {}, "NA": {},
		"MN": {}, "MN3": {}, "K": {}, "FE": {}, "FE2": {},
		"CU": {}, "CU1": {}, "CU3": {}, "CO": {},
	}
	dnaNucleotideTable = map[rune]struct{}{
		'A': {}, 'T': {}, 'C': {}, 'G': {},
	}
	rnaNucleotideTable = map[rune]struct{}{
		'A': {}, 'U': {}, 'C': {}, 'G': {},
	}
	nucleotideTable = map[rune]struct{}{
		'A': {}, 'U': {}, 'C': {}, 'G': {}, 'T': {},
	}
	commonModificationsCCDTable = map[string]map[string]map[string]string{
		"dna": {
			"A": {"6MA": "N6-Methyl-2-Deoxy-Adenosine", "3DR": "abasic dideoxyribose"},
			"C": {"5CM": "5-Methyl-2-Deoxy-Cytidine", "C34": "N4-Methyl-2-Deoxy-Cytidine", "5HC": "5-Hydroxymethyl-2-Deoxy-Cytidine",
				"1CC": "5-Carboxy-2-Deoxy-Cytidine", "5FC": "5-Formylcytosine", "3DR": "abasic dideoxyribose"},
			"T": {"3DR": "abasic dideoxyribose"},
			"G": {"6OG": "6-O-Methyl Guanosine", "8OG": "8-oxoguanine", "3DR": "abasic dideoxyribose"},
		},
		"rna": {
			"A": {"A2M": "2'0-Methyladenosine", "MA6": "6N-Dimethyladenosine", "6MZ": "N6-Methyladenosine"},
			"C": {"5MC": "5-Methylcytidine", "OMC": "02'-Methylcytidine", "4OC": "4N,02'-Methylcytidine", "RSQ": "5-Formylcytosine"},
			"U": {"5MU": "5-Methyluridine", "OMU": "02'-Methyluridine", "UR3": "3-Methyluridine", "PSU": "Pseudouridine"},
			"G": {"2MG": "2N-Methylguanosine", "OMG": "O2'-Methylguanosine", "7MG": "7N-Methyl-8-Hydroguanosine"},
		},
		"protein": {
			"R": {"2MR": "N3,N4-Dimethyl-L-arginine", "AGM": "5-Methyl-L-arginine", "CIR": "Citrulline"},
			"C": {"MCS": "Malonyl cysteine", "P1L": "S-Palmitoyl-L-cysteine", "SNC": "S-Nitroso-Cysteine"},
			"H": {"NEP": "N1-Phosphohistidine", "HIP": "ND1-Phosphohistidine"},
			"K": {"ALY": "N6-Acetyl-L-lysine", "MLY": "N-Dimethyl-L-lysine", "M3L": "N-Trimethyllysine", "MLZ": "N6-Methyllysine",
				"LYZ": "5-Hydroxylysine", "KCR": "N-6-Crotonyl-L-Lysine", "YHA": "Homocitrulline"},
			"N": {"AHB": "3-Hydroxyasparagine", "SNN": "L-3-Aminosuccinimide"},
			"P": {"HYP": "4-Hydroxyproline", "HY3": "3-Hydroxyproline"},
			"S": {"SEP": "Phosphoserine"},
			"T": {"TPO": "Phosphothreonine"},
			"W": {"TRF": "N1-Formyl-Tryptophan"},
			"Y": {"PTR": "O-Phospho-L-tyrosine"},
		},
	}
	modelTypeMaxTokenTable = map[string]uint64{
		modelTypeBase: MAX_BASE_TOKEN_COUNT,
		modelTypeS1:   MAX_S1_TOKEN_COUNT,
	}
)

// BatchSubmitHelixFold3 批量提交任务
func BatchSubmitHelixFold3(ctx context.Context, req ghttp.Request) ghttp.Response {
	// 获取请求参数，组装成helixfold3的任务参数
	params := helpers.GetBodyParams(ctx, req)
	tasks := helpers.GetMapSliceParam(params, "tasks")
	if len(tasks) <= 0 {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "parm tasks is empty")
	}
	newTasks := assembleHelixFold3Task(tasks)
	taskIDs, errCode, errMsg := batchSubmitHelixFold3(ctx, newTasks)
	if errCode != 0 {
		return helpers.FailReturn(ctx, errCode, errMsg)
	}
	// 返回结果
	result := map[string]any{
		"task_ids": taskIDs,
	}
	return helpers.SuccReturn(ctx, result)
}

func CheckHelixFold3RefStructure(ctx context.Context, req ghttp.Request) ghttp.Response {
	// 获取请求参数，组装成helixfold3的任务参数
	params := helpers.GetBodyParams(ctx, req)
	tasks := helpers.GetMapSliceParam(params, "tasks")
	if len(tasks) <= 0 {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "parm tasks is empty")
	}
	newTasks := assembleHelixFold3Task(tasks)
	// 校验参考构象
	for _, task := range newTasks {
		if err := checkHF3TaskRefStructures(task.Entities, task.RefStructures); err != nil {
			return helpers.FailReturn(ctx, helpers.ParamErrorCode, err.Error())
		}
	}
	return helpers.SuccReturn(ctx, nil)
}

func batchSubmitHelixFold3(ctx context.Context, tasks []HelixFold3Task) ([]uint64, int, string) {
	user := ctxutils.GetUserInfo(ctx)
	if len(tasks) <= 0 {
		return nil, helpers.ParamErrorCode, "param tasks is empty"
	}
	if len(tasks) > 20 {
		return nil, helpers.ParamErrorCode, "task count value out of range [1, 20]"
	}
	// 校验任务参数
	if err := checkTaskParam(ctx, tasks); err != nil {
		return nil, helpers.ParamErrorCode, err.Error()
	}
	// 校验用户权限
	isTrial, err := checkUserPermission(ctx, tasks, &user)
	if err != nil {
		return nil, helpers.AccountErrorCode, err.Error()
	}
	// 计算任务费用
	taskConfs, taskCost, err := figureTasksCost(ctx, tasks, isTrial)
	if err != nil {
		// 试用减掉使用次数
		if isTrial {
			cacheKey := redis.UserHelixFold3TaskNumPrefix + strconv.Itoa(int(user.UserID))
			redis.IncrBy(ctx, cacheKey, -1)
		}
		return nil, helpers.ParamErrorCode, err.Error()
	}
	// 校验余额是否充足，包括helix平台代金券、billing余额/代金券
	if !isTrial {
		if err := checkUserBalance(ctx, taskCost, models.TaskTypeHelixFoldAA); err != nil {
			return nil, helpers.AccountErrorCode, err.Error()
		}
	}
	// 遍历提交任务
	taskIDs, failTaskIDs := submitTasks(ctx, taskConfs)
	// 失败任务减掉使用次数
	if len(failTaskIDs) > 0 {
		if isTrial {
			cacheKey := redis.UserHelixFold3TaskNumPrefix + strconv.Itoa(int(user.UserID))
			redis.IncrBy(ctx, cacheKey, -1)
		}
	}
	return taskIDs, 0, ""
}

// SubmitHelixfoldaa 提交helixfoldaa任务，包括参数校验、数据组装和任务提交等操作
// ctx: 上下文对象，包含请求的信息和应用程序状态
// req: HTTP请求，包含HTTP方法、URL、头部、查询字符串和请求体等信息
// 返回值：ghttp.Response类型，包含响应状态码、响应头部、响应体和错误信息（如果有）
func SubmitHelixfoldaa(ctx context.Context, req ghttp.Request) ghttp.Response {
	// get param from req
	params := helpers.GetBodyParams(ctx, req)
	name := helpers.GetStringParam(params, "job_name")
	if len(name) == 0 {
		name = helpers.GetStringParam(params, "name")
	}
	name = getTaskName(name)
	randomSeed := helpers.GetIntParam(params, "random_seed", randomSeedGenerator.Int63())
	recycle := helpers.GetIntParam(params, "recycle", DEFAULT_RECYCLE)
	ensemble := helpers.GetIntParam(params, "ensemble", DEFAULT_ENSEMBLE)
	entities := helpers.GetMapSliceParam(params, "entities")
	constraint := helpers.GetMapSliceParam(params, "constraint")
	refStructures := helpers.GetMapSliceParam(params, "ref_structures")
	modleType := helpers.GetStringParam(params, "model_type")
	if len(modleType) == 0 {
		modleType = modelTypeBase
	}
	newEntities := assembleHelixFoldAAEntities(entities)
	newConstraint := assembleHelixFoldAAConstraint(constraint)
	newRefStructures := assembleHelixFoldAARefStructures(refStructures)
	task := HelixFold3Task{
		Name:          name,
		RandomSeed:    randomSeed,
		Recycle:       recycle,
		Ensemble:      ensemble,
		Entities:      newEntities,
		Constraint:    newConstraint,
		RefStructures: newRefStructures,
		ModelType:     modleType,
	}
	taskIDs, errCode, errMsg := batchSubmitHelixFold3(ctx, []HelixFold3Task{task})
	if errCode != 0 {
		return helpers.FailReturn(ctx, errCode, errMsg)
	}
	if len(taskIDs) != 1 {
		return helpers.FailReturn(ctx, helpers.LogicErrorCode, "submit task failed")
	}
	result := map[string]any{
		"task_id": taskIDs[0],
	}
	return helpers.SuccReturn(ctx, result)
}

// freezeUserCoupon freezeUserCoupon 根据上下文中的用户类型，冻结优惠券并返回冻结优惠券字符串和剩余金额
// ctx context.Context 上下文对象
// cost float64 需要冻结的金额，必须大于等于0
// 返回值1 string 冻结优惠券字符串，如果用户类型为试用或内部用户则为空字符串
// 返回值2 float64 剩余金额，如果用户类型为试用或内部用户则为cost
// 返回值3 error 错误信息，如果用户类型无效则返回错误
func freezeUserCoupon(ctx context.Context, cost float64) (string, float64, error) {
	if cost <= 0 {
		return "", 0, nil
	}
	userType := ctxutils.GetUserInfo(ctx).Type
	// 只有试用账户可以使用代金券
	if userType == int64(models.UserTypeTry) {
		couponM := models.Coupon{}
		freezeCouponsStr, cost, err := couponM.Charge(ctx, ctxutils.GetUserID(ctx), uint64(models.TaskTypeHelixFoldAA), cost)
		if err != nil {
			return "", 0, err
		}
		return freezeCouponsStr, cost, nil
	}
	return "", cost, nil
}

// checkHF3CommonParam 检查HF3公共参数是否合法，返回一个布尔值和错误信息
func checkHF3CommonParam(ctx context.Context, name string, recycle int64, ensemble int64,
	newEntities []models.HelixFoldAAEntity) error {
	if len([]rune(name)) > MaxTaskNameLimit || !IsValidTaskName(name) { // name
		return fmt.Errorf("job_name/name is invalid. The job_name/name should not exceed %d characters in length", MaxTaskNameLimit)
	}
	if recycle < 10 || recycle > 100 { // recycle
		return errors.New("param recycle value out of range [10, 100]")
	}
	if ensemble < 1 || ensemble > 100 { // ensemble
		return errors.New("param ensemble value out of range [1, 100]")
	}
	if len(newEntities) <= 0 {
		return errors.New("param entities is empty")
	}
	return nil
}

// getTokenCount 获取token数量
// ctx context.Context 上下文对象
// entities []models.HelixFoldAAEntity 实体列表，包含类型、数量、序号、CCD和SMILES等信息
// 返回值 uint64 返回查询结果的token数量，如果出现错误则返回0
func getTokenCount(ctx context.Context, entities []models.HelixFoldAAEntity) uint64 {
	var pbEntities []*pb.Entity
	for _, entity := range entities {
		pbEntity := pb.Entity{
			Type:     entity.Type,
			Count:    uint64(entity.Count),
			Sequence: entity.Sequence,
			Ccd:      entity.Ccd,
			Smiles:   entity.Smiles,
		}
		var modifications []*pb.Modification
		for _, mod := range entity.Modifications {
			modifications = append(modifications, &pb.Modification{
				Type:        mod.Type,
				Index:       uint64(mod.Index),
				Ccd:         mod.Ccd,
				RSmiles:     mod.RSmiles,
				RConnectIdx: uint64(mod.RConnectIdx),
			})
		}
		pbEntity.Modification = modifications
		pbEntities = append(pbEntities, &pbEntity)
	}
	request := &pb.GetEntitiesTokenCountRequest{
		Entities: pbEntities,
	}
	resp, err := algorithm.GetSerialServiceClient().GetEntitiesTokenCount(ctx, request)
	if err != nil {
		helpers.LogError(ctx, err)
		helpers.HelixNotice(ctx, "----query entities token count from helix-algorithm-support service err---"+err.Error())
		return 0
	}
	return resp.Count
}

// checkHF3TaskEntity 检查HelixFoldAA任务参数是否合法，如果不合法则返回false和错误信息，否则返回true和nil
func checkHF3TaskEntity(ctx context.Context, entities []models.HelixFoldAAEntity) error {
	for _, entity := range entities {
		if entity.Type == "" { // type
			return fmt.Errorf("type of entity is empty")
		}
		if _, exist := entityType[entity.Type]; !exist {
			return fmt.Errorf("type of entity is invalid")
		}
		switch entity.Type {
		case "protein":
			if err := checkHF3ProteinParam(ctx, entity); err != nil {
				return err
			}
		case "dna":
			if err := checkHF3DNAParam(ctx, entity); err != nil {
				return err
			}
		case "rna":
			if err := checkHF3RNAParam(ctx, entity); err != nil {
				return err
			}
		case "ion":
			if err := checkHF3IONParam(ctx, entity); err != nil {
				return err
			}
		case "ligand":
			if err := checkHF3LigandParam(ctx, entity); err != nil {
				return err
			}
		default:
			return errors.New("type of entity is invalid")
		}
	}
	return nil
}

// checkHF3LigandParam 检查HF3蛋白质参数是否合法，如果不合法则返回false和nil错误；如果合法则返回true和nil错误
// ctx: 上下文对象，context.Context类型
// entities: []models.HelixFoldAAEntity，需要检查的HF3蛋白质实体列表，models.HelixFoldAAEntity类型
// entity: models.HelixFoldAAEntity，待检查的单个HF3蛋白质实体，models.HelixFoldAAEntity类型
// 返回值：
// bool，如果参数合法则为true，否则为false
// error，如果参数合法则为nil，否则为非nil错误
func checkHF3LigandParam(ctx context.Context, entity models.HelixFoldAAEntity) error {
	if len(entity.Ccd) == 0 && len(entity.Smiles) == 0 { // ccd smiles
		return errors.New("ccd and smiles of entity is empty")
	}
	if len(entity.Ccd) != 0 {
		ccdList, err := services.NewCcdService().QueryCcdList(ctx, entity.Ccd, 1, 1)
		if err != nil {
			return err
		}
		if len(ccdList) != 0 && len(ccdList[0].Smiles) != 0 {
			entity.Smiles = ccdList[0].Smiles
		}
	}
	if len(entity.Smiles) == 0 {
		return errors.New("smiles of entity is empty")
	}
	if entity.Count <= 0 || entity.Count > 50 { // count
		return errors.New("count of entity should be greater than 0 and less than or equal to 50")
	}
	if strings.Contains(entity.Smiles, ".") {
		return errors.New("SMILES containing a period (.) are not supported. Please submit each disconnected components separately.")
	}
	return nil
}

// checkHF3IONParam 检查HF3ION参数是否合法，返回一个布尔值和错误信息
func checkHF3IONParam(ctx context.Context, entity models.HelixFoldAAEntity) error {
	if len(entity.Ccd) == 0 { // sequence
		return errors.New("ccd of entity is empty")
	}
	if _, ok := ccdNameTable[entity.Ccd]; !ok {
		return errors.New("invalid ccd name")
	}
	if entity.Count <= 0 || entity.Count > 50 { // count
		return errors.New("count of entity should be greater than 0 and less than or equal to 50")
	}
	return nil
}

// checkHF3RNAParam 检查HF3RNA参数是否合法，如果不合法则返回false和nil错误；否则返回true和nil错误
// ctx: 上下文对象，可以为空
// entities: []models.HelixFoldAAEntity类型，包含所有需要检查的实体信息
// entity: models.HelixFoldAAEntity类型，需要检查的单个实体信息
// 返回值：bool, error类型，第一个bool表示是否合法，第二个error类型表示错误，可能为nil
func checkHF3RNAParam(ctx context.Context, entity models.HelixFoldAAEntity) error {
	if len(entity.Sequence) == 0 { // sequence
		return errors.New("sequence of entity is empty")
	}
	for _, token := range entity.Sequence {
		if _, ok := rnaNucleotideTable[token]; !ok {
			return errors.New("invalid nucleotide")
		}
	}
	if entity.Count <= 0 || entity.Count > 2000 { // count
		return errors.New("count of entity should be greater than 0 and less than or equal to 2000")
	}
	for _, modification := range entity.Modifications {
		switch modification.Type {
		case "residue_replace":
			if modification.Index < 1 || modification.Index > int64(len(entity.Sequence)) {
				return errors.New("index of modification should >= 1 and <= length of sequence")
			}
			if len(modification.Ccd) <= 0 {
				return errors.New("ccd of modification is empty")
			}
			v1, exist := commonModificationsCCDTable["rna"][string(entity.Sequence[modification.Index-1])]
			if !exist {
				return errors.New("invalid modification")
			}
			if _, exist = v1[modification.Ccd]; !exist {
				return errors.New("invalid modification")
			}
		case "sidechain_replace":
			if modification.Index < 1 || modification.Index > int64(len(entity.Sequence)) {
				return errors.New("index of modification should >= 1 and <= length of sequence")
			}
			if len(modification.RSmiles) == 0 {
				return errors.New("smiles of sidechain_replace modification is empty")
			}
			if strings.Contains(modification.RSmiles, ".") {
				return errors.New("SMILES containing a period (.) are not supported. Please submit each disconnected components separately")
			}
		default:
			return errors.New("invalid modification type")
		}
	}
	return nil
}

// checkHF3DNAParam 检查给定的实体是否符合 HF3DNA 参数要求，如果不符合则返回错误信息。
// ctx context.Context 上下文对象（可选）
// entities []models.HelixFoldAAEntity 包含多个 HelixFoldAAEntity 类型的切片，用于判断是否存在重复序列（可选）
// entity models.HelixFoldAAEntity 需要检查的单个 HelixFoldAAEntity 类型的实体
// 返回值 bool 表示是否符合要求，true 表示符合，false 表示不符合
// 返回值 error 表示错误信息，如果没有错误则为 nil
func checkHF3DNAParam(ctx context.Context, entity models.HelixFoldAAEntity) error {
	if len(entity.Sequence) == 0 { // sequence
		return errors.New("sequence of entity is empty")
	}
	for _, token := range entity.Sequence {
		if _, ok := dnaNucleotideTable[token]; !ok {
			return errors.New("invalid nucleotide")
		}
	}
	if entity.Count <= 0 || entity.Count > 2000 { // count
		return errors.New("count of entity should be greater than 0 and less than or equal to 2000")
	}
	for _, modification := range entity.Modifications {
		switch modification.Type {
		case "residue_replace":
			if modification.Index < 1 || modification.Index > int64(len(entity.Sequence)) {
				return errors.New("index of modification should >= 1 and <= length of sequence")
			}
			if len(modification.Ccd) <= 0 {
				return errors.New("ccd of modification is empty")
			}
			v1, exist := commonModificationsCCDTable["dna"][string(entity.Sequence[modification.Index-1])]
			if !exist {
				return errors.New("invalid modification")
			}
			if _, exist = v1[modification.Ccd]; !exist {
				return errors.New("invalid modification")
			}
		case "sidechain_replace":
			if modification.Index < 1 || modification.Index > int64(len(entity.Sequence)) {
				return errors.New("index of modification should >= 1 and <= length of sequence")
			}
			if len(modification.RSmiles) == 0 {
				return errors.New("smiles of sidechain_replace modification is empty")
			}
			if strings.Contains(modification.RSmiles, ".") {
				return errors.New("SMILES containing a period (.) are not supported. Please submit each disconnected components separately")
			}
		default:
			return errors.New("invalid modification type")
		}
	}
	return nil
}

// checkHF3ProteinParam 检查HF3蛋白质参数是否合法，如果不合法则返回false和nil错误；否则返回true和nil错误
func checkHF3ProteinParam(ctx context.Context, entity models.HelixFoldAAEntity) error {
	if len(entity.Sequence) == 0 { // sequence
		return errors.New("sequence of entity is empty")
	}
	for _, token := range entity.Sequence {
		if _, ok := aminoAcidTable[token]; !ok {
			return errors.New("invalid amino")
		}
	}
	if entity.Count <= 0 { // count
		return errors.New("count of entity is less than 1")
	}
	for _, modification := range entity.Modifications {
		switch modification.Type {
		case "residue_replace", "glycos":
			if modification.Index < 1 || modification.Index > int64(len(entity.Sequence)) {
				helpers.LogError(ctx, fmt.Errorf("invalid modification index: %v", modification.Index))
				return errors.New("index of modification should >= 1 and <= length of sequence")
			}
			if len(modification.Ccd) <= 0 && len(modification.Expression) <= 0 {
				helpers.LogError(ctx, fmt.Errorf("invalid modification ccd or expression: %v", modification))
				return errors.New("ccd and expression of modification is empty")
			}
			if len(modification.Ccd) > 0 {
				v1, exist := commonModificationsCCDTable["protein"][string(entity.Sequence[modification.Index-1])]
				if !exist {
					helpers.LogError(ctx, fmt.Errorf("invalid modification: %v", modification))
					return errors.New("invalid modification")
				}
				if _, exist = v1[modification.Ccd]; !exist {
					helpers.LogError(ctx, fmt.Errorf("invalid modification ccd: %v", modification))
					return errors.New("invalid modification")
				}
			}
		case "sidechain_replace":
			if modification.Index < 1 || modification.Index > int64(len(entity.Sequence)) {
				return errors.New("index of modification should >= 1 and <= length of sequence")
			}
			if len(modification.RSmiles) == 0 {
				return errors.New("smiles of sidechain_replace modification is empty")
			}
			if strings.Contains(modification.RSmiles, ".") {
				return errors.New("SMILES containing a period (.) are not supported. Please submit each disconnected components separately")
			}
		default:
			return errors.New("invalid modification type")
		}
	}
	return nil
}

func assembleHelixFoldAAEntities(entities []any) []models.HelixFoldAAEntity {
	var entityList []models.HelixFoldAAEntity
	for _, entity := range entities {
		e, _ := entity.(map[string]any)
		var ee models.HelixFoldAAEntity
		_ = mapToStruct(e, &ee)
		entityList = append(entityList, ee)
	}
	// 解析modifications
	for i, entity := range entities {
		e, _ := entity.(map[string]any)
		modifications, ok := e["modification"].([]any)
		if !ok {
			continue
		}
		var mms []models.ModificationEntity
		for _, modification := range modifications {
			var mm models.ModificationEntity
			m, _ := modification.(map[string]any)
			_ = mapToStruct(m, &mm)
			mms = append(mms, mm)

		}
		entityList[i].Modifications = mms
	}
	return entityList
}

func assembleHelixFoldAAConstraint(constraints []any) []models.HelixFoldAAConstraint {
	var constraintList []models.HelixFoldAAConstraint
	for _, constraint := range constraints {
		e, _ := constraint.(map[string]any)
		var ee models.HelixFoldAAConstraint
		_ = mapToStruct(e, &ee)
		constraintList = append(constraintList, ee)
	}
	return constraintList
}

func assembleHelixFoldAAS1SampleConstrain(constraints []any) []models.HelixFoldAAS1SampleConstraint {
	var constraintList []models.HelixFoldAAS1SampleConstraint
	for _, constraint := range constraints {
		e, _ := constraint.(map[string]any)
		var ee models.HelixFoldAAS1SampleConstraint
		_ = mapToStruct(e, &ee)
		constraintList = append(constraintList, ee)
	}
	return constraintList
}

func assembleHelixFoldAARefStructures(refStructures []any) []models.HelixFoldAARefStructure {
	var refStructureList []models.HelixFoldAARefStructure
	for _, refStructure := range refStructures {
		e, _ := refStructure.(map[string]any)
		var ee models.HelixFoldAARefStructure
		_ = mapToStruct(e, &ee)
		ee.ReferTargetPairs = assembleHelixFoldAAReferTargePairs(e["refer_target_pairs"].([]any))
		refStructureList = append(refStructureList, ee)
	}
	return refStructureList
}

func assembleHelixFoldAAReferTargePairs(referTargePairs []any) []models.HelixFoldAAReferTargetPair {
	var referTargePairList []models.HelixFoldAAReferTargetPair
	for _, referTargePair := range referTargePairs {
		e, _ := referTargePair.(map[string]any)
		var ee models.HelixFoldAAReferTargetPair
		_ = mapToStruct(e, &ee)
		referTargePairList = append(referTargePairList, ee)
	}
	return referTargePairList
}

// mapToStruct 将一个map转换为结构体，并赋值到结构体中，如果map中的key与结构体中的字段不匹配则会被忽略
// 参数m：需要转换的map，类型为map[string]any
// 参数result：结构体的指针，类型为interface{}
// 返回值error：错误信息，如果没有错误则为nil
func mapToStruct(m map[string]any, result interface{}) error {
	val := reflect.ValueOf(result).Elem()
	for k, v := range m {
		field := val.FieldByName(strings.Title(toCamelCase(k)))
		if field.IsValid() && field.CanSet() {
			val := reflect.ValueOf(v)
			if field.Type() != val.Type() {
				val = convertVarToType(val, field.Type())
			}
			if field.Type() == val.Type() {
				field.Set(val)
			}
		}
	}
	return nil
}

// toCamelCase 将下划线命名转换为驼峰命名
func toCamelCase(s string) string {
	// 使用 strings.Builder 来高效地构建结果字符串
	var result strings.Builder
	// 标记下一个字符是否应该大写
	nextUpper := false

	// 遍历输入字符串的每个字符
	for _, r := range s {
		// 如果当前字符是下划线，则设置标记为 true
		if r == '_' {
			nextUpper = true
			continue
		}

		// 根据标记决定是将字符转换为大写还是小写
		if nextUpper {
			result.WriteRune(unicode.ToUpper(r))
			nextUpper = false // 重置标记
		} else {
			result.WriteRune(unicode.ToLower(r))
		}
	}

	// 将 strings.Builder 转换为字符串并返回
	return result.String()
}

func convertVarToType(value reflect.Value, t reflect.Type) reflect.Value {
	if value.Type().ConvertibleTo(t) {
		return value.Convert(t)
	}
	// 如果遇到类型转换问题，可能需要自定义转换逻辑
	return value
}

// calculateT calculateT 计算 T 值，参数 L 为 uint64 类型，ensemble 和 recycle 为 int64 类型，返回值为 float64 类型，保留两位小数
func calculateT(L uint64, ensemble int64, recycle int64) float64 {
	// Given constants
	a := 0.00014
	b := 1.7

	// Calculate the formula
	t := (a*math.Pow(float64(L), b)*float64(ensemble)*float64(recycle) + 5) * 0.3

	// Round to two decimal places and return
	return t
}

type HelixFold3Task struct {
	JobName            string                                 `json:"job_name,omitempty"`
	Name               string                                 `json:"name,omitempty"`
	Recycle            int64                                  `json:"recycle,omitempty"`
	Ensemble           int64                                  `json:"ensemble,omitempty"`
	ModelType          string                                 `json:"model_type,omitempty"`
	Entities           []models.HelixFoldAAEntity             `json:"entities,omitempty"`
	Constraint         []models.HelixFoldAAConstraint         `json:"constraint,omitempty"`
	RefStructures      []models.HelixFoldAARefStructure       `json:"ref_structures,omitempty"`
	S1SampleConstraint []models.HelixFoldAAS1SampleConstraint `json:"s1_sample_constraint,omitempty"`
	RandomSeed         int64                                  `json:"random_seed,omitempty"`
}

type HelixFold3Conf struct {
	HelixFold3Task
	Cost   float64
	Tokens uint64
}

func checkUserPermission(ctx context.Context, newTasks []HelixFold3Task, user *models.UserInfo) (bool, error) {
	isTrial := ctxutils.GetIsTrial(ctx)
	if isTrial {
		// 免费次数无法提交多个任务
		// base 模型 Ensemble 必须为1，Recycle 必须为10，才能使用免费次数
		// s1 模型 Ensemble 必须为20，Recycle 必须为10，才能使用免费次数
		if len(newTasks) > 1 ||
			(newTasks[0].ModelType == modelTypeBase && (newTasks[0].Ensemble != 1 || newTasks[0].Recycle != 10)) ||
			(newTasks[0].ModelType == modelTypeS1 && (newTasks[0].Ensemble != 20 || newTasks[0].Recycle != 10)) {
			// 减掉使用次数，并将isTrial置为false
			cacheKey := redis.UserHelixFold3TaskNumPrefix + strconv.Itoa(int(user.UserID))
			redis.IncrBy(ctx, cacheKey, -1)
			isTrial = false
		}
	}
	// 未开通chpc服务的非内部账户无法提交非试用任务
	open := chpc.IsOpenCHPCService(ctxutils.GetIAMUserDomainID(ctx))
	if !isTrial && !open && user.Type != int64(models.UserTypeInside) {
		return isTrial, fmt.Errorf("account not open chpc service")
	}
	// 非试用商业账户需要校验是否欠费
	if !isTrial && user.Type == int64(models.UserTypeCharge) {
		// 用户是否欠费
		debt, err := chpc.QueryIAMUserIsArrears(ctx, ctxutils.GetIAMUserDomainID(ctx))
		if err != nil {
			return isTrial, fmt.Errorf("query account fund status failure")
		}
		if debt {
			return isTrial, fmt.Errorf("account arrears，please recharge")
		}
	}
	return isTrial, nil
}

func checkTaskParam(ctx context.Context, tasks []HelixFold3Task) error {
	for i, task := range tasks {
		// name
		if len([]rune(task.Name)) > MaxTaskNameLimit || !IsValidTaskName(task.Name) {
			return fmt.Errorf("task %s param error: job_name/name is invalid. The job_name/name "+
				"should not exceed %d characters in length", task.Name, MaxTaskNameLimit)
		}
		// recycle
		if task.Recycle < 10 || task.Recycle > 100 {
			return fmt.Errorf("task %s param error: recycle value out of range [10, 100]", task.Name)
		}
		// ensemble
		if task.Ensemble < 1 || task.Ensemble > 100 {
			return fmt.Errorf("task %s param error: ensemble value out of range [1, 100]", task.Name)
		}
		// entities
		if len(task.Entities) <= 0 {
			return fmt.Errorf("task %s param error: entities is empty", task.Name)
		}
		if task.ModelType == modelTypeS1 {
			count := 0
			for _, e := range task.Entities {
				count += int(e.Count)
			}
			if count < 2 {
				return fmt.Errorf("task %s param error: the count of entities in s1 must be greater than 1", task.Name)
			}
		}
		if err := checkHF3TaskEntity(ctx, task.Entities); err != nil {
			return err
		}
		// constraint
		if err := checkHF3TaskConstraint(task.Entities, task.Constraint); err != nil {
			return err
		}
		// refStructures
		// API暂不支持参考构象
		isApi := 0
		if _, ok := ctx.Value("is_api").(int); ok {
			isApi = 1
		}
		if isApi == 1 && len(task.RefStructures) > 0 {
			return fmt.Errorf(`"ref_structures" does not supported through API now`)
		}
		if err := checkHF3TaskRefStructures(tasks[i].Entities, tasks[i].RefStructures); err != nil {
			return err
		}
		// s1SampleConstraint
		if err := checkHF3TaskS1SampleConstraint(task.Entities, task.S1SampleConstraint); err != nil {
			return err
		}
	}
	return nil
}

func checkHF3TaskS1SampleConstraint(entities []models.HelixFoldAAEntity, s1SampleConstraint []models.HelixFoldAAS1SampleConstraint) error {
	if len(s1SampleConstraint) > maxS1SampleConstraints {
		return fmt.Errorf("the number of s1_sample_constraint cannot exceed %d", maxS1SampleConstraints)
	}
	for _, c := range s1SampleConstraint {
		leftIndexs := strings.Split(c.LeftEntity, "-")
		if len(leftIndexs) != 2 {
			return fmt.Errorf("ref target format is invalid")
		}
		if err := checkHF3TaskEntityIndexs(entities, leftIndexs); err != nil {
			return fmt.Errorf("ref target is invalid: %w", err)
		}
		rightIndexs := strings.Split(c.RightEntity, "-")
		if len(rightIndexs) != 2 {
			return fmt.Errorf("ref target format is invalid")
		}
		if err := checkHF3TaskEntityIndexs(entities, rightIndexs); err != nil {
			return fmt.Errorf("ref target is invalid: %w", err)
		}
	}

	return nil
}

func checkHF3TaskRefStructures(entities []models.HelixFoldAAEntity, refStructures []models.HelixFoldAARefStructure) error {
	for i, refStructure := range refStructures {
		if refStructure.RefFile == "" {
			return fmt.Errorf("reference file of reference structures is empty")
		}
		// 处理参考文件路径
		refStructures[i].RefFile = strings.TrimSuffix(refStructures[i].RefFile, "qt")
		bucket, object := helpers.DealBosFileUrl(refStructures[i].RefFile)
		var content string
		var err error
		if bucket != "paddle-helix" {
			content, err = bce.GetObject(bucket, object)
			if err != nil {
				return fmt.Errorf("reference file get failed: %w", err)
			}
			if err = bceaa.PutObject(object, content); err != nil {
				return fmt.Errorf("reference file put failure: %w", err)
			}
			refStructures[i].RefFile = strings.Replace(refStructures[i].RefFile, bucket, "paddle-helix", 1)
		} else {
			content, err = bceaa.GetObject(bucket, object)
			if err != nil {
				return fmt.Errorf("reference file get failed: %w", err)
			}
		}
		for _, referTargePair := range refStructure.ReferTargetPairs {
			if !regexp.MustCompile(`^[a-zA-Z0-9]+$`).MatchString(referTargePair.Refer) {
				return fmt.Errorf("参考结构中的链名只能包含数字和字母")
			}
			indexs := strings.Split(referTargePair.Target, "-")
			if len(indexs) != 2 {
				return fmt.Errorf("ref target format is invalid")
			}
			if err := checkHF3TaskEntityIndexs(entities, indexs); err != nil {
				return fmt.Errorf("ref target is invalid: %w", err)
			}
			// 算法侧校验
			// 生成临时文件
			tempFilePath := helpers.GetRandomString(10) + filepath.Ext(object)
			tempFile, err := os.CreateTemp("", tempFilePath)
			if err != nil {
				return fmt.Errorf("create temp file failed: %w", err)
			}
			// 运行结束后删除文件
			defer os.Remove(tempFile.Name())
			// 写入数据
			if _, err = tempFile.WriteString(content); err != nil {
				return fmt.Errorf("write temp file failed: %w", err)
			}
			entityIndex, _ := strconv.Atoi(indexs[0])
			// 传给算法校验
			param := tool.Hf3RefCheckParam{
				CifPath:      tempFile.Name(),
				ReferChainID: referTargePair.Refer,
				HitPdbCode:   strings.TrimSuffix(filepath.Base(object), filepath.Ext(object)),
				Sequence:     entities[entityIndex-1].Sequence,
			}
			if err := tool.Hf3RefCheck(param); err != nil {
				return err
			}
		}
	}
	return nil
}

func checkHF3TaskConstraint(entities []models.HelixFoldAAEntity, constraints []models.HelixFoldAAConstraint) error {
	if len(constraints) > maxConstraints {
		return fmt.Errorf("constraint number exceeds the maximum limit")
	}
	for _, constraint := range constraints {
		switch constraint.Type {
		case constraintTypeDistance:
			if err := checkHF3TaskConstraintDistance(entities, constraint); err != nil {
				return err
			}
		default:
			return fmt.Errorf("constraint type is invalid")
		}
	}
	return nil
}

func checkHF3TaskConstraintDistance(entities []models.HelixFoldAAEntity, constraint models.HelixFoldAAConstraint) error {
	if constraint.LeftEntity == constraint.RightEntity {
		return fmt.Errorf("left entity and right entity can not be the same entity")
	}
	if constraint.Distance < 2 || constraint.Distance > 20 {
		return fmt.Errorf("distance value out of range [2, 20]")
	}
	switch constraint.Level {
	case constraintLevelResidue:
		// x-x-x
		leftIndexs := strings.Split(constraint.LeftEntity, "-")
		if len(leftIndexs) != 3 {
			return fmt.Errorf("left entity index format is invalid")
		}
		if err := checkHF3TaskEntityIndexs(entities, leftIndexs); err != nil {
			return fmt.Errorf("left entity index is invalid: %w", err)
		}
		rightIndexs := strings.Split(constraint.RightEntity, "-")
		if len(rightIndexs) != 3 {
			return fmt.Errorf("right entity index format is invalid")
		}
		if err := checkHF3TaskEntityIndexs(entities, rightIndexs); err != nil {
			return fmt.Errorf("right entity index is invalid: %w", err)
		}
	case constraintLevelChain:
		// x-x
		leftIndexs := strings.Split(constraint.LeftEntity, "-")
		if len(leftIndexs) != 2 {
			return fmt.Errorf("left entity index format is invalid")
		}
		if err := checkHF3TaskEntityIndexs(entities, leftIndexs); err != nil {
			return fmt.Errorf("left entity index is invalid: %w", err)
		}
		rightIndexs := strings.Split(constraint.RightEntity, "-")
		if len(rightIndexs) != 2 {
			return fmt.Errorf("right entity index format is invalid")
		}
		if err := checkHF3TaskEntityIndexs(entities, rightIndexs); err != nil {
			return fmt.Errorf("right entity index is invalid: %w", err)
		}
	default:
		return fmt.Errorf("constraint level is invalid")
	}
	return nil
}

func checkHF3TaskEntityIndexs(entities []models.HelixFoldAAEntity, indexs []string) error {
	// 第一个数字代表实体序号，与 entities 字段对齐，从1开始
	entityIndex, err := strconv.Atoi(indexs[0])
	if err != nil {
		return fmt.Errorf("entity index format is invalid")
	}
	if entityIndex <= 0 || entityIndex > len(entities) {
		return fmt.Errorf("entity index out of range")
	}
	if len(indexs) == 1 {
		return nil
	}
	// 第二个数字代表当前实体类型的第几个，与实体的 count 字段对应，从1开始
	countIndex, err := strconv.Atoi(indexs[1])
	if err != nil {
		return fmt.Errorf("entity index format is invalid")
	}
	if countIndex <= 0 || countIndex > int(entities[entityIndex-1].Count) {
		return fmt.Errorf("entity index out of range")
	}
	if len(indexs) == 2 {
		return nil
	}
	// 第三个数字代表当前实体的第几个残基，与实体的 sequence 字段对应，从1开始
	sequenceIndex, err := strconv.Atoi(indexs[2])
	if err != nil {
		return fmt.Errorf("entity index format is invalid")
	}
	switch entities[entityIndex-1].Type {
	case entityTypeProtein, entityTypeDNA, entityTypeRNA:
		if sequenceIndex <= 0 || sequenceIndex > len(entities[entityIndex-1].Sequence) {
			return fmt.Errorf("entity index out of range")
		}
	case entityTypeIon, entityTypeLigand:
		if sequenceIndex != 1 {
			return fmt.Errorf("entity index out of range")
		}
	default:
		return fmt.Errorf("entity type is invalid")
	}
	return nil
}

// assembleHelixFold3Task 将任务列表中的任务转换为HelixFold3Task类型，并返回新的任务列表
// 参数tasks：[]any类型，包含任务列表
// 返回值[]HelixFold3Task类型，包含新的任务列表
func assembleHelixFold3Task(tasks []any) []HelixFold3Task {
	var newTasks []HelixFold3Task
	for _, task := range tasks {
		// 解析name、recycle、ensemble
		t, _ := task.(map[string]any)
		var tt HelixFold3Task
		_ = mapToStruct(t, &tt)
		if tt.Recycle == 0 {
			tt.Recycle = DEFAULT_RECYCLE
		}
		if tt.Ensemble == 0 {
			tt.Ensemble = DEFAULT_ENSEMBLE
		}
		if tt.ModelType == "" {
			tt.ModelType = defaultModelType
		}
		// 解析entities
		entities, _ := t["entities"].([]any)
		newEntities := assembleHelixFoldAAEntities(entities)
		tt.Entities = newEntities
		// constraints
		constraint, _ := t["constraint"].([]any)
		newConstraint := assembleHelixFoldAAConstraint(constraint)
		tt.Constraint = newConstraint
		// ref_structures
		refStructures, _ := t["ref_structures"].([]any)
		newRefStructures := assembleHelixFoldAARefStructures(refStructures)
		tt.RefStructures = newRefStructures
		// s1_sample_constraint
		s1SampleConstraint, _ := t["s1_sample_constraint"].([]any)
		newS1SampleConstraint := assembleHelixFoldAAS1SampleConstrain(s1SampleConstraint)
		tt.S1SampleConstraint = newS1SampleConstraint

		newTasks = append(newTasks, tt)
	}
	return newTasks
}

func figureTasksCost(ctx context.Context, tasks []HelixFold3Task, isTrial bool) ([]*HelixFold3Conf, float64, error) {
	var taskConfs []*HelixFold3Conf
	taskCost := 0.0
	user := ctxutils.GetUserInfo(ctx)
	for _, task := range tasks {
		if task.Name == "" {
			if task.JobName != "" {
				task.Name = task.JobName
			} else {
				task.Name = getTaskName(task.Name)
			}
		}
		if task.Recycle == 0 {
			task.Recycle = DEFAULT_RECYCLE
		}
		if task.Ensemble == 0 {
			task.Ensemble = DEFAULT_ENSEMBLE
		}
		// check token length
		tokens := getTokenCount(ctx, task.Entities)
		if tokens > modelTypeMaxTokenTable[task.ModelType] {
			return nil, 0, fmt.Errorf("task %s token error: the range of token quantity for a single task is 1-%d, tokens: %d",
				task.Name, modelTypeMaxTokenTable[task.ModelType], tokens)
		}

		// check cost
		cost, billingUnitCount := 0.0, 0.0
		if !isTrial && user.Type != int64(models.UserTypeInside) {
			// 试用和内部账户不计费
			billingUnitCount = calculateT(tokens, task.Ensemble, task.Recycle)
			cost = billingUnitCount * BILLING_UNIT_PRICE
		}
		taskConf := &HelixFold3Conf{
			HelixFold3Task: task,
			Cost:           cost,
			Tokens:         tokens,
		}
		taskConfs = append(taskConfs, taskConf)
		taskCost += cost
	}
	return taskConfs, taskCost, nil
}

func submitTasks(ctx context.Context, taskConfs []*HelixFold3Conf) ([]uint64, []uint64) {
	isApi := 0
	if _, ok := ctx.Value("is_api").(int); ok {
		isApi = 1
	}
	var taskIDs, failTaskIDs []uint64
	couponM := models.Coupon{}
	for _, taskConf := range taskConfs {
		// 冻结代金券
		freezeCouponsStr, cost, err := freezeUserCoupon(ctx, taskConf.Cost)
		if err != nil {
			taskIDs = append(taskIDs, 0)
			continue
		}
		billingUnitCount := math.Ceil(cost / BILLING_UNIT_PRICE)
		newCtx := context.WithValue(ctx, "billing_unit_count", billingUnitCount)
		// assemble data
		extendParams := models.TaskHelixFoldAAConf{
			Recycle:            taskConf.Recycle,
			Ensemble:           taskConf.Ensemble,
			RandomSeed:         taskConf.RandomSeed,
			ModelType:          taskConf.ModelType,
			S1SampleConstraint: taskConf.S1SampleConstraint,
			Entities:           taskConf.Entities,
			Constraints:        taskConf.Constraint,
			RefStructures:      taskConf.RefStructures,
			Tokens:             taskConf.Tokens,
		}
		extendParamsByte, _ := json.Marshal(extendParams)
		taskN := models.Task{
			Name:            taskConf.Name,
			UserId:          uint64(getUserId(ctx)),
			FuncType:        uint64(models.FuncTypeForecast),
			Config:          string(extendParamsByte),
			IsApi:           uint64(isApi),
			IAMUserID:       ctxutils.GetIAMUserID(ctx),
			IAMUserDomainID: ctxutils.GetIAMUserDomainID(ctx),
			NTokens:         taskConf.Tokens,
			Coupons:         freezeCouponsStr,
			Balance:         billingUnitCount * BILLING_UNIT_PRICE,
		}
		switch taskConf.ModelType {
		case modelTypeBase:
			taskN.Type = uint64(models.TaskTypeHelixFoldAA)
		case modelTypeS1:
			taskN.Type = uint64(models.TaskTypeHelixFold3S1)
		}
		taskNew, err := taskN.Add(ctx, taskN)
		if err != nil {
			taskIDs = append(taskIDs, 0)
			// 返还代金券
			if err := couponM.Return(ctx, freezeCouponsStr); err != nil {
				helpers.DBLogError(ctx, err)
			}
			continue
		}
		taskIDs = append(taskIDs, taskNew.ID)
		// submit task
		if err = services.PreSubmitTaskChpc(newCtx, taskNew); err != nil {
			// 返还代金券
			if err := couponM.Return(ctx, freezeCouponsStr); err != nil {
				helpers.DBLogError(ctx, err)
			}
			failTaskIDs = append(failTaskIDs, taskNew.ID)
			taskNew.Status = int64(models.TaskStatusFailed)
			taskNew.JobFailReason = err.Error()
			_ = taskNew.Save(ctx)
			continue
		}
	}
	return taskIDs, failTaskIDs
}

// QuerySubmitHelixFold3TaskPrice 查询helixfold3任务金额
func QuerySubmitHelixFold3TaskPrice(ctx context.Context, req ghttp.Request) ghttp.Response {
	params := helpers.GetBodyParams(ctx, req)
	name := helpers.GetStringParam(params, "job_name")
	if len(name) == 0 {
		name = helpers.GetStringParam(params, "name")
	}
	_ = helpers.GetIntParam(params, "random_seed", randomSeedGenerator.Int63())
	recycle := helpers.GetIntParam(params, "recycle", DEFAULT_RECYCLE)
	ensemble := helpers.GetIntParam(params, "ensemble", DEFAULT_ENSEMBLE)
	entities := helpers.GetMapSliceParam(params, "entities")
	modelType := helpers.GetStringParam(params, "model_type")
	if modelType == "" {
		modelType = modelTypeBase
	}
	isApi := 0
	if _, ok := ctx.Value("is_api").(int); ok {
		isApi = 1
	}

	// check common param
	name = getTaskName(name)
	newEntities := assembleHelixFoldAAEntities(entities)
	if err := checkHF3CommonParam(ctx, name, recycle, ensemble, newEntities); err != nil {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, err.Error())
	}

	// check task param
	if err := checkHF3TaskEntity(ctx, newEntities); err != nil {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, err.Error())
	}

	// check token length
	tokens := getTokenCount(ctx, newEntities)
	if tokens > modelTypeMaxTokenTable[modelType] {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, fmt.Sprintf("The range of token quantity for a single task is 1-%d.",
			modelTypeMaxTokenTable[modelType]))
	}

	// compute cost
	var cost float64
	if isApi == 1 {
		billingUnitCount := calculateT(tokens, ensemble, recycle)
		cost = billingUnitCount * BILLING_UNIT_PRICE
		cost = math.Ceil(cost/BILLING_UNIT_PRICE) * BILLING_UNIT_PRICE
	}
	couponM := models.Coupon{}
	coupon, err := couponM.QueryAvailableCouponAmountForTask(ctx, ctxutils.GetUserID(ctx), models.TaskTypeHelixFoldAA)
	if err != nil {
		return helpers.FailReturn(ctx, helpers.AccountErrorCode, err.Error())
	}
	_ = math.Max(cost-coupon, 0)

	// 返回结果
	prices := make([]*TaskPriceInfo, 0)
	prices = append(prices, &TaskPriceInfo{
		Name:  name,
		Price: keepTwoDecimalPlaces(cost),
	})
	taskPriceInfos := TaskPriceInfos{
		Prices:     prices,
		TotalPrice: keepTwoDecimalPlaces(cost),
	}
	return helpers.SuccReturn(ctx, taskPriceInfos)
}

// QueryBatchSubmitHelixFold3TaskPrice 查询helixfold3多任务金额
func QueryBatchSubmitHelixFold3TaskPrice(ctx context.Context, req ghttp.Request) ghttp.Response {
	params := helpers.GetBodyParams(ctx, req)
	tasks := helpers.GetMapSliceParam(params, "tasks")
	if len(tasks) <= 0 {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "tasks not exist")
	}
	newTasks := assembleHelixFold3Task(tasks)
	if len(tasks) <= 0 {
		return helpers.FailReturn(ctx, helpers.ParamErrorCode, "check tasks param error")
	}
	isApi := 0
	if _, ok := ctx.Value("is_api").(int); ok {
		isApi = 1
	}
	// compute task cost
	totalPrice := 0.0
	prices := make([]*TaskPriceInfo, 0)
	for _, task := range newTasks {
		name := task.JobName
		if len(name) == 0 {
			name = task.Name
		}
		recycle := task.Recycle
		ensemble := task.Ensemble
		entities := task.Entities
		if recycle == 0 {
			recycle = DEFAULT_RECYCLE
		}
		if ensemble == 0 {
			ensemble = DEFAULT_ENSEMBLE
		}
		// check common param
		name = getTaskName(name)
		task.Name = name
		if err := checkHF3CommonParam(ctx, name, recycle, ensemble, entities); err != nil {
			return helpers.FailReturn(ctx, helpers.ParamErrorCode, err.Error())
		}
		// check task param
		if err := checkHF3TaskEntity(ctx, entities); err != nil {
			return helpers.FailReturn(ctx, helpers.ParamErrorCode, err.Error())
		}
		// check token length
		tokens := getTokenCount(ctx, entities)
		if tokens > modelTypeMaxTokenTable[task.ModelType] {
			return helpers.FailReturn(ctx, helpers.ParamErrorCode, fmt.Sprintf("The range of token quantity for a single task is 1-%d.",
				modelTypeMaxTokenTable[task.ModelType]))
		}
		// check cost
		cost, billingUnitCount := 0.0, 0.0
		if isApi == 1 {
			billingUnitCount = calculateT(tokens, ensemble, recycle)
			cost = billingUnitCount * BILLING_UNIT_PRICE
			cost = math.Ceil(cost/BILLING_UNIT_PRICE) * BILLING_UNIT_PRICE
		}
		cost = keepTwoDecimalPlaces(cost)
		prices = append(prices, &TaskPriceInfo{
			Name:  name,
			Price: cost,
		})
		totalPrice += cost
	}
	// 返回结果
	couponM := models.Coupon{}
	coupon, err := couponM.QueryAvailableCouponAmountForTask(ctx, ctxutils.GetUserID(ctx), models.TaskTypeHelixFoldAA)
	if err != nil {
		return helpers.FailReturn(ctx, helpers.DBErrorCode, "query available coupon failure")
	}
	_ = math.Max(totalPrice-coupon, 0)
	taskPriceInfos := TaskPriceInfos{
		Prices:     prices,
		TotalPrice: keepTwoDecimalPlaces(totalPrice),
	}
	return helpers.SuccReturn(ctx, taskPriceInfos)
}

// keepTwoDecimalPlaces 功能：将一个浮点数转换为保留两位小数的浮点数。
// 参数：n float64 - 需要转换的浮点数。
// 返回值：float64 - 转换后的浮点数，保留两位小数。
func keepTwoDecimalPlaces(n float64) float64 {
	n, _ = strconv.ParseFloat(fmt.Sprintf("%.2f", n), 64)
	return n
}
