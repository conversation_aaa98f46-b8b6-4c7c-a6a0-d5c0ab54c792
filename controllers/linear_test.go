package controllers

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http/httptest"
	"testing"
	"time"

	"icode.baidu.com/baidu/gdp/ghttp"
	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/models"
)

// TestSubmitUTR 测试SubmitUTR函数，该函数用于提交UTR信息。
// 参数t：*testing.T类型，表示当前测试用例。
// 返回值：无返回值
func TestSubmitUTR(t *testing.T) {
	ctx := context.Background()
	ctx = context.WithValue(ctx, "user_info", models.UserInfo{UserID: 1, Type: 1})

	input1, _ := json.<PERSON>(map[string]interface{}{
		"cds_serial":   "serialxxx",
		"target_len":   25,
		"given_utr":    true,
		"utr_serial":   "serialxxx",
		"utr_file_url": "xxx",
	})
	input3, _ := json.<PERSON>(map[string]interface{}{
		"cds_file_url": "bos:/bml-test-test/helix_upload/fb5d8890-d7b0-41e0-ada1-da73270dba9b.txt",
		"target_len":   25,
	})
	input4, _ := json.Marshal(map[string]interface{}{
		"cds_file_url": "xxx",
		"name":         "test002001",
	})

	caseList := []struct {
		input  io.Reader
		output int
	}{
		{nil, helpers.ParamErrorCode},
		{bytes.NewReader(input1), helpers.SuccessCode},
		{bytes.NewReader(input3), helpers.SuccessCode},
		{bytes.NewReader(input4), helpers.ParamErrorCode},
	}

	for _, v := range caseList {
		raw := httptest.NewRequest("POST", "/", v.input)
		req := ghttp.NewRequest(time.Now(), raw)

		resp := SubmitUTR(ctx, req).(*ghttp.JSONResponse)
		data := resp.Data.(helpers.JsonResp)
		if data.Code != v.output {
			fmt.Println(data)
			t.Errorf("code=%d, want=%d", data.Code, v.output)
		}
	}
}

// TestSubmitLinearFold 测试SubmitLinearFold函数，该函数用于提交线性折叠任务
// 参数t *testing.T：表示当前的测试对象
// 返回值无
func TestSubmitLinearFold(t *testing.T) {
	ctx := context.Background()
	ctx = context.WithValue(ctx, "user_info", models.UserInfo{UserID: 1, Type: 1})

	// 测试用例1：只有fold功能的请求
	input1, _ := json.Marshal(map[string]interface{}{
		"name":           "test_fold_only",
		"sequence":       "AUGCGGCUACGCGCGCGCGCGCGC",
		"need_fold":      true,
		"need_partition": false,
		"fold_config": map[string]interface{}{
			"beam_size":   100,
			"zuker_score": 5,
			"constraint":  false,
		},
	})

	// 测试用例2：同时有fold和partition功能的请求
	input2, _ := json.Marshal(map[string]interface{}{
		"name":           "test_fold_and_partition",
		"sequence":       "AUGCGGCUACGCGCGCGCGCGCGC",
		"need_fold":      true,
		"need_partition": true,
		"fold_config": map[string]interface{}{
			"beam_size":   50,
			"zuker_score": 3,
		},
		"partition_config": map[string]interface{}{
			"beam_size": 80,
		},
	})

	// 测试用例3：使用文件URL的请求
	input3, _ := json.Marshal(map[string]interface{}{
		"name":              "test_file_url",
		"sequence_file_url": "bos:/bml-test-test/helix_upload/fb5d8890-d7b0-41e0-ada1-da73270dba9b.txt",
		"need_fold":         true,
		"fold_config": map[string]interface{}{
			"beam_size": 100,
		},
	})

	// 测试用例4：无效请求 - 缺少sequence和sequence_file_url
	input4, _ := json.Marshal(map[string]interface{}{
		"name":      "test_invalid",
		"need_fold": true,
		"fold_config": map[string]interface{}{
			"beam_size": 100,
		},
	})

	// 测试用例5：无效请求 - need_fold和need_partition都为false
	input5, _ := json.Marshal(map[string]interface{}{
		"name":           "test_invalid_flags",
		"sequence":       "AUGCGGC",
		"need_fold":      false,
		"need_partition": false,
	})

	caseList := []struct {
		input  io.Reader
		output int
		desc   string
	}{
		{nil, helpers.ParamErrorCode, "empty body"},
		{bytes.NewReader(input1), helpers.AccountErrorCode, "fold only - should fail due to balance check"},
		{bytes.NewReader(input2), helpers.AccountErrorCode, "fold and partition - should fail due to balance check"},
		{bytes.NewReader(input3), helpers.AccountErrorCode, "file url - should fail due to balance check"},
		{bytes.NewReader(input4), helpers.ParamErrorCode, "missing sequence"},
		{bytes.NewReader(input5), helpers.ParamErrorCode, "invalid flags"},
	}

	for i, v := range caseList {
		raw := httptest.NewRequest("POST", "/", v.input)
		req := ghttp.NewRequest(time.Now(), raw)

		resp := SubmitLinearFold(ctx, req).(*ghttp.JSONResponse)
		var data helpers.JsonResp
		switch respData := resp.Data.(type) {
		case helpers.JsonResp:
			data = respData
		case *helpers.JsonResp:
			data = *respData
		case **helpers.JsonResp:
			data = **respData
		default:
			t.Errorf("Test case %d (%s): unexpected response type: %T", i, v.desc, resp.Data)
			continue
		}
		if data.Code != v.output {
			fmt.Printf("Test case %d (%s): code=%d, want=%d, data=%+v\n", i, v.desc, data.Code, v.output, data)
			t.Errorf("Test case %d (%s): code=%d, want=%d", i, v.desc, data.Code, v.output)
		}
	}
}

// TestSubmitLinearPartition 测试SubmitLinearPartition函数，该函数用于提交线性分区任务。
// 参数t *testing.T：表示当前正在执行的单元测试。
// 返回值没有返回值
func TestSubmitLinearPartition(t *testing.T) {
	ctx := context.Background()
	ctx = context.WithValue(ctx, "user_info", models.UserInfo{UserID: 1, Type: 1})

	// 测试用例1：标准的LinearPartition请求
	input1, _ := json.Marshal(map[string]interface{}{
		"name":           "test_partition",
		"sequence":       "AUGCGGCUACGCGCGCGCGCGCGC",
		"need_fold":      false,
		"need_partition": true,
		"partition_config": map[string]interface{}{
			"beam_size": 100,
		},
	})

	// 测试用例2：使用文件URL的请求
	input2, _ := json.Marshal(map[string]interface{}{
		"name":              "test_partition_file",
		"sequence_file_url": "bos:/bml-test-test/helix_upload/fb5d8890-d7b0-41e0-ada1-da73270dba9b.txt",
		"need_partition":    true,
		"partition_config": map[string]interface{}{
			"beam_size": 80,
		},
	})

	// 测试用例3：无效请求 - 缺少sequence和sequence_file_url
	input3, _ := json.Marshal(map[string]interface{}{
		"name":           "test_invalid",
		"need_partition": true,
		"partition_config": map[string]interface{}{
			"beam_size": 100,
		},
	})

	// 测试用例4：无效请求 - need_fold为true（LinearPartition不允许）
	input4, _ := json.Marshal(map[string]interface{}{
		"name":           "test_invalid_fold",
		"sequence":       "AUGCGGC",
		"need_fold":      true,
		"need_partition": true,
		"partition_config": map[string]interface{}{
			"beam_size": 100,
		},
	})

	// 测试用例5：无效请求 - 缺少partition_config
	input5, _ := json.Marshal(map[string]interface{}{
		"name":           "test_missing_config",
		"sequence":       "AUGCGGC",
		"need_partition": true,
	})

	caseList := []struct {
		input  io.Reader
		output int
		desc   string
	}{
		{nil, helpers.ParamErrorCode, "empty body"},
		{bytes.NewReader(input1), helpers.AccountErrorCode, "standard partition - should fail due to balance check"},
		{bytes.NewReader(input2), helpers.AccountErrorCode, "file url - should fail due to balance check"},
		{bytes.NewReader(input3), helpers.ParamErrorCode, "missing sequence"},
		{bytes.NewReader(input4), helpers.ParamErrorCode, "invalid need_fold=true"},
		{bytes.NewReader(input5), helpers.ParamErrorCode, "missing partition_config"},
	}

	for i, v := range caseList {
		raw := httptest.NewRequest("POST", "/", v.input)
		req := ghttp.NewRequest(time.Now(), raw)

		resp := SubmitLinearPartition(ctx, req).(*ghttp.JSONResponse)
		var data helpers.JsonResp
		switch respData := resp.Data.(type) {
		case helpers.JsonResp:
			data = respData
		case *helpers.JsonResp:
			data = *respData
		case **helpers.JsonResp:
			data = **respData
		default:
			t.Errorf("Test case %d (%s): unexpected response type: %T", i, v.desc, resp.Data)
			continue
		}
		if data.Code != v.output {
			fmt.Printf("Test case %d (%s): code=%d, want=%d, data=%+v\n", i, v.desc, data.Code, v.output, data)
			t.Errorf("Test case %d (%s): code=%d, want=%d", i, v.desc, data.Code, v.output)
		}
	}
}

// TestSubmitRNAForecast 测试SubmitRNAForecast函数，该函数用于提交RNA预测任务。
// 注释掉此测试，因为SubmitRNAForecast函数不存在
/*
func TestSubmitRNAForecast(t *testing.T) {
	ctx := context.Background()
	ctx1 := context.WithValue(ctx, "user_info", models.UserInfo{UserID: 1, Type: 1})
	ctx2 := context.WithValue(ctx, "user_info", models.UserInfo{UserID: 1, Type: 10})
	ctx3 := context.WithValue(ctx, "user_info", models.UserInfo{UserID: 1, Type: 20})
	ctx4 := context.WithValue(ctx, "user_info", models.UserInfo{UserID: 1, Type: 30})

	input2, _ := json.Marshal(map[string]interface{}{
		"serial":  "serialxxx",
		"version": "plus",
		"config": map[string]interface{}{
			"beam_size":   12,
			"zuker_score": 12,
			"constraint":  false,
		},
	})
	input3, _ := json.Marshal(map[string]interface{}{
		"file_url": "bos:/bml-test-test/helix_upload/fb5d8890-d7b0-41e0-ada1-da73270dba9b.txt",
		"name":     "test002",
		"version":  "advance",
	})
	input4, _ := json.Marshal(map[string]interface{}{
		"file_url": "xxx",
		"name":     "test002001",
	})

	caseList := []struct {
		ctx    context.Context
		input  io.Reader
		output int
	}{
		{ctx1, nil, helpers.ParamErrorCode},
		{ctx2, bytes.NewReader(input2), helpers.ParamErrorCode},
		{ctx3, bytes.NewReader(input3), helpers.ParamErrorCode},
		{ctx4, bytes.NewReader(input4), helpers.ParamErrorCode},
	}

	for _, v := range caseList {
		raw := httptest.NewRequest("POST", "/", v.input)
		req := ghttp.NewRequest(time.Now(), raw)

		resp := SubmitRNAForecast(v.ctx, req).(*ghttp.JSONResponse)
		data := resp.Data.(helpers.JsonResp)
		if data.Code != v.output {
			fmt.Println(data)
			t.Errorf("code=%d, want=%d", data.Code, v.output)
		}
	}
}
*/

// TestLinearFoldParamValidation 测试LinearFold参数校验功能
func TestLinearFoldParamValidation(t *testing.T) {
	ctx := context.Background()

	// 测试用例1：有效的LinearFold参数
	validParam := SubmitLinearFoldParam{
		Name:     "test_valid",
		Sequence: "AUGCGGCUACGCGCGCGCGCGCGC",
		NeedFold: true,
		FoldConfig: &models.LinearFoldConfig{
			BeamSize:   100,
			ZukerScore: 5,
		},
	}

	// 测试用例2：无效的序列字符
	invalidSeqParam := SubmitLinearFoldParam{
		Name:     "test_invalid_seq",
		Sequence: "AUGCGGCXYZ", // 包含无效字符
		NeedFold: true,
		FoldConfig: &models.LinearFoldConfig{
			BeamSize: 100,
		},
	}

	// 测试用例3：beam_size超出范围
	invalidBeamParam := SubmitLinearFoldParam{
		Name:     "test_invalid_beam",
		Sequence: "AUGCGGC",
		NeedFold: true,
		FoldConfig: &models.LinearFoldConfig{
			BeamSize: 300, // 超出MaxBeamSizeLimit
		},
	}

	testCases := []struct {
		param    SubmitLinearFoldParam
		hasError bool
		desc     string
	}{
		{validParam, false, "valid parameters"},
		{invalidSeqParam, true, "invalid sequence characters"},
		{invalidBeamParam, true, "beam_size out of range"},
	}

	for i, tc := range testCases {
		err := checkLinearFoldParam(ctx, &tc.param)
		if tc.hasError && err == nil {
			t.Errorf("Test case %d (%s): expected error but got none", i, tc.desc)
		}
		if !tc.hasError && err != nil {
			t.Errorf("Test case %d (%s): unexpected error: %v", i, tc.desc, err)
		}
	}
}

// TestLinearPartitionParamValidation 测试LinearPartition参数校验功能
func TestLinearPartitionParamValidation(t *testing.T) {
	ctx := context.Background()

	// 测试用例1：有效的LinearPartition参数
	validParam := SubmitLinearPartitionParam{
		Name:          "test_valid",
		Sequence:      "AUGCGGCUACGCGCGCGCGCGCGC",
		NeedFold:      false,
		NeedPartition: true,
		PartitionConfig: &models.LinearPartitionConfig{
			BeamSize: 100,
		},
	}

	// 测试用例2：need_fold为true（LinearPartition不允许）
	invalidFoldParam := SubmitLinearPartitionParam{
		Name:          "test_invalid_fold",
		Sequence:      "AUGCGGC",
		NeedFold:      true, // 不允许
		NeedPartition: true,
		PartitionConfig: &models.LinearPartitionConfig{
			BeamSize: 100,
		},
	}

	// 测试用例3：缺少partition_config
	missingConfigParam := SubmitLinearPartitionParam{
		Name:          "test_missing_config",
		Sequence:      "AUGCGGC",
		NeedFold:      false,
		NeedPartition: true,
		// PartitionConfig 为 nil
	}

	testCases := []struct {
		param    SubmitLinearPartitionParam
		hasError bool
		desc     string
	}{
		{validParam, false, "valid parameters"},
		{invalidFoldParam, true, "need_fold=true not allowed"},
		{missingConfigParam, true, "missing partition_config"},
	}

	for i, tc := range testCases {
		err := checkLinearPartitionParam(ctx, &tc.param)
		if tc.hasError && err == nil {
			t.Errorf("Test case %d (%s): expected error but got none", i, tc.desc)
		}
		if !tc.hasError && err != nil {
			t.Errorf("Test case %d (%s): unexpected error: %v", i, tc.desc, err)
		}
	}
}

// TestLinearFoldJSONParsing 测试LinearFold的JSON解析功能
func TestLinearFoldJSONParsing(t *testing.T) {
	// 测试JSON解析
	jsonStr := `{
		"name": "test_fold",
		"sequence": "AUGCGGCUACGCGCGCGCGCGCGC",
		"need_fold": true,
		"need_partition": false,
		"fold_config": {
			"beam_size": 100,
			"zuker_score": 5,
			"constraint": false
		}
	}`

	var param SubmitLinearFoldParam
	err := json.Unmarshal([]byte(jsonStr), &param)
	if err != nil {
		t.Errorf("JSON parsing failed: %v", err)
		return
	}

	// 验证解析结果
	if param.Name != "test_fold" {
		t.Errorf("Expected name 'test_fold', got '%s'", param.Name)
	}
	if param.Sequence != "AUGCGGCUACGCGCGCGCGCGCGC" {
		t.Errorf("Expected sequence 'AUGCGGCUACGCGCGCGCGCGCGC', got '%s'", param.Sequence)
	}
	if !param.NeedFold {
		t.Errorf("Expected need_fold to be true")
	}
	if param.NeedPartition {
		t.Errorf("Expected need_partition to be false")
	}
	if param.FoldConfig == nil {
		t.Errorf("Expected fold_config to be non-nil")
		return
	}
	if param.FoldConfig.BeamSize != 100 {
		t.Errorf("Expected beam_size 100, got %d", param.FoldConfig.BeamSize)
	}
}
