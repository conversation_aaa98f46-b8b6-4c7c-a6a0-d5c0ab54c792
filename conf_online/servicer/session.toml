Name = "session"

# 连接超时，
ConnTimeOut = 500
# 写数据超时
WriteTimeOut = 1000
# 读数据超时
ReadTimeOut = 1000
# 请求失败后的重试次数：总请求次数 = Retry + 1
Retry = 2

# 资源使用策略，非必选，默认使用 RoundRobin
# RoundRobin: 依次轮询
[Strategy]
Name="RoundRobin"

# passport - session2
# 线上环境 bns: group.ess-session.passport.cn
# 资源定位：BNS 配置，支持智能 BNS
[Resource.BNS]
BNSName = "group.smartbns-from_product=ess-session%group.flow-ssngate.passport.all"
EnableSmartBNS = false
UsePort = "main"

# passport qa测试环境
# 资源定位：手动配置 - 使用IP、端口
#[Resource.Manual]
#[[Resource.Manual.default]]
#Host = "***********"
#Port = 9081
