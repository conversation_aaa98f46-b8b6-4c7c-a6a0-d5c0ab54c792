module icode.baidu.com/helix_web

go 1.21

require (
	github.com/afocus/captcha v0.0.0-20191010092841-4bd1f21c8868
	github.com/baidubce/bce-sdk-go v0.9.52
	github.com/go-redis/redis/v8 v8.6.0
	github.com/google/uuid v1.6.0
	github.com/jordan-wright/email v4.0.0+incompatible
	github.com/streadway/amqp v1.0.0
	github.com/stretchr/testify v1.8.4
	google.golang.org/grpc v1.66.2
	google.golang.org/protobuf v1.34.1
	gorm.io/gorm v1.25.1
	icode.baidu.com/baidu/bce-iam/sdk-go v1.2.13
	icode.baidu.com/baidu/gdp/automaxprocs v0.1.************
	icode.baidu.com/baidu/gdp/codec v1.20.0
	icode.baidu.com/baidu/gdp/conf v1.20.0
	icode.baidu.com/baidu/gdp/env v1.20.0
	icode.baidu.com/baidu/gdp/ghttp v1.20.11
	icode.baidu.com/baidu/gdp/gorm_adapter v1.20.1
	icode.baidu.com/baidu/gdp/logit v1.20.11
	icode.baidu.com/baidu/gdp/mysql v1.20.6
	icode.baidu.com/baidu/gdp/net v1.20.10
	icode.baidu.com/baidu/gdp/nshead v1.20.7
	icode.baidu.com/baidu/gdp/passport v1.20.0
	icode.baidu.com/baidu/gdp/redis v1.20.8
	mosn.io/holmes v1.1.0
	mosn.io/pkg v0.0.0-20211217101631-d914102d1baf
)

require (
	github.com/BurntSushi/toml v0.3.1 // indirect
	github.com/DATA-DOG/go-sqlmock v1.5.0 // indirect
	github.com/StackExchange/wmi v0.0.0-20190523213315-cbe66965904d // indirect
	github.com/beorn7/perks v1.0.1 // indirect
	github.com/cespare/xxhash/v2 v2.3.0 // indirect
	github.com/davecgh/go-spew v1.1.1 // indirect
	github.com/dgryski/go-rendezvous v0.0.0-20200823014737-9f7001d12a5f // indirect
	github.com/didi/gendry v1.5.0 // indirect
	github.com/fsnotify/fsnotify v1.6.0 // indirect
	github.com/go-ole/go-ole v1.2.4 // indirect
	github.com/go-sql-driver/mysql v1.7.0 // indirect
	github.com/golang/freetype v0.0.0-20170609003504-e2365dfdc4a0 // indirect
	github.com/golang/protobuf v1.5.3 // indirect
	github.com/hashicorp/go-syslog v1.0.0 // indirect
	github.com/hashicorp/golang-lru/v2 v2.0.7 // indirect
	github.com/jinzhu/inflection v1.0.0 // indirect
	github.com/jinzhu/now v1.1.5 // indirect
	github.com/kr/pretty v0.3.1 // indirect
	github.com/matttproud/golang_protobuf_extensions v1.0.1 // indirect
	github.com/onsi/ginkgo v1.16.5 // indirect
	github.com/onsi/gomega v1.25.0 // indirect
	github.com/pmezard/go-difflib v1.0.0 // indirect
	github.com/prometheus/client_golang v1.11.1 // indirect
	github.com/prometheus/client_model v0.3.0 // indirect
	github.com/prometheus/common v0.26.0 // indirect
	github.com/prometheus/procfs v0.6.0 // indirect
	github.com/rcrowley/go-metrics v0.0.0-20200313005456-10cdbea86bc0 // indirect
	github.com/shirou/gopsutil v3.20.11+incompatible // indirect
	go.opentelemetry.io/otel v0.17.0 // indirect
	go.opentelemetry.io/otel/metric v0.17.0 // indirect
	go.opentelemetry.io/otel/trace v0.17.0 // indirect
	golang.org/x/image v0.0.0-20220302094943-723b81ca9867 // indirect
	golang.org/x/net v0.26.0 // indirect
	golang.org/x/sys v0.21.0 // indirect
	golang.org/x/text v0.16.0 // indirect
	golang.org/x/time v0.3.0 // indirect
	google.golang.org/genproto v0.0.0-20200806141610-86f49bd18e98 // indirect
	gopkg.in/natefinch/lumberjack.v2 v2.2.1 // indirect
	gopkg.in/yaml.v3 v3.0.1 // indirect
	gorm.io/driver/mysql v1.5.1 // indirect
	icode.baidu.com/baidu/gdp/bns v1.20.3 // indirect
	icode.baidu.com/baidu/gdp/exjson v0.0.0-20190802111509-ad9978e27629 // indirect
	icode.baidu.com/baidu/gdp/extension v1.20.7 // indirect
	icode.baidu.com/baidu/gdp/mcpack v1.0.0 // indirect
	mosn.io/api v0.0.0-20210204052134-5b9a826795fd // indirect
)
