import json
import sys
import os
import traceback
import argparse
# from infer_scripts import preprocess
# from infer_scripts.tools.utils import digit2alphabet
from templates_input import get_refer_structure_features

"""
input ref_structure_check check.
author: gaojie<PERSON>
"""


def ref_structure_check_single(input_json):
    msg = ''
    input = json.loads(input_json)
    try:
        feature_structure = get_refer_structure_features(input.get('cif_path'),
            input.get('refer_chain_id'), input.get('sequence'),
            input.get('hit_pdb_code'), input.get('kalign_binary_path'))
        print('succ')
        exit(0)
    except Exception as e:
        print(f'Sequence mismatch in reference structure. Ensure reference and target sequences are at least 90% similar and at least 6 residues long.')
        exit(1)

if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    parser.add_argument("--input_json", type=str, required=True)
    args = parser.parse_args()
    ref_structure_check_single(args.input_json)

