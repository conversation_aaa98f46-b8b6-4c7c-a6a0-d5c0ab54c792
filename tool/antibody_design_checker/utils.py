from Bio.PDB.MMCIF2Dict import MMCIF2Dict
from collections import defaultdict
import re 

# Mapping from three-letter residue codes to one-letter codes
three_to_one = {
    "ALA": "A", "ARG": "R", "ASN": "N", "ASP": "D", "CYS": "C",
    "GLU": "E", "GLN": "Q", "GLY": "G", "HIS": "H", "ILE": "I",
    "LEU": "L", "LYS": "K", "MET": "M", "PHE": "F", "PRO": "P",
    "SER": "S", "THR": "T", "TRP": "W", "TYR": "Y", "VAL": "V",
    "SEC": "U", "PYL": "O", "ASX": "B", "GLX": "Z", "UNK": "X"
}

def extract_seq_from_cif_coords(cif_file):
    """
    从 mmCIF 文件中提取真正出现在 _atom_site 坐标表中的氨基酸序列。
    只统计存在坐标的残基，不依赖 _entity_poly 的 header。
    返回:
        - seqs: dict of chain_id -> 1-letter sequence
        - seqs_idx: dict of chain_id -> list of residue number strings
    """
    mmcif = MMCIF2Dict(cif_file)
    
    # 解析字段
    resnames   = mmcif["_atom_site.label_comp_id"]
    chain_ids  = mmcif["_atom_site.auth_asym_id"]
    resseqs    = mmcif["_atom_site.auth_seq_id"]
    icode_list = mmcif.get("_atom_site.pdbx_PDB_ins_code", ['?'] * len(resnames))  # 插入码
    
    seqs = defaultdict(str)
    seqs_idx = defaultdict(list)
    seen = set()  # 记录已处理过的 (chain, resseq, icode)
    
    for resname, chain_id, resseq, icode in zip(resnames, chain_ids, resseqs, icode_list):
        key = (chain_id, resseq, icode)
        if key in seen:
            continue
        seen.add(key)
        
        aa = three_to_one.get(resname.upper())
        if aa:
            seqs[chain_id] += aa
            seqs_idx[chain_id].append(resseq + (icode if icode not in ('.', '?') else ''))
        else:
            # 非标准残基如 HEM、HOH、其他 ligand
            continue

    return seqs, seqs_idx

def filter_non_std_res(seq):
    """Filter out non-standard residues, replacing with 'X'."""
    non_std_res = re.findall("\(.+\)", seq)
    for ns in non_std_res:
        seq = seq.replace(ns, "")

    return ''.join([aa if aa.isalpha() and aa.isupper() else 'X' for aa in seq])

# def filter_non_std_res(seq):
#     """ ."""
#     non_std_res = re.findall("\(.+\)", seq)
#     for ns in non_std_res:
#         seq = seq.replace(ns, "")
    
#     return seq

def get_entity_sequences_from_cif_dict(mmcif_dict):
    """
    Get a list of entity-level sequences from an mmCIF dictionary.
    If _entity_poly.pdbx_seq_one_letter_code exists, use it.
    Otherwise, reconstruct sequences from _entity_poly_seq.mon_id.
    The order matches _entity_poly.entity_id.
    """
    # Try to get one-letter code sequences
    entity_sequences = mmcif_dict.get("_entity_poly.pdbx_seq_one_letter_code")
    if isinstance(entity_sequences, str):
        entity_sequences = [entity_sequences]
    if entity_sequences:
        return [filter_non_std_res(seq.replace("\n", "")) for seq in entity_sequences]

    # Fallback: reconstruct sequences from mon_id
    mon_ids = mmcif_dict.get("_entity_poly_seq.mon_id", [])
    entity_ids = mmcif_dict.get("_entity_poly_seq.entity_id", [])

    if isinstance(mon_ids, str): mon_ids = [mon_ids]
    if isinstance(entity_ids, str): entity_ids = [entity_ids]

    # Build mapping: entity_id → list of residues
    seq_map = defaultdict(list)
    for eid, mon in zip(entity_ids, mon_ids):
        seq_map[eid].append(three_to_one.get(mon, "X"))

    # Ensure entity ID order matches strand mapping
    entity_poly_ids = mmcif_dict.get("_entity_poly.entity_id")
    if isinstance(entity_poly_ids, str):
        entity_poly_ids = [entity_poly_ids]

    return ["".join(seq_map[eid]) for eid in entity_poly_ids]

def extract_seqres_from_CIF(cif_file):
    """
    Extracts chain-level sequences from a mmCIF file.
    Uses get_entity_sequences_from_cif_dict() for robust sequence extraction.
    Returns a dictionary: {chain_id → sequence}
    """
    mmcif_dict = MMCIF2Dict(cif_file)

    # Get entity-level sequences
    entity_sequences = get_entity_sequences_from_cif_dict(mmcif_dict)

    # Get chain IDs (pdbx_strand_id), mapped to entity IDs
    auth_ids = mmcif_dict.get("_entity_poly.pdbx_strand_id", [])
    entity_ids = mmcif_dict.get("_entity_poly.entity_id", [])
    
    if isinstance(auth_ids, str): auth_ids = [auth_ids]
    if isinstance(entity_ids, str): entity_ids = [entity_ids]

    # Map each chain ID (can be comma-separated) to its entity sequence
    chain_seq_dict = {}
    for chain_group, seq in zip(auth_ids, entity_sequences):
        chains = chain_group.split(',')
        for chain in chains:
            chain_seq_dict[chain] = seq

    return chain_seq_dict


def main():
    import argparse

    parser = argparse.ArgumentParser(description="Extract chain-level sequences from mmCIF file.")
    parser.add_argument("cif_file", help="Path to the mmCIF file")
    args = parser.parse_args()

    result = extract_seqres_from_CIF(args.cif_file)

    for chain, seq in result.items():
        print(f">Chain {chain}\n{seq}\n")

if __name__ == "__main__":
    main()