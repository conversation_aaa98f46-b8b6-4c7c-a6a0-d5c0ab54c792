package bootstrap

import (
	"context"
	"time"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/helix_web/helpers"
	"mosn.io/holmes"
	mlog "mosn.io/pkg/log"
)

type ReporterImpl struct{}
func (r *ReporterImpl) Report(pType string, buf []byte, reason string, eventID string) error {
	helpers.HelixNotice(context.Background(), "----monitor err---" +reason)
	return nil
}

func Profiling(ctx context.Context) {
	newCtx := logit.NewContext(ctx)
	defer helpers.DeferFunc(newCtx)()

	r := &ReporterImpl{}
	for {
		h, _ := holmes.New(
			holmes.WithProfileReporter(r),
			holmes.WithCollectInterval("5s"), // 指标采集时间间隔
			holmes.WithDumpPath("/tmp"),      // profile保存路径
			holmes.WithTextDump(),
			holmes.WithLogger(holmes.NewFileLog("/tmp/holmes.log", mlog.INFO)),

			holmes.WithCPUDump(10, 30, 80, 2*time.Minute),                     // 配置CPU的性能监控规则
			holmes.WithMemDump(30, 30, 60, 2*time.Minute),                     // 配置Heap Memory 性能监控规则
			holmes.WithGCHeapDump(10, 20, 40, 2*time.Minute),                  // 配置基于GC周期的Heap Memory 性能监控规则
			holmes.WithGoroutineDump(500, 30, 20000, 100*1000, 2*time.Minute), //配置Goroutine数量的监控规则
		)

		// enable all
		h.EnableCPUDump().
			EnableGoroutineDump().
			EnableMemDump().
			EnableGCHeapDump().Start()

		// 监控 10 分钟后关闭
		time.Sleep(time.Second * 600)
		h.Stop()
		time.Sleep(time.Second * 300)
	}
}
