package bootstrap

import (
	"context"
	"errors"
	"fmt"
	"io"
	"log"
	"net"
	"os"
	"os/signal"
	"path/filepath"
	"runtime"
	"syscall"
	"time"

	"icode.baidu.com/baidu/gdp/conf"
	"icode.baidu.com/baidu/gdp/env"
	"icode.baidu.com/baidu/gdp/ghttp"
	"icode.baidu.com/helix_web/httpapi"
)

// Config app的配置
// 默认对应 conf/app.toml
type Config struct {
	AppName string
	IDC     string
	RunMode string

	ShutdownTimeout int // shutdown 超时时间

	Env env.AppEnv

	// http 服务的配置
	HTTPServer struct {
		Listen       string
		ReadTimeout  int // 单位 ms
		WriteTimeout int // 单位 ms
		IdleTimeout  int
	}
}

// Check 检查配置是否正确
func (c *Config) Check() error {
	if c.AppName == "" {
		return errors.New("config.AppName is empty")
	}
	if c.IDC == "" {
		return errors.New("config.IDC is empty")
	}
	if c.RunMode == "" {
		return errors.New("config.RunMode is empty")
	}
	// 自定义
	return nil
}

// ParserAppConfig 解析应用配置
func ParserAppConfig(filePath string) (*Config, error) {
	confPath, err := filepath.Abs(filePath)
	if err != nil {
		return nil, err
	}

	var c *Config
	if err := conf.Parse(confPath, &c); err != nil {
		return nil, err
	}
	if err := c.Check(); err != nil {
		return nil, err
	}

	// 解析并设置全局应用环境信息
	rootDir := filepath.Dir(filepath.Dir(confPath))
	opt := env.Option{
		AppName: c.AppName,
		IDC:     c.IDC,
		RunMode: c.RunMode,
		RootDir: rootDir,
		DataDir: filepath.Join(rootDir, "data"),
		LogDir:  filepath.Join(rootDir, "log"),
		ConfDir: filepath.Join(rootDir, filepath.Base(filepath.Dir(confPath))),
	}
	c.Env = env.New(opt)
	return c, nil
}

// NewApp 创建应用
// 这里只是一个示例，只会启动一个http server
func NewApp(ctx context.Context, c *Config) *App {
	ctx1, cancel := context.WithCancel(ctx)
	app := &App{
		ctx:    ctx1,
		config: c,
		close:  cancel,
	}
	app.initHTTPServer()
	return app
}

// App 应用
type App struct {
	ctx      context.Context
	listener net.Listener
	server   ghttp.Server
	config   *Config
	close    func()
}

// Start 启动服务
func (app *App) Start() error {
	l, err := net.Listen("tcp", app.config.HTTPServer.Listen)
	log.Println("app.Start Listen ", app.config.HTTPServer.Listen, err)
	if err != nil {
		return err
	}
	app.listener = l
	return app.server.Serve(app.listener)
}

func (app *App) initHTTPServer() {
	ser := &ghttp.DefaultServer{
		Ctx:          app.ctx,
		ReadTimeout:  time.Millisecond * time.Duration(app.config.HTTPServer.ReadTimeout),
		WriteTimeout: time.Millisecond * time.Duration(app.config.HTTPServer.WriteTimeout),
		IdleTimeout:  time.Millisecond * time.Duration(app.config.HTTPServer.IdleTimeout),
		MaxBodyBytes: 0,
		Logger:       nil,
	}

	ser.SetHandler(httpapi.Router(ser))
	app.server = ser
}

// Shutdown 关闭服务
func (app *App) Shutdown(ctx context.Context) {
	if app.listener != nil {
		app.listener.Close()
	}

	defer app.close()

	_ = app.server.Shutdown(ctx)

	for _, fn := range closeFns {
		_ = fn()
	}
}

// StartWithGracefulShutdown 启动服务 并且支持优雅shutdown
func (app *App) StartWithGracefulShutdown() int {
	ch := make(chan os.Signal, 1)
	go func() {
		signal.Notify(ch, syscall.SIGINT, syscall.SIGTERM, syscall.SIGQUIT)
	}()

	done := make(chan bool)
	go func() {
		// 捕获 app 启动中是否出错
		defer func() {
			re := recover()
			if re != nil {
				trace := make([]byte, 8192)
				runtime.Stack(trace[:], false)
				fmt.Fprintf(os.Stderr, "%s server panic:%v\n%s\n", time.Now(), re, trace)
			}
			done <- true
		}()

		// 协程到这里等待了 (app.start() 等待了...)
		// 注意：
		// 进程被 kill 后出现如下输出是正常的：
		// server exit:accept tcp xxx: use of closed network connection (select 走第二个case)
		err := app.Start()
		fmt.Fprintf(os.Stderr, "%s server exit:%v\n", time.Now(), err)
	}()

	exitCode := 0

	timeout := time.Duration(app.config.ShutdownTimeout) * time.Millisecond
	if timeout == 0 {
		timeout = 5 * time.Second
	}

	select {
	case <-done:
		exitCode = 1
		app.Shutdown(app.ctx)
	case sig := <-ch:
		fmt.Fprintf(os.Stderr, "%s receive signal %v\n", time.Now(), sig)
		exitCode = 2
		ctxStop, cancel := context.WithTimeout(context.Background(), timeout)
		app.Shutdown(ctxStop)
		cancel()
		fmt.Fprintf(os.Stderr, "%s exit by signal %v\n", time.Now(), sig)
	}
	return exitCode
}

// 实现 ghttp.server 的接口
type server interface {
	// 开始并监听服务
	Serve(l net.Listener) error
	Shutdown(ctx context.Context) error
}

var closeFns []func() error

// TryRegisterCloser 注册 close 方法
//
// 	若有资源需要在程序退出前进行清理，请使用该方法注册
// 	可以注册实现了 io.Closer 的对象
// 	或者是直接注册close方法，类型是 func()error
func TryRegisterCloser(comp interface{}) {
	if c, ok := comp.(io.Closer); ok {
		closeFns = append(closeFns, c.Close)
		return
	}
	if fn, ok := comp.(func() error); ok {
		closeFns = append(closeFns, fn)
	}
}
