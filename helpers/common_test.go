package helpers

import (
	"context"
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
	"icode.baidu.com/baidu/gdp/env"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/helix_web/library/resource"
)

// initLoggers 初始化logger
func init() {
	ctx := context.Background()
	env.DefaultRunMode = "debug"

	webLogger, err := logit.NewLogger(ctx, logit.OptConfigFile("logit/service.toml"))
	if err != nil {
		panic(err.Error())
	}
	resource.LoggerService = webLogger
}

func TestLogError(t *testing.T) {
	ctx := context.Background()
	LogError(ctx, errors.New("ni hao test"))
}

func TestDealBosFileUrl(t *testing.T)  {
	tests := []struct {
		input string
		output1 string
		output2 string
	}{
		{"bos:/bml/test", "bml", "test"},
		{"bos:/bml/", "bml", ""},
		{"bml", "", ""},
	}

	for _, test := range tests {
		got1, got2 := DealBosFileUrl(test.input)
		assert.Equal(t, test.output1, got1)
		assert.Equal(t, test.output2, got2)
	}
}

func TestGenerateCaptcha(t *testing.T)  {
	tests := []struct {
		input int
	}{
		{5},
		{6},
	}

	for _, test := range tests {
		_, got, _ := GenerateCaptcha(test.input)
		assert.Equal(t, test.input, len(got))
	}
}

func TestGetRandomString(t *testing.T) {
	tests := []struct {
		input int
	}{
		{5},
		{6},
	}

	for _, test := range tests {
		got := GetRandomString(test.input)
		assert.Equal(t, test.input, len(got))
	}
}

func TestGetNumRandomStr(t *testing.T) {
	tests := []struct {
		input int
	}{
		{5},
		{6},
	}

	for _, test := range tests {
		got := GetNumRandomStr(test.input)
		assert.Equal(t, test.input, len(got))
	}
}
