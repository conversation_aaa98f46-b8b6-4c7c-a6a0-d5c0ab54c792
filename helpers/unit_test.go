package helpers

import (
	"reflect"
	"testing"
)

func TestStrSliceToInt64(t *testing.T) {
	tests := []struct {
		input []string
		output []int64
	}{
		{[]string{"1","2","3"}, []int64{int64(1), int64(2), int64(3)}},
		{[]string{"","23.09","12"}, []int64{int64(0), int64(0), int64(12)}},
		{[]string{"dd","ss","12"}, []int64{int64(0), int64(0), int64(12)}},
	}

	for _, test := range tests {
		if got := StrSliceToInt64(test.input); !reflect.DeepEqual(got, test.output) {
			t.<PERSON>("output(%v)", got)
		}
	}
}

func TestFormatFloat(t *testing.T) {
	tests := []struct {
		input float64
		output float64
	}{
		{666.3232342, 666.3232},
		{666.32, 666.3200},
		{666.555555, 666.5556},
	}

	for _, test := range tests {
		if got := FormatFloat(test.input, 4); got != test.output {
			t.<PERSON>rrorf("output(%v)", got)
		}
	}
}

func TestAssertString(t *testing.T) {
	tests := []struct {
		input interface{}
		output string
	}{
		{121312, ""},
		{"ddd", "ddd"},
		{[]int{1,2,3}, ""},
	}

	for _, test := range tests {
		if got := AssertString(test.input); got != test.output {
			t.Errorf("output(%v)", got)
		}
	}
}

func TestAssertFloat64(t *testing.T) {
	tests := []struct {
		input interface{}
		output float64
	}{
		{121312, 0},
		{float64(12.12), float64(12.12)},
		{[]int{1,2,3}, 0},
	}

	for _, test := range tests {
		if got := AssertFloat64(test.input); got != test.output {
			t.Errorf("output(%v)", got)
		}
	}
}

func TestCheckInSliceByInt64(t *testing.T) {
	tests := []struct {
		input1 int64
		input2 []int64
		output bool
	}{
		{12, []int64{12, 13, 15}, true},
		{10, []int64{12, 13, 15}, false},
	}

	for _, test := range tests {
		if got := CheckInSliceByInt64(test.input1, test.input2); got != test.output {
			t.Errorf("output(%v)", got)
		}
	}
}

func TestCheckInSliceByString(t *testing.T) {
	tests := []struct {
		input1 string
		input2 []string
		output bool
	}{
		{"11", []string{"11", "ss", "dd"}, true},
		{"ee", []string{"11", "ss", "dd"}, false},
	}

	for _, test := range tests {
		if got := CheckInSliceByString(test.input1, test.input2); got != test.output {
			t.Errorf("output(%v)", got)
		}
	}
}

func TestStrToInt(t *testing.T) {
	tests := []struct {
		input string
		output int
	}{
		{"11", 11},
		{"ee", 0},
	}

	for _, test := range tests {
		if got := StrToInt(test.input); got != test.output {
			t.Errorf("output(%v)", got)
		}
	}
}
