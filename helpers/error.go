package helpers

import (
	"fmt"
	"strings"
)

const (
	ErrorCode = 1000
	ErrorMsg  = "非常抱歉，系统出现错误，请稍后重试"

	AccountErr   = "ACCOUNT_ERR"
	ParamErr     = "PARAM_ERR"
	DBErr        = "DB_ERR"
	RateLimitErr = "RATE_LIMIT_ERR"
	SubmitErr    = "SUBMIT_ERR"

	ErrorRequestTooFast  = "request too fast, please repeat"
	ErrorExceedFreeLimit = "limit times per-person"
)

type easyErr struct {
	Code int64
	Msg  string
}

func (e easyErr) Error() string {
	return fmt.Sprintf("code:%d,msg:%v", e.Code, e.Msg)
}

func NewError(errKey string, extraInfo ...any) error {
	// if len(extraInfo) > 1 {
	// return easyErr{Code: ErrorCode, Msg: ErrorMsg}
	// }
	errorInfo, err := GetTomlConf("/message.toml")
	if err != nil {
		return easyErr{Code: ErrorCode, Msg: ErrorMsg}
	}
	ok := true
	errKeyArr := Explode("/", errKey)
	if len(errKeyArr) == 0 {
		return easyErr{Code: ErrorCode, Msg: ErrorMsg}
	}
	for _, key := range errKeyArr {
		if errorInfo, ok = errorInfo[key].(map[string]any); !ok {
			return easyErr{Code: ErrorCode, Msg: errKey}
		}
	}
	if errorInfo["code"].(int64) <= 0 || len(errorInfo["msg"].(string)) <= 0 {
		return easyErr{Code: ErrorCode, Msg: errKey}
	}

	msg := errorInfo["msg"].(string)
	if len(extraInfo) > 0 {
		msg = fmt.Sprintf(msg, extraInfo...)
	}
	return easyErr{Code: errorInfo["code"].(int64), Msg: msg}
}

func GetCode(err error) int64 {
	if e, ok := err.(easyErr); ok {
		return e.Code
	}
	return ErrorCode
}

func GetMsg(err error) string {
	if e, ok := err.(easyErr); ok {
		return e.Msg
	}
	return ErrorMsg
}

func Explode(delimiter, text string) []string {
	if len(text) == 0 {
		return []string{}
	}
	if len(delimiter) > len(text) {
		return strings.Split(delimiter, text)
	}
	return strings.Split(text, delimiter)
}
