package helpers

import (
	"bytes"
	"encoding/json"
	"io"
	"io/ioutil"
	"reflect"
	"strings"
	"testing"
)

func TestGetBodyParams(t *testing.T) {
	var needOutput map[string]interface{}

	input1, _ := json.<PERSON>(map[string]interface{}{"name":"nick", "age":18})
	input2, _ := json.<PERSON>(map[string]interface{}{"name":"nick", "other": map[string]interface{}{"age":18, "sex":1}})
	tests := []struct {
		input io.ReadCloser
		output map[string]interface{}
	}{
		{ioutil.NopCloser(bytes.NewReader(input1)), map[string]interface{}{
			"name":  "nick",
			"age":   float64(18),
		}},
		{ioutil.NopCloser(bytes.NewReader(input2)), map[string]interface{}{
			"name": "nick",
			"other": map[string]interface{}{
				"age": float64(18),
				"sex": float64(1),
			},
		}},
		{nil, needOutput},
		{ioutil.NopCloser(strings.NewReader("")), needOutput},
	}

	for _, test := range tests {
		if got := GetBodyParams(test.input); !reflect.DeepEqual(got, test.output) {
			t.Errorf("want(%v), got(%v)", test.output, got)
		}
	}
}

func TestGetBoolParam(t *testing.T) {
	tests := []struct {
		input map[string]interface{}
		output bool
	}{
		{map[string]interface{}{"is_check": true}, true},
		{map[string]interface{}{"is_check": false}, false},
		{map[string]interface{}{"is_check": 1}, false},
	}

	for _, test := range tests {
		if got := GetBoolParam(test.input, "is_check", false); got != test.output {
			t.Errorf("want(%v), got(%v)", test.output, got)
		}
	}
}

func TestGetIntParam(t *testing.T) {
	tests := []struct {
		input map[string]interface{}
		output int64
	}{
		{map[string]interface{}{"age": true}, 0},
		{map[string]interface{}{"age": float64(12)}, int64(12)},
		{map[string]interface{}{"age": 1.98}, int64(1)},
	}

	for _, test := range tests {
		if got := GetIntParam(test.input, "age", 0); got != test.output {
			t.Errorf("want(%d), got(%d)", test.output, got)
		}
	}
}

func TestGetStringParam(t *testing.T) {
	tests := []struct {
		input map[string]interface{}
		output string
	}{
		{map[string]interface{}{"name": "li"}, "li"},
		{map[string]interface{}{"name": " li "}, "li"},
		{map[string]interface{}{"name": 33}, ""},
	}

	for _, test := range tests {
		if got := GetStringParam(test.input, "name"); got != test.output {
			t.Errorf("want(%v), got(%v)", test.output, got)
		}
	}

	for _, test := range tests {
		if got := GetHTMLStringParam(test.input, "name"); got != test.output {
			t.Errorf("want(%v), got(%v)", test.output, got)
		}
	}
}

func TestGetFloatParam(t *testing.T) {
	tests := []struct {
		input map[string]interface{}
		output float64
	}{
		{map[string]interface{}{"amount": true}, 0},
		{map[string]interface{}{"amount": float64(12)}, 12},
		{map[string]interface{}{"amount": 1.98}, 1.98},
	}

	for _, test := range tests {
		if got := GetFloatParam(test.input, "amount", 0); got != test.output {
			t.Errorf("want(%v), got(%v)", test.output, got)
		}
	}
}

func TestGetIntSliceParam(t *testing.T) {
	tests := []struct {
		input map[string]interface{}
		output []int64
	}{
		{map[string]interface{}{"ids": []int64{12, 14}}, []int64{12, 14}},
		{map[string]interface{}{"ids": []int64{12, 14, 15}}, []int64{12, 14, 15}},
	}

	for _, test := range tests {
		if got := GetIntSliceParam(test.input, "ids"); len(got) == len(test.output) {
			t.Errorf("want(%v), got(%v)", test.output, got)
		}
	}
}

func TestGetStringSliceParam(t *testing.T) {
	tests := []struct {
		input map[string]interface{}
		output []string
	}{
		{map[string]interface{}{"ids": []string{"ss", "ee"}}, []string{"ss", "ee"}},
		{map[string]interface{}{"ids": []string{"ss", "33"}}, []string{"ss", "33"}},
	}

	for _, test := range tests {
		if got := GetStringSliceParam(test.input, "ids"); len(got) == len(test.output) {
			t.Errorf("want(%v), got(%v)", test.output, got)
		}
	}
}
