package helpers

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestSendMail(t *testing.T) {
	tests := []struct {
		fromU   string
		toU     string
		subject string
		content string
	}{
		{FromUser, "<EMAIL>", "paddlepaddle", "this is a good news"},
		{FromUser, "<EMAIL>", "paddlepaddle-test", "this is a great news"},
	}

	for _, test := range tests {
		got := SendMail(test.fromU, test.toU, test.subject, test.content, "")
		assert.Equal(t, nil, got)
	}
}
