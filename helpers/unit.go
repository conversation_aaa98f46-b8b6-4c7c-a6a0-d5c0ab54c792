package helpers

import (
	"fmt"
	"strconv"
)

// 字符串切片转整型切片
func StrSliceToInt64(strSlice []string) []int64 {
	var intSlice []int64
	for _, str := range strSlice {
		i, err := strconv.ParseInt(str, 10, 64)
		if err != nil {
			i = int64(0)
		}
		intSlice = append(intSlice, i)
	}

	return intSlice
}

// 格式化 float 64 保留小数
func FormatFloat(value float64, decimal int) float64 {
	val, _ := strconv.ParseFloat(fmt.Sprintf("%."+strconv.Itoa(decimal)+"f", value), 64)
	return val
}

// 断言字符串
func AssertString(value interface{}) string {
	val, _ := value.(string)
	return val
}

// 断言float64
func AssertFloat64(value interface{}) float64 {
	val, _ := value.(float64)
	return val
}

// check slice int64
func CheckInSliceByInt64(value int64, intSlice []int64) bool {
	for _, val := range intSlice {
		if val == value {
			return true
		}
	}

	return false
}

// check slice int64
func CheckInSliceByString(value string, stringSlice []string) bool {
	for _, val := range stringSlice {
		if val == value {
			return true
		}
	}

	return false
}

func Contains[T comparable](slice []T, target T) bool {
	for _, item := range slice {
		if item == target {
			return true
		}
	}

	return false
}

func StrToInt(value string) int {
	val, _ := strconv.ParseFloat(value, 64)
	return int(val)
}
