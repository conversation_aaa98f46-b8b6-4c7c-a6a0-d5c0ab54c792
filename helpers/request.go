package helpers

import (
	"context"
	"encoding/json"
	"html/template"
	"io"
	"strings"

	"icode.baidu.com/baidu/gdp/ghttp"
)

// 获取 body 内容
func GetBodyParams(ctx context.Context, req ghttp.Request) map[string]any {
	var bodyParams map[string]any
	if req.Body() == nil {
		return bodyParams
	}

	bodyData, err := io.ReadAll(req.Body())
	if err != nil {
		return bodyParams
	}
	defer req.Body().Close()
	_ = json.Unmarshal(bodyData, &bodyParams)
	LogParamsService(ctx, bodyParams)
	return bodyParams
}

func GetQueryParamByName(ctx context.Context, req ghttp.Request, name string, defaultValue string) string {
	return req.QueryDefault(name, defaultValue)
}

// 获取 int 参数
func GetIntParam(params map[string]any, name string, defaultValue int64) int64 {
	value := defaultValue
	if valueFloat, ok := params[name]; ok {
		if valueF, ok := valueFloat.(float64); ok {
			value = int64(valueF)
		}
	}

	return value
}

// 获取 float 参数
func GetFloatParam(params map[string]any, name string, defaultValue float64) float64 {
	value := defaultValue
	if valueFloat, ok := params[name]; ok {
		if valueF, ok := valueFloat.(float64); ok {
			value = valueF
		}
	}

	return value
}

// 获取bool 参数
func GetBoolParam(params map[string]any, name string, defaultValue bool) bool {
	value := defaultValue
	if valueFloat, ok := params[name]; ok {
		if valueF, ok := valueFloat.(bool); ok {
			value = valueF
		}
	}

	return value
}

// 获取 string 参数
func GetStringParam(params map[string]any, name string) string {
	if valueString, ok := params[name]; ok {
		if valueS, ok := valueString.(string); ok {
			return strings.TrimSpace(valueS)
		}
	}

	return ""
}

// 获取 string 参数
func GetHTMLStringParam(params map[string]any, name string) string {
	if valueString, ok := params[name]; ok {
		if valueS, ok := valueString.(string); ok {
			return template.HTMLEscapeString(strings.TrimSpace(valueS))
		}
	}

	return ""
}

// 获取 map 参数
func GetMapParam(params map[string]any, name string) map[string]any {
	if valueMap, ok := params[name]; ok {
		if value, ok := valueMap.(map[string]any); ok {
			return value
		}
	}

	return nil
}

// 获取 int 切片
func GetIntSliceParam(params map[string]any, name string) []int64 {
	var value []int64
	if valueSlice, ok := params[name]; ok {
		valueS, ok := valueSlice.([]any)
		if !ok {
			return value
		}

		for _, val := range valueS {
			if valNeed, ok := val.(float64); ok {
				value = append(value, int64(valNeed))
			}
		}
	}

	return value
}

// 获取 string 切片
func GetStringSliceParam(params map[string]any, name string) []string {
	var value []string
	if valueSlice, ok := params[name]; ok {
		valueS, ok := valueSlice.([]any)
		if !ok {
			return value
		}

		for _, val := range valueS {
			if valNeed, ok := val.(string); ok {
				value = append(value, strings.Trim(valNeed, " "))
			}
		}
	}

	return value
}

// 获取 map 切片
func GetMapSliceParam(params map[string]any, name string) []any {
	var value []any
	if valueSlice, ok := params[name]; ok {
		valueS, ok := valueSlice.([]any)
		if ok {
			return valueS
		}
	}

	return value
}
