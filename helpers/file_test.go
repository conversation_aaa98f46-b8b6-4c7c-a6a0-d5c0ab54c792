package helpers

import (
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
	"icode.baidu.com/baidu/gdp/env"
)

func TestWriteFile(t *testing.T) {
	err := WriteFile("/tmp/aa.txt", "content is test")
	assert.Equal(t, nil, err)
}

func TestReadTomlFile(t *testing.T) {
	tests := []struct {
		input string
		output error
	}{
		{env.ConfDir() + "/bos1.toml", errors.New("")},
		{env.ConfDir() + "/bos.toml", nil},
	}

	for _, test := range tests {
		_, got := ReadTomlFile(test.input)

		if test.output != nil {
			assert.NotEqual(t, nil, got)
			continue
		}
		assert.Equal(t, test.output, got)
	}
}
