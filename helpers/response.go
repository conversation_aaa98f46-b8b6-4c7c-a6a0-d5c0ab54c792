package helpers

import (
	"context"
	"encoding/json"
	"io"

	"icode.baidu.com/baidu/gdp/ghttp"
)

const (
	SuccessCode = 0

	CommonErrorCode = 1001

	ParamErrorCode   = 2001
	CaptchaErrorCode = 2002

	LoginErrorCode = 3001

	ServiceErrorCode = 4001

	LogicErrorCode       = 5001
	UserDayTaskErrorCode = 5002

	DBErrorCode = 6001

	AccountErrorCode = 7001

	RateLimitErrorCode         = 8001
	SubmitErrorCode            = 8002
	TaskCancelFailureErrorCode = 8003
	TooManyTask                = 8004
)

type JsonResp struct {
	Code  int    `json:"code"`
	LogId string `json:"logId"`
	Msg   string `json:"msg"`
	Data  any    `json:"data"`
}

func SuccReturn(ctx context.Context, data any) ghttp.Response {
	// 写日志
	jsonResp := JsonResp{
		Code:  SuccessCode,
		LogId: GetLogId(ctx),
		Msg:   "",
		Data:  data,
	}
	dataByte, _ := json.Marshal(jsonResp)
	LogNotice(ctx, string(dataByte))

	return ghttp.NewJSONResponse(200, jsonResp)
}

func FailReturn(ctx context.Context, code int, msg string) ghttp.Response {
	// 写日志
	jsonResp := JsonResp{
		Code:  code,
		LogId: GetLogId(ctx),
		Msg:   msg,
		Data:  nil,
	}
	dataByte, _ := json.Marshal(jsonResp)
	LogNotice(ctx, string(dataByte))

	return ghttp.NewJSONResponse(200, jsonResp)
}

func StreamReturn(rd io.Reader, headers map[string][]string) ghttp.Response {
	return &ghttp.StreamResponse{
		StatusCode:  200,
		Headers:     headers,
		Reader:      rd,
		ContentType: "application/octet-stream; charset=utf-8",
	}
}

// 异常信息处理，并构造返回结果
func NewException(ctx context.Context, key string, extraInfo ...any) ghttp.Response {
	err := NewError(key, extraInfo...).(easyErr)
	ReturnLogError(ctx, err)
	return BuildResponse(ctx, struct{}{}, err)
}

// 格式化返回结果
func BuildResponse(ctx context.Context, res any, err error) ghttp.Response {
	// 错误返回
	if err != nil {
		resp := &JsonResp{
			Code:  int(GetCode(err)),
			LogId: GetLogId(ctx),
			Msg:   GetMsg(err),
			Data:  nil,
		}
		return ghttp.NewJSONResponse(200, &resp)
	}

	// 正常返回
	resp := &JsonResp{
		Code:  SuccessCode,
		LogId: GetLogId(ctx),
		Msg:   "",
		Data:  res,
	}
	return ghttp.NewJSONResponse(200, &resp)
}
