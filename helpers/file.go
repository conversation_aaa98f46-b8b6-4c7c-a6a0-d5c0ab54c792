package helpers

import (
	"bufio"
	"errors"
	"os"
	"path/filepath"

	"icode.baidu.com/baidu/gdp/conf"
)

// 读取 toml 文件
func ReadTomlFile(filePath string) (map[string]interface{}, error) {
	var fileMap map[string]interface{}

	confPath, err := filepath.Abs(filePath)
	if err != nil {
		return fileMap, err
	}

	err = conf.Parse(confPath, &fileMap)
	if err != nil {
		return fileMap, err
	}
	if len(fileMap) <= 0 {
		return fileMap, errors.New(filePath + " file is empty")
	}

	return fileMap, nil
}

// 获取配置文件中的 field
func GetConfField(filePath, field string) (string, error) {
	confMap, err := ReadTomlFile(filePath)
	if err != nil {
		return "", err
	}

	content, ok := confMap[field].(string)
	if !ok || len(content) <= 0 {
		return "", errors.New(filePath + " " + field + " empty")
	}

	return content, nil
}

// 写入文件
func WriteFile(filePath string, contentStr string) error {
	file, err := os.OpenFile(filePath, os.O_WRONLY|os.O_CREATE, 0666)
	if err != nil {
		return err
	}
	defer file.Close()

	// 写入文件时
	write := bufio.NewWriter(file)
	write.WriteString(contentStr)

	// Flush将缓存的文件真正写入到文件中
	err = write.Flush()
	return err
}