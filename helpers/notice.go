package helpers

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"net/http"
	"net/smtp"
	"os"
	"strings"

	"icode.baidu.com/baidu/gdp/env"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/helix_web/library/resource"
)

const (
	HelixNoticeWebhookOnline = "http://apiin.im.baidu.com/api/msg/groupmsgsend?access_token=dd8a7d6ab288e3411b71f7ecdb763720d"
	HelixNoticeWebhook = "http://apiin.im.baidu.com/api/msg/groupmsgsend?access_token=d499469fc248ea82b3e1d0777059e2c44"

	FromUserBaidu = "<EMAIL>"
)

var noticeHookMap = map[string]string{
	env.RunModeDebug: HelixNoticeWebhook,
	env.RunModeTest: HelixNoticeWebhook,
	env.RunModeRelease: HelixNoticeWebhookOnline,
}

// helix notice
func HelixNotice(ctx context.Context, message string) {
	sendHiNotice(ctx, message, noticeHookMap[env.RunMode()], []string{})
}

// send notice
func sendHiNotice(ctx context.Context, message string, webhook string, userIds []string) {
	hostname, _ := os.Hostname()
	logId := ""

	field := logit.FindLogIDField(ctx)
	if field != nil {
		logId, _ = field.Value().(string)
	}

	message = env.RunMode() + "--[host:" + hostname + "]--[logid:" + logId + "]--" + message
	body := []map[string]interface{}{
		{
			"content": message,
			"type":    "TEXT",
		},
	}
	if len(userIds) > 0 {
		body = append(body, map[string]interface{}{
			"atuserids": userIds,
			"atall": false,
			"type": "AT",
		})
	}

	// 发送内容
	content := map[string]interface{} {
		"message": map[string]interface{} {
			"body": body,
		},
	}
	contentByte, err := json.Marshal(content)
	if err != nil {
		return
	}

	reqBody := strings.NewReader(string(contentByte))
	resp, err := http.Post(webhook, "application/json", reqBody)
	if err != nil {
		resource.LoggerService.Error(ctx, "------hi notice "+ err.Error())
		return
	}
	defer resp.Body.Close()
}

// 发送邮件通知
func SendEmailByBaidu(fromEmail, toEmail, subject, content string) error {
	toList := []string{toEmail}

	// 多个收件人使用英文逗号连接，To，能发出邮件，但是邮件里看不到收件人
	msg := []byte(
			"To: "+ toEmail +"\r\n" +
				"Subject: "+ subject +"\r\n" +
				"MIME-Version: 1.0\r\n" +
				"Content-Type: text/html; charset=utf-8\r\n" +
				"Content-Transfer-Encoding: base64\r\n" +
				"\r\n"+
				base64.StdEncoding.EncodeToString([]byte(content))+
				"\r\n")
	err := smtp.SendMail("mail1-in.baidu.com:25", nil, fromEmail, toList, msg)

	var ctx context.Context
	if err != nil {
		resource.LoggerService.Error(ctx, "------email notice failed:"+ err.Error())
	}
	return err
}