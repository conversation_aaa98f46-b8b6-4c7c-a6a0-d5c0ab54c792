package helpers

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestHelixNotice(t *testing.T) {
	ctx := context.Background()
	HelixNotice(ctx, "this is test")
}

func TestSendEmailByBaidu(t *testing.T) {
	tests := []struct {
		fromU string
		toU string
		subject string
		content string
	}{
		{FromUser, "<EMAIL>", "paddlepaddle", "this is a good news"},
		{FromUser, "<EMAIL>", "paddlepaddle-test", "this is a great news"},
	}

	for _, test := range tests {
		got := SendEmailByBaidu(test.fromU, test.toU, test.subject, test.content)
		assert.Equal(t, nil, got)
	}
}
