package jobs

import (
	"context"
	"time"

	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/library/resource"
	"icode.baidu.com/helix_web/models"
	"icode.baidu.com/helix_web/services"
)

// 消费消息
func SyncTask(ctx context.Context) {
	newCtx := logit.NewContext(ctx)
	defer helpers.DeferFunc(newCtx)()

	for {
		err := services.ConsumerTask(newCtx)
		if err != nil {
			helpers.LogError(newCtx, err)

			// 发送Hi
			go helpers.HelixNotice(newCtx, "--- mq error timeout pause ---"+err.Error())
		}

		// 睡眠 10 秒
		resource.LoggerService.Notice(newCtx, "------- mq sleep 10s -------")
		time.Sleep(time.Second * 10)
	}
}

// 消费预处理消息
func SyncPretreat(ctx context.Context) {
	newCtx := logit.NewContext(ctx)
	defer helpers.DeferFunc(newCtx)()

	for {
		err := services.ConsumerPretreat(newCtx)
		if err != nil {
			helpers.LogError(newCtx, err)

			// 发送Hi
			go helpers.HelixNotice(newCtx, "--- mq error timeout pause ---"+err.Error())
		}

		// 睡眠 10 秒
		resource.LoggerService.Notice(newCtx, "------- mq sleep 10s -------")
		time.Sleep(time.Second * 10)
	}
}

// 提交失败的任务
func SubmitTask(ctx context.Context) {
	newCtx := logit.NewContext(ctx)
	defer helpers.DeferFunc(newCtx)()

	taskM := models.Task{}
	for {
		logit.AddMetaFields(newCtx,
			logit.LogIDField(logit.NewLogID()),
		)

		// 获取提交失败的任务
		cond := models.TaskCond{
			StatusList: []int64{int64(models.TaskStatusNew)},
		}
		tasks, err := taskM.GetTaskByCond(newCtx, cond, 5, 1)
		if err != nil || len(tasks) == 0 {
			// 睡眠 120 s
			resource.LoggerService.Notice(newCtx, "------- no tasks need submit sleep 120s -------")
			time.Sleep(time.Second * 120)
			continue
		}

		for _, task := range tasks {
			if int(task.Type) != models.TaskTypeHelixFoldAA && int(task.Type) != models.TaskTypeHelixFold3S1 {
				services.PreSubmitTask(newCtx, task)
			}
		}

		// 休息 60 s
		time.Sleep(time.Second * 60)
	}
}

// 处理账单
func PushBill(ctx context.Context) {
	newCtx := logit.NewContext(ctx)
	defer helpers.DeferFunc(newCtx)()

	taskM := models.Task{}
	for {
		logit.AddMetaFields(newCtx,
			logit.LogIDField(logit.NewLogID()),
		)

		// 获取提交失败的任务
		cond := models.TaskCond{
			StatusList: []int64{int64(models.TaskStatusSucc)},
			ChargeTab:  []int64{int64(models.ChargeTabCoupon), int64(models.ChargeTabBilling)},
		}
		tasks, err := taskM.GetTaskByCond(newCtx, cond, 5, 1)
		if err != nil || len(tasks) == 0 {
			// 睡眠 120 s
			resource.LoggerService.Notice(newCtx, "------- no bill need push sleep 120s -------")
			time.Sleep(time.Second * 120)
			continue
		}

		for _, task := range tasks {
			services.DoCharge(newCtx, task)
		}

		// 休息 60 s
		time.Sleep(time.Second * 60)
	}
}

// 处理HelixFoldAA消息
func SyncTaskAA(ctx context.Context) {
	newCtx := logit.NewContext(ctx)
	defer helpers.DeferFunc(newCtx)()

	for {
		err := services.ConsumerTaskAA(newCtx)
		if err != nil {
			helpers.LogError(newCtx, err)

			// 发送Hi
			go helpers.HelixNotice(newCtx, "--- mq error timeout pause ---"+err.Error())
		}

		// 睡眠 10 秒
		resource.LoggerService.Notice(newCtx, "------- mq sleep 10s -------")
		time.Sleep(time.Second * 10)
	}
}
