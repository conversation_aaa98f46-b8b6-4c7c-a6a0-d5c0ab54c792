package models

import (
	"context"
	"encoding/json"
	"time"

	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/library/resource"
)

type Achievement struct {
	ID          uint64    `gorm:"id"`
	AdminId     uint64    `gorm:"admin_id"`
	Title       string    `gorm:"title"`
	Author      string    `gorm:"author"`
	Url         string    `gorm:"url"`
	TagList     string    `gorm:"tag_list"`
	TagListEn   string    `gorm:"tag_list_en"`
	Status      int64     `gorm:"status"`
	PublishTime time.Time `gorm:"publish_time"`
	CreatedAt   time.Time `gorm:"created_at"`
	UpdatedAt   time.Time `gorm:"updated_at"`
}

func (a *Achievement) TableName() string {
	return "helix_achievement"
}

// 获取 achievement 列表
func (a *Achievement) GetList(ctx context.Context, limit, page int, orderBy ...string) ([]Achievement, error) {
	db := resource.Gorm.WithContext(ctx)

	// 排序处理
	order := "publish_time desc"
	if len(orderBy) > 0 {
		order = orderBy[0]
	}

	// 拼接where
	condWhere := map[string]interface{}{
		"status": ShowStatus,
	}

	// 执行查询
	var achievementList []Achievement
	offset := limit * (page - 1)
	err := db.Where(condWhere).Limit(limit).Offset(offset).Order(order).Find(&achievementList).Error
	if err != nil {
		helpers.DBLogError(ctx, err)
		go helpers.HelixNotice(ctx, err.Error())
	}
	return achievementList, err
}

// 数据转化
func (a *Achievement) SwapData() map[string]interface{} {
	var achievementData map[string]interface{}
	if a.ID <= 0 {
		return achievementData
	}

	var tagList, tagListEn []string
	_ = json.Unmarshal([]byte(a.TagList), &tagList)
	_ = json.Unmarshal([]byte(a.TagListEn), &tagListEn)

	achievementData = map[string]interface{}{
		"id":           a.ID,
		"admin_id":     a.AdminId,
		"title":        a.Title,
		"author":       a.Author,
		"url":          a.Url,
		"status":       a.Status,
		"tag_list":     tagList,
		"tag_list_en":  tagListEn,
		"publish_time": SwapFieldUnix(a.PublishTime.Unix()),
		"created_at":   a.CreatedAt.Unix(),
		"updated_at":   a.UpdatedAt.Unix(),
	}
	return achievementData
}
