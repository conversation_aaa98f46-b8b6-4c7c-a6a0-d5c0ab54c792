package models

import (
	"context"

	"gorm.io/gorm"

	"icode.baidu.com/helix_web/library/resource"
)

const (
	DefaultPageSize int = 20 // 默认分页大小
	DefaultPageNum  int = 1
)

// Ccd 表结构
type Ccd struct {
	ID           uint64 `gorm:"id" json:"id"`
	Key          string `gorm:"key" json:"key"`
	SynonymsName string `gorm:"synonyms_name" json:"synonyms_name"`
	Smiles       string `gorm:"smiles" json:"smiles"`
	NTokens      uint64 `gorm:"n_tokens" json:"n_tokens"`
}

func (c *Ccd) TableName() string {
	return "helix_ccd"
}

type CcdCond struct {
	Key      string
	PageNum  int
	PageSize int
}

// QueryCcdList 根据key检索ccd记录列表，检索范围包括Key、SynonymsName字段
func (c *Ccd) QueryCcdList(ctx context.Context, ccdCond CcdCond) ([]Ccd, error) {
	// 装配DB
	db := resource.Gorm.WithContext(ctx)
	db = assembleDB(db, ccdCond)
	// 执行查询
	var ccdList []Ccd
	err := db.Find(&ccdList).Error
	if err != nil {
		return nil, err
	}
	return ccdList, nil
}

// 根据条件装配DB
func assembleDB(db *gorm.DB, ccdCond CcdCond) *gorm.DB {
	// 分页信息
	pageNum := DefaultPageNum
	pageSize := DefaultPageSize
	if ccdCond.PageNum > 0 {
		pageNum = ccdCond.PageNum
	}
	if ccdCond.PageSize > 0 {
		pageSize = ccdCond.PageSize
	}
	// 全文索引信息
	searchKey := ccdCond.Key + "*"
	likeKey := ccdCond.Key + "%"

	db = db.Select("*, MATCH(`key`, `synonyms_name`) AGAINST(? IN BOOLEAN MODE) AS relevance", searchKey).
		Where("MATCH(`key`, `synonyms_name`) AGAINST(? IN BOOLEAN MODE)", searchKey).
		Order(gorm.Expr("CASE WHEN `key` LIKE ? THEN 1 ELSE 2 END, relevance DESC", likeKey)).
		Offset((pageNum - 1) * pageSize).
		Limit(pageSize)

	return db
}
