package models

import (
	"context"
	"time"

	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/library/resource"
)

const (
	FeedbarTypeAchievement = 10
	FeedbarTypeAward       = 20
	FeedbarTypeBusiness    = 30
	FeedbarTypeVersion     = 40

	ShowStatus = 20
)

type Feedbar struct {
	ID          uint64    `gorm:"id"`
	AdminId     uint64    `gorm:"admin_id"`
	Content     string    `gorm:"content"`
	ContentEN   string    `gorm:"content_en"`
	Sort        uint64    `gorm:"sort"`
	Url         string    `gorm:"url"`
	Type        uint64    `gorm:"type"`
	Status      int64     `gorm:"status"`
	PicUrl      string    `gorm:"pic_url"`
	IsHome      uint64    `gorm:"is_home"`
	IsTop       uint64    `gorm:"is_top"`
	DisplayTime time.Time `gorm:"display_time"`
	CreatedAt   time.Time `gorm:"created_at"`
	UpdatedAt   time.Time `gorm:"updated_at"`
}

func (f *Feedbar) TableName() string {
	return "helix_feed_bar"
}

// 获取 feedbar 列表
func (f *Feedbar) GetList(ctx context.Context, limit, page int, orderBy ...string) ([]Feedbar, error) {
	db := resource.Gorm.WithContext(ctx)

	// 排序处理
	order := "display_time desc"
	if len(orderBy) > 0 {
		order = orderBy[0]
	}

	// 拼接where
	condWhere := map[string]interface{}{
		"status": ShowStatus,
	}

	// 执行查询
	var feedbarList []Feedbar
	offset := limit * (page - 1)
	err := db.Where(condWhere).Limit(limit).Offset(offset).Order(order).Find(&feedbarList).Error
	if err != nil {
		helpers.DBLogError(ctx, err)
		go helpers.HelixNotice(ctx, err.Error())
	}
	return feedbarList, err
}

// 数据转化
func (f *Feedbar) SwapData() map[string]interface{} {
	var data map[string]interface{}
	if f.ID <= 0 {
		return data
	}

	data = map[string]interface{}{
		"id":           f.ID,
		"admin_id":     f.AdminId,
		"url":          f.Url,
		"type":         f.Type,
		"content":      f.Content,
		"content_en":   f.ContentEN,
		"sort":         f.Sort,
		"status":       f.Status,
		"pic_url":      f.PicUrl,
		"is_home":      f.IsHome,
		"is_top":       f.IsTop,
		"display_time": SwapFieldUnix(f.DisplayTime.Unix()),
		"created_at":   f.CreatedAt.Unix(),
		"updated_at":   f.UpdatedAt.Unix(),
	}
	return data
}
