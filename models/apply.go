package models

import (
	"context"
	"time"

	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/library/resource"
)

// Apply 表结构
type Apply struct {
	ID        uint64    `gorm:"id"`
	Name      string    `gorm:"name"`
	Phone     string    `gorm:"phone"`
	Email     string    `gorm:"email"`
	Company   string    `gorm:"company"`
	Content   string    `gorm:"content"`
	CreatedAt time.Time `gorm:"created_at"`
}

func (a *Apply) TableName() string {
	return "helix_charge_apply"
}

// 添加用户
func (a *Apply) Add(ctx context.Context, applyNew Apply) (Apply, error) {
	applyNew.CreatedAt = time.Now()

	db := resource.Gorm.WithContext(ctx)
	err := db.Create(&applyNew).Error
	if err != nil {
		helpers.DBLogError(ctx, err)
		go helpers.HelixNotice(ctx, err.<PERSON><PERSON><PERSON>())
	}

	return applyNew, err
}



