package models

import (
	"context"
	"time"

	"gorm.io/gorm"
	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/library/resource"
)

const (
	ChargeTypeNum = 10
	ChargeTypeDuration = 20

	NeedPushTrue  = 1
	NeedPushFalse = 0
)

// 表结构
type Bill struct {
	ID             uint64    `gorm:"id"`
	UserId         uint64    `gorm:"user_id"`
	RealId         uint64    `gorm:"real_id"`
	TaskId         uint64    `gorm:"task_id"`
	TaskType       uint64    `gorm:"task_type"`
	ChargeType     uint64    `gorm:"charge_type"`
	ConsoleApiId   uint64    `gorm:"console_api_id"`
	LogId          string    `gorm:"log_id"`
	StartTime      time.Time `gorm:"start_time"`
	EndTime        time.Time `gorm:"end_time"`
	ChargeNum      uint64    `gorm:"charge_num"`
	ChargeDuration uint64    `gorm:"charge_duration"`
	CostAmount     float64   `gorm:"cost_amount"`
	CouponIdList   string    `gorm:"coupon_id_list"`
	NeedPush       uint64    `gorm:"need_push"`
	Level          uint64    `gorm:"level"`
	CreatedAt      time.Time `gorm:"created_at"`
}

func (b *Bill) TableName() string {
	return "helix_bill"
}

type BillCond struct {
	UserId int64
	RealId int64
	NeedPush []int
}

func (b *Bill) Add(ctx context.Context, billNew Bill) (Bill, error) {
	billNew.CreatedAt = time.Now()

	db := resource.Gorm.WithContext(ctx)
	err := db.Create(&billNew).Error
	if err != nil {
		helpers.DBLogError(ctx, err)
		go helpers.HelixNotice(ctx, err.Error())
	}

	return billNew, err
}

// 获取账单
func (b *Bill) GetList(ctx context.Context, billCond BillCond, limit, page int) ([]Bill, error) {
	db := resource.Gorm.WithContext(ctx)
	db = billBuildWhere(db, billCond)

	// 执行查询
	var billList []Bill
	offset := limit * (page - 1)
	err := db.Limit(limit).Offset(offset).Order("id desc").Find(&billList).Error
	if err != nil {
		helpers.DBLogError(ctx, err)
		go helpers.HelixNotice(ctx, err.Error())

		return billList, err
	}
	return billList, nil
}

// 统计数据
func  (b *Bill) Count(ctx context.Context, billCond BillCond) (int64, error) {
	db := resource.Gorm.WithContext(ctx)
	db = billBuildWhere(db, billCond)

	// 执行查询
	var count int64
	err := db.Model(b).Count(&count).Error
	if err != nil {
		helpers.DBLogError(ctx, err)
		go helpers.HelixNotice(ctx, err.Error())
	}
	return count, err
}

// where build
func billBuildWhere(db *gorm.DB, billCond BillCond) *gorm.DB {
	if billCond.UserId > 0 {
		db = db.Where("user_id = ?", billCond.UserId)
	}
	if billCond.RealId > 0 {
		db = db.Where("real_id = ?", billCond.RealId)
	}
	if len(billCond.NeedPush) > 0 {
		db = db.Where("need_push in (?)", billCond.NeedPush)
	}

	return db
}



