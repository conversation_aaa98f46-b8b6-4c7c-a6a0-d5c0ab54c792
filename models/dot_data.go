package models

import (
	"context"
	"time"

	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/library/resource"
)

const (
	DotTypeNor  = 10
	DotTypeHome = 11
	DotTypePV   = 20
	DotTypeHQDL = 30
	DotTypeWCDL = 40
)

type DotData struct {
	ID        uint64    `gorm:"id"`
	UserId    uint64    `gorm:"user_id"`
	Type      uint64    `gorm:"type"`
	Host      string    `gorm:"host"`
	Content   string    `gorm:"content"`
	CreatedAt time.Time `gorm:"created_at"`
}

func (d *DotData) TableName() string {
	return "helix_dot_data"
}

// Add 添加用户
func (d *DotData) Add(ctx context.Context, DotDataNew DotData) (DotData, error) {
	DotDataNew.CreatedAt = time.Now()

	db := resource.Gorm.WithContext(ctx)
	err := db.Create(&DotDataNew).Error
	if err != nil {
		helpers.DBLogError(ctx, err)
		go helpers.HelixNotice(ctx, err.Error())
	}

	return DotDataNew, err
}
