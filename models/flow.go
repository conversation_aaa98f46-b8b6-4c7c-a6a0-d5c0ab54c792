package models

import (
	"context"
	"gorm.io/gorm"
	"time"

	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/library/resource"
)

// 计费流量总览
type Flow struct {
	ID         uint64    `gorm:"id"`
	UserId     uint64    `gorm:"user_id"`
	TaskId     uint64    `gorm:"task_id"`
	TaskType   uint64    `gorm:"task_type"`
	ChargeTime uint64    `gorm:"charge_time"`
	CreatedAt  time.Time `gorm:"created_at"`
	UpdatedAt  time.Time `gorm:"updated_at"`
}

func (f *Flow) TableName() string {
	return "helix_flow"
}

// Add 添加flow 记录
func (f *Flow) Add(ctx context.Context, flowNew Flow) (Flow, error) {
	flowNew.UpdatedAt = time.Now()

	db := resource.Gorm.WithContext(ctx)
	err := db.Create(&flowNew).Error
	if err != nil {
		helpers.DBLogError(ctx, err)
		go helpers.HelixNotice(ctx, err.Error())
	}

	return flowNew, err
}

// 修改flow数据
func (f *Flow) Save(ctx context.Context) error {
	db := resource.Gorm.WithContext(ctx)
	err := db.Model(f).Updates(&f).Error
	if err != nil {
		helpers.DBLogError(ctx, err)
		go helpers.HelixNotice(ctx, err.Error())
	}

	return err
}

// 批量查询用户信息
func (f *Flow) GetListByCond(ctx context.Context, flowCond Flow, limit int, page int) ([]Flow, error) {
	// 获取数据信息
	db := resource.Gorm.WithContext(ctx)
	db = flowBuildWhere(db, flowCond)

	// 执行查询
	var FlowList []Flow
	offset := limit * (page - 1)
	err := db.Limit(limit).Offset(offset).Order("id desc").Find(&FlowList).Error
	if err != nil {
		helpers.DBLogError(ctx, err)
		go helpers.HelixNotice(ctx, err.Error())
	}

	return FlowList, err
}

// 统计数据
func (f *Flow) CountByCond(ctx context.Context, flowCond Flow) (int64, error) {
	// 获取数据信息
	db := resource.Gorm.WithContext(ctx)
	db = flowBuildWhere(db, flowCond)

	// 执行查询
	var count int64
	err := db.Table(f.TableName()).Count(&count).Error
	if err != nil {
		helpers.DBLogError(ctx, err)
		go helpers.HelixNotice(ctx, err.Error())
	}

	return count, err
}

// 数据转化
func (f *Flow) SwapData() map[string]interface{} {
	var flowData map[string]interface{}
	if f.ID <= 0 {
		return flowData
	}

	flowData = map[string]interface{}{
		"id":         f.ID,
		"user_id":    f.UserId,
		"task_id":    f.TaskId,
		"task_type":  f.TaskType,
		"created_at": f.CreatedAt.Unix(),
		"updated_at": f.UpdatedAt.Unix(),
	}
	return flowData
}

// where build
func flowBuildWhere(db *gorm.DB, flowCond Flow) *gorm.DB {
	if flowCond.UserId > 0 {
		db = db.Where("user_id = ?", flowCond.UserId)
	}

	if flowCond.TaskType > 0 {
		db = db.Where("task_type = ?", flowCond.TaskType)
	}

	return db
}
