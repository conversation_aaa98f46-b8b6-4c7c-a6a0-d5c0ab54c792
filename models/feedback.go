package models

import (
	"context"
	"time"

	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/library/resource"
)

// User 表User的结构
type Feedback struct {
	ID        uint64    `gorm:"id"`
	Name      string    `gorm:"name"`
	Phone     string    `gorm:"phone"`
	Email     string    `gorm:"email"`
	Company   string    `gorm:"company"`
	Content   string    `gorm:"content"`
	DealTime  time.Time `gorm:"deal_time"`
	CreatedAt time.Time `gorm:"created_at"`
	UpdatedAt time.Time `gorm:"updated_at"`
}

func (f *Feedback) TableName() string {
	return "helix_feedback"
}

// 添加用户
func (f *Feedback) Add(ctx context.Context, feedbackNew Feedback) (Feedback, error) {
	feedbackNew.CreatedAt = time.Now()

	db := resource.Gorm.WithContext(ctx)
	err := db.Create(&feedbackNew).Error
	if err != nil {
		helpers.DBLogError(ctx, err)
		go helpers.HelixNotice(ctx, err.Error())
	}

	return feedbackNew, err
}



