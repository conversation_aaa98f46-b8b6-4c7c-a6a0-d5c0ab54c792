package models

import (
	"context"
	"encoding/json"
	"gorm.io/gorm"
	"time"

	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/library/resource"
)

// pretreat 的结构
type Pretreat struct {
	ID           uint64    `gorm:"id"`
	UserId       uint64    `gorm:"user_id"`
	Type         uint64    `gorm:"type"`
	Config       string    `gorm:"config"`
	Result       string    `gorm:"result"`
	ServerTaskId uint64    `gorm:"server_task_id"`
	Status       int64     `gorm:"status"`
	FinishTime   time.Time `gorm:"finish_time"`
	CreatedAt    time.Time `gorm:"created_at"`
	UpdatedAt    time.Time `gorm:"updated_at"`
}

type PretreatConf struct {
	FileUrl string `json:"file_url"`
	Serial  string `json:"serial"`
}

type PretreatRes struct {
	FileUrl   string `json:"file_url"`
	ErrorCode uint64 `json:"error_code"`
	PdbUrl    string `json:"pdb_url"`
}

func (p *Pretreat) TableName() string {
	return "helix_pretreat"
}

// 获取信息
func (p *Pretreat) GetById(ctx context.Context, pretreatId int64) (Pretreat, error) {
	db := resource.Gorm.WithContext(ctx)

	// 拼接where
	condWhere := map[string]interface{}{
		"id": pretreatId,
	}

	// 执行查询
	pretreat := Pretreat{}
	err := db.Where(condWhere).First(&pretreat).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		helpers.DBLogError(ctx, err)
		go helpers.HelixNotice(ctx, err.Error())
		return pretreat, err
	}
	return pretreat, nil
}

// 获取任务信息
func (p *Pretreat) GetByUserAndId(ctx context.Context, userId, pretreatId int64) (Pretreat, error) {
	db := resource.Gorm.WithContext(ctx)

	// 拼接where
	condWhere := map[string]any{
		"id":      pretreatId,
		"user_id": userId,
	}

	// 执行查询
	pretreat := Pretreat{}
	err := db.Where(condWhere).First(&pretreat).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		helpers.DBLogError(ctx, err)
		go helpers.HelixNotice(ctx, err.Error())
		return pretreat, err
	}
	return pretreat, nil
}

// 添加任务
func (p *Pretreat) Add(ctx context.Context, PretreatNew Pretreat) (Pretreat, error) {
	PretreatNew.Status = int64(TaskStatusDoing)
	PretreatNew.CreatedAt = time.Now()
	PretreatNew.UpdatedAt = time.Now()

	db := resource.Gorm.WithContext(ctx)
	err := db.Create(&PretreatNew).Error
	if err != nil {
		helpers.DBLogError(ctx, err)
		go helpers.HelixNotice(ctx, err.Error())
	}
	return PretreatNew, err
}

// 保存任务
func (p *Pretreat) Save(ctx context.Context) error {
	// 获取实例
	db := resource.Gorm.WithContext(ctx)
	err := db.Model(p).Updates(&p).Error
	if err != nil {
		helpers.DBLogError(ctx, err)
		go helpers.HelixNotice(ctx, err.Error())
	}

	return err
}

// 数据转化
func (p *Pretreat) SwapData() map[string]interface{} {
	var taskData map[string]interface{}
	if p.ID <= 0 {
		return taskData
	}

	// 处理数据
	var config map[string]interface{}
	_ = json.Unmarshal([]byte(p.Config), &config)

	var result map[string]interface{}
	_ = json.Unmarshal([]byte(p.Result), &result)

	taskData = map[string]interface{}{
		"id":          p.ID,
		"user_id":     p.UserId,
		"type":        p.Type,
		"status":      p.Status,
		"config":      config,
		"result":      result,
		"finish_time": SwapFieldUnix(p.FinishTime.Unix()),
		"created_at":  p.CreatedAt.Unix(),
		"updated_at":  p.UpdatedAt.Unix(),
	}
	return taskData
}
