package models

import (
	"context"
	"gorm.io/gorm"
	"time"

	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/library/resource"
)

const (
	CommentType10 = 10
	CommentType20 = 20
	CommentType30 = 30
	CommentType40 = 40
	CommentType50 = 50
	CommentType60 = 60
)

var AwardTypeList = []int{
	CommentType10,
	CommentType20,
	CommentType30,
	CommentType40,
}

type Comment struct {
	ID           uint64    `gorm:"id"`
	UserId       uint64    `gorm:"user_id"`
	Username     string    `gorm:"username"`
	Type         uint64    `gorm:"type"`
	Score        float64   `gorm:"score"`
	Content      string    `gorm:"content"`
	OpenContent  string    `gorm:"open_content"`
	ExtraComment string    `gorm:"extra_comment"`
	AwardNum     uint64    `gorm:"award_num"`
	TaskId       uint64    `gorm:"task_id"`
	TaskType     uint64    `gorm:"task_type"`
	CreatedAt    time.Time `gorm:"created_at"`
	UpdatedAt    time.Time `gorm:"updated_at"`
}

func (c *Comment) TableName() string {
	return "helix_user_comment"
}

// 添加
func (c *Comment) Add(ctx context.Context, commentNew Comment) (Comment, error) {
	commentNew.CreatedAt = time.Now()

	db := resource.Gorm.WithContext(ctx)
	err := db.Create(&commentNew).Error
	if err != nil {
		helpers.DBLogError(ctx, err)
		go helpers.HelixNotice(ctx, err.Error())
	}

	return commentNew, err
}

func (c *Comment) GetLastAward(ctx context.Context, userId, typ int64) (Comment, error) {
	db := resource.Gorm.WithContext(ctx)
	db = db.Where("award_num > ?", 0)

	// 拼接where
	condWhere := map[string]interface{} {
		"user_id": userId,
		"type": typ,
	}

	// 执行查询
	row := Comment{}
	err := db.Where(condWhere).Order("id desc").First(&row).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		helpers.DBLogError(ctx, err)
		go helpers.HelixNotice(ctx, err.Error())
		return row, err
	}
	return row, nil
}

func (c *Comment) GetByUserId(ctx context.Context, userId, typ, taskId int64) (Comment, error) {
	db := resource.Gorm.WithContext(ctx)

	// 拼接where
	condWhere := map[string]interface{} {
		"user_id": userId,
		"type": typ,
	}
	if taskId >= 0 {
		condWhere["task_id"] = taskId
	}

	// 执行查询
	row := Comment{}
	err := db.Where(condWhere).Order("id desc").First(&row).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		helpers.DBLogError(ctx, err)
		go helpers.HelixNotice(ctx, err.Error())
		return row, err
	}
	return row, nil
}

func (c *Comment) GetByTaskId(ctx context.Context, userId, taskId int64) (Comment, error) {
	db := resource.Gorm.WithContext(ctx)

	// 拼接where
	condWhere := map[string]interface{} {
		"user_id": userId,
		"task_id": taskId,
	}

	// 执行查询
	row := Comment{}
	err := db.Where(condWhere).Order("id desc").First(&row).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		helpers.DBLogError(ctx, err)
		go helpers.HelixNotice(ctx, err.Error())
		return row, err
	}
	return row, nil
}

// 修改数据
func (c *Comment) Save(ctx context.Context) error {
	db := resource.Gorm.WithContext(ctx)
	err := db.Model(c).Updates(&c).Error
	if err != nil {
		helpers.DBLogError(ctx, err)
		go helpers.HelixNotice(ctx, err.Error())
	}

	return err
}
