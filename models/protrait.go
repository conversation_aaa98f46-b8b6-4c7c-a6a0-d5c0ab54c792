package models

import (
	"context"
	"gorm.io/gorm"
	"time"

	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/library/resource"
)

type Portrait struct {
	ID        uint64    `gorm:"id"`
	UserId    uint64    `gorm:"user_id"`
	Username  string    `gorm:"username"`
	Channel   string    `gorm:"channel"`
	Demand    string    `gorm:"demand"`
	Industry  string    `gorm:"industry"`
	Career    string    `gorm:"career"`
	AwardNum  uint64    `gorm:"award_num"`
	Code      string    `gorm:"code"`
	CreatedAt time.Time `gorm:"created_at"`
	UpdatedAt time.Time `gorm:"updated_at"`
}

func (p *Portrait) TableName() string {
	return "helix_portrait"
}

// 添加
func (p *Portrait) Add(ctx context.Context, portraitNew Portrait) (Portrait, error) {
	portraitNew.CreatedAt = time.Now()

	db := resource.Gorm.WithContext(ctx)
	err := db.Create(&portraitNew).Error
	if err != nil {
		helpers.DBLogError(ctx, err)
		go helpers.HelixNotice(ctx, err.Error())
	}

	return portraitNew, err
}

func (p *Portrait) GetByUserId(ctx context.Context, userId int64) (Portrait, error) {
	db := resource.Gorm.WithContext(ctx)

	// 拼接where
	condWhere := map[string]interface{}{
		"user_id": userId,
	}

	// 执行查询
	row := Portrait{}
	err := db.Where(condWhere).First(&row).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		helpers.DBLogError(ctx, err)
		go helpers.HelixNotice(ctx, err.Error())
		return row, err
	}
	return row, nil
}

func (p *Portrait) GetByCode(ctx context.Context, code string) (Portrait, error) {
	db := resource.Gorm.WithContext(ctx)

	// 拼接where
	condWhere := map[string]any{
		"code": code,
	}

	// 执行查询
	row := Portrait{}
	err := db.Where(condWhere).First(&row).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		helpers.DBLogError(ctx, err)
		go helpers.HelixNotice(ctx, err.Error())
		return row, err
	}
	return row, nil
}

func (p *Portrait) Save(ctx context.Context) error {
	db := resource.Gorm.WithContext(ctx)
	err := db.Model(p).Updates(&p).Error
	if err != nil {
		helpers.DBLogError(ctx, err)
		go helpers.HelixNotice(ctx, err.Error())
	}

	return err
}
