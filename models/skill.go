package models

import (
	"context"
	"encoding/json"
	"gorm.io/gorm"
	"time"

	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/library/resource"
)

const (
	SkillTypeXFZ   = 10
	SkillTypeDB    = 20
	SkillTypeDBXFZ = 30
	SkillTypeDBDB  = 40
	SkillTypeRNA   = 50

	SkillStatusOnline = 20
)

var SkillTypeMap = map[int]bool{
	SkillTypeXFZ:   true,
	SkillTypeDB:    true,
	SkillTypeDBXFZ: true,
	SkillTypeDBDB:  true,
	SkillTypeRNA:   true,
}

type Skill struct {
	ID        uint64    `gorm:"id"`
	AdminId   uint64    `gorm:"admin_id"`
	Type      uint64    `gorm:"type"`
	Sort      uint64    `gorm:"type"`
	Status    int64     `gorm:"status"`
	Data      string    `gorm:"data"`
	CreatedAt time.Time `gorm:"created_at"`
	UpdatedAt time.Time `gorm:"updated_at"`
}

func (s *Skill) TableName() string {
	return "helix_skill"
}

// GetById 通过Id获取信息
func (s *Skill) GetById(ctx context.Context, id int64) (Skill, error) {
	db := resource.Gorm.WithContext(ctx)
	db = db.Where("status <> ?", StatusDel)
	condWhere := map[string]any{
		"id": id,
	}

	// 执行查询
	SkillData := Skill{}
	err := db.Where(condWhere).First(&SkillData).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		helpers.DBLogError(ctx, err)
		go helpers.HelixNotice(ctx, err.Error())

		return SkillData, err
	}
	return SkillData, nil
}

func (s *Skill) GetByType(ctx context.Context, tType int64) ([]Skill, error) {
	db := resource.Gorm.WithContext(ctx)
	db = db.Where("status <> ?", StatusDel)
	if tType > 0 {
		db = db.Where("type = ?", tType)
	}

	// 执行查询
	var skillList []Skill
	err := db.Find(&skillList).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		helpers.DBLogError(ctx, err)
		go helpers.HelixNotice(ctx, err.Error())

		return skillList, err
	}
	return skillList, nil
}

// 获取 Skill 列表
func (s *Skill) GetAll(ctx context.Context) ([]Skill, error) {
	db := resource.Gorm.WithContext(ctx)
	condWhere := map[string]interface{}{
		"status": SkillStatusOnline,
	}

	// 执行查询
	var SkillList []Skill
	err := db.Where(condWhere).Order("sort asc, created_at desc").Find(&SkillList).Error
	if err != nil {
		helpers.DBLogError(ctx, err)
		go helpers.HelixNotice(ctx, err.Error())
	}
	return SkillList, err
}

// SwapData 数据转化
func (s *Skill) SwapData() map[string]interface{} {
	var data map[string]interface{}
	if s.ID <= 0 {
		return data
	}

	var content interface{}
	_ = json.Unmarshal([]byte(s.Data), &content)

	data = map[string]interface{}{
		"id":         s.ID,
		"admin_id":   s.AdminId,
		"type":       s.Type,
		"sort":       s.Sort,
		"status":     s.Status,
		"data":       content,
		"created_at": s.CreatedAt.Unix(),
		"updated_at": s.UpdatedAt.Unix(),
	}
	return data
}
