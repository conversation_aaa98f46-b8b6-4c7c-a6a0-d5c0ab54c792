package models

import (
	"context"
	"time"

	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/library/resource"
)

// 表结构
type RequestLog struct {
	ID        uint64    `gorm:"id"`
	UserId    uint64    `gorm:"user_id"`
	Uri       string    `gorm:"uri"`
	CreatedAt time.Time `gorm:"created_at"`
}

func (r *RequestLog) TableName() string {
	return "helix_request_log"
}

// 添加记录
func (r *RequestLog) Add(ctx context.Context, requestNew RequestLog) (RequestLog, error) {
	requestNew.CreatedAt = time.Now()

	db := resource.Gorm.WithContext(ctx)
	err := db.Create(&requestNew).Error
	if err != nil {
		helpers.DBLogError(ctx, err)
		go helpers.HelixNotice(ctx, err.Error())
	}

	return requestNew, err
}



