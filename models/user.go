package models

import (
	"context"
	"time"

	"gorm.io/gorm"

	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/library/resource"
)

const (
	UserTypeInside int = 1
	UserTypeNormal int = 10
	UserTypeCharge int = 20
	UserTypeTry    int = 30

	DefaultCameoRealID          int = ********** // yueyang
	DefaultInnerRealID          int = ********** // 线上
	UserFreeTaskLimit           int = 10
	UserFreeHelixFold3TaskLimit int = 20

	SourcePassport int = 10
	SourceUC       int = 11
	SourceIAM      int = 12
	SourceGitHub   int = 20

	AccountPassType = "passport"
	AccountUCType   = "uc"
)

var SourceStringToInt = map[string]int{
	AccountPassType: SourcePassport,
	AccountUCType:   SourceUC,
}

// 表结构
type User struct {
	ID           uint64    `gorm:"id"`
	Username     string    `gorm:"username"`
	DisplayName  string    `gorm:"display_name"`
	RealID       uint64    `gorm:"real_id"`
	UserID       string    `gorm:"user_id"`
	UserDomainID string    `gorm:"user_domain_id"`
	Type         uint64    `gorm:"type"`
	Email        string    `gorm:"email"`
	Phone        string    `gorm:"phone"`
	Source       uint64    `gorm:"source"`
	NeedSwitch   uint64    `gorm:"need_switch"`
	LoginTime    time.Time `gorm:"login_time"`
	ChangeTime   time.Time `gorm:"change_time"`
	CreatedAt    time.Time `gorm:"created_at"`
	UpdatedAt    time.Time `gorm:"updated_at"`
}

// UserInfo 结构体
type UserInfo struct {
	UserID          int64  `json:"user_id"`
	RealID          int64  `json:"real_id"`
	IAMUserID       string `json:"iam_user_id"`
	IAMUserDomainID string `json:"iam_user_domain_id"`
	DisplayName     string `json:"display_name"`
	Type            int64  `json:"type"`
	Source          int64  `json:"source"`
	Phone           string `json:"phone"`
	Avatar          string `json:"avatar"`
	NeedPopup       bool   `json:"need_popup"`
	HadPortrait     bool   `json:"had_portrait"`
	NeedBox         bool   `json:"need_box"`
}

func (u *User) TableName() string {
	return "helix_user"
}

// 获取用户信息
func (u *User) GetUserByID(ctx context.Context, id int64) (User, error) {
	db := resource.Gorm.WithContext(ctx)

	// 拼接where
	condWhere := map[string]any{
		"id": id,
	}

	// 执行查询
	userData := User{}
	err := db.Where(condWhere).First(&userData).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		go helpers.HelixNotice(ctx, err.Error())

		return userData, err
	}
	return userData, nil
}

// 获取用户信息
func (u *User) GetUserByRealID(ctx context.Context, realID int64, userID string, source ...int64) (User, error) {
	db := resource.Gorm.WithContext(ctx)

	// 拼接where
	condWhere := map[string]any{
		"real_id": realID,
		"source":  SourcePassport,
	}
	if userID != "" {
		condWhere["user_id"] = userID
	}

	if len(source) > 0 {
		condWhere["source"] = source[0]
	}

	// 执行查询
	userData := User{}
	err := db.Where(condWhere).First(&userData).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		helpers.DBLogError(ctx, err)
		go helpers.HelixNotice(ctx, err.Error())

		return userData, err
	}

	return userData, nil
}

// 添加用户
func (u *User) Add(ctx context.Context, userNew User) (User, error) {
	userNew.LoginTime = time.Now()
	userNew.CreatedAt = time.Now()
	userNew.UpdatedAt = time.Now()

	db := resource.Gorm.WithContext(ctx)
	err := db.Create(&userNew).Error
	if err != nil {
		helpers.DBLogError(ctx, err)
		go helpers.HelixNotice(ctx, err.Error())
	}

	return userNew, err
}

// 修改用户数据
func (u *User) Save(ctx context.Context) error {
	db := resource.Gorm.WithContext(ctx)
	err := db.Model(u).Updates(&u).Error
	if err != nil {
		helpers.DBLogError(ctx, err)
		go helpers.HelixNotice(ctx, err.Error())
	}

	return err
}

// 数据转化
func (u *User) SwapData() map[string]any {
	var userData map[string]any
	if u.ID <= 0 {
		return userData
	}

	userData = map[string]any{
		"user_id":      u.ID,
		"real_id":      u.RealID,
		"username":     u.Username,
		"display_name": u.DisplayName,
		"email":        u.Email,
		"phone":        u.Phone,
		"login_time":   u.CreatedAt.Unix(),
		"created_at":   u.CreatedAt.Unix(),
		"updated_at":   u.UpdatedAt.Unix(),
	}
	return userData
}

// GetUserIDsByDomainID 根据用户域ID获取用户ID列表，返回一个字符串切片和错误信息
func (u *User) GetUserIDsByDomainID(ctx context.Context, userDomainID string) ([]string, error) {
	db := resource.Gorm.WithContext(ctx)
	db = db.Where("user_domain_id = ?", userDomainID)
	var (
		users   []User
		userIds []string
	)
	err := db.Find(&users).Error
	if err != nil {
		return userIds, err
	}
	for _, user := range users {
		userIds = append(userIds, user.UserID)
	}
	return userIds, nil
}
