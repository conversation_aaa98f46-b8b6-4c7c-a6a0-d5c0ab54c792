package models

import (
	"context"
	"encoding/json"
	"gorm.io/gorm"
	"time"

	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/library/resource"
)

const (
	ModuleTypeScene = 10
	ModuleTypePower = 20
)

var ModuleTypeMap = map[int]bool{
	ModuleTypeScene: true,
	ModuleTypePower: true,
}

type HomeModule struct {
	ID        uint64    `gorm:"id"`
	AdminId   uint64    `gorm:"admin_id"`
	Type      uint64    `gorm:"type"`
	Status    int64     `gorm:"status"`
	Data      string    `gorm:"data"`
	CreatedAt time.Time `gorm:"created_at"`
	UpdatedAt time.Time `gorm:"updated_at"`
}

func (h *HomeModule) TableName() string {
	return "helix_home_module"
}

func (h *HomeModule) GetByType(ctx context.Context, tType int64) (HomeModule, error) {
	db := resource.Gorm.WithContext(ctx)
	condWhere := map[string]interface{}{
		"type":   tType,
		"status": StatusNor,
	}

	// 执行查询
	HomeModuleData := HomeModule{}
	err := db.Where(condWhere).First(&HomeModuleData).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		helpers.DBLogError(ctx, err)
		go helpers.HelixNotice(ctx, err.Error())

		return HomeModuleData, err
	}
	return HomeModuleData, nil
}

// 获取 HomeModule
func (h *HomeModule) GetAll(ctx context.Context) ([]HomeModule, error) {
	db := resource.Gorm.WithContext(ctx)
	condWhere := map[string]interface{}{
		"status": StatusNor,
	}

	var list []HomeModule
	err := db.Where(condWhere).Find(&list).Error
	if err != nil {
		helpers.DBLogError(ctx, err)
		go helpers.HelixNotice(ctx, err.Error())
	}
	return list, err
}

// SwapData 数据转化
func (h *HomeModule) SwapData() map[string]interface{} {
	var data map[string]interface{}
	if h.ID <= 0 {
		return data
	}

	var content interface{}
	_ = json.Unmarshal([]byte(h.Data), &content)

	data = map[string]interface{}{
		"id":         h.ID,
		"admin_id":   h.AdminId,
		"type":       h.Type,
		"status":     h.Status,
		"data":       content,
		"created_at": h.CreatedAt.Unix(),
		"updated_at": h.UpdatedAt.Unix(),
	}
	return data
}
