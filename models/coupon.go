package models

import (
	"context"
	"encoding/json"
	"time"

	"gorm.io/gorm/clause"

	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/library/resource"
)

const (
	CouponStatusLose = 1

	couponListLimit = 50
)

// 表结构
type Coupon struct {
	ID         uint64    `gorm:"id"`
	UserId     uint64    `gorm:"user_id"`
	AdminId    uint64    `gorm:"admin_id"`
	TaskType   uint64    `gorm:"task_type"`
	RangeList  string    `gorm:"range_list"`
	Amount     float64   `gorm:"amount"`
	RestAmount float64   `gorm:"rest_amount"`
	Status     int64     `gorm:"status"`
	Remark     string    `gorm:"remark"`
	StartTime  time.Time `gorm:"start_time"`
	EndTime    time.Time `gorm:"login_time"`
	CreatedAt  time.Time `gorm:"created_at"`
	UpdatedAt  time.Time `gorm:"updated_at"`
}

type FreezeCoupons struct {
	Coupons []*FreezeCoupon `json:"coupons"`
}

type FreezeCoupon struct {
	CouponID uint64  `json:"coupon_id"`
	Amount   float64 `json:"amount"`
}

func (c *Coupon) TableName() string {
	return "helix_coupon"
}

// GetList 获取优惠券列表
// ctx context.Context 上下文对象
// userId int64 用户ID
// taskType int64 任务类型
// 返回值 []Coupon 优惠券列表，error 错误信息
func (c *Coupon) GetList(ctx context.Context, userId, taskType int64) ([]Coupon, error) {
	db := resource.Gorm.WithContext(ctx)

	// 拼接where
	condWhere := map[string]interface{}{
		"user_id":   userId,
		"task_type": taskType,
		"status":    StatusNor,
	}

	// 执行查询
	var couponList []Coupon
	err := db.Where(condWhere).Limit(couponListLimit).Find(&couponList).Error
	if err != nil {
		helpers.DBLogError(ctx, err)
		go helpers.HelixNotice(ctx, err.Error())

		return couponList, err
	}
	return couponList, nil
}

func (c *Coupon) GetUserList(ctx context.Context, userId int64, limit, page int) ([]Coupon, error) {
	db := resource.Gorm.WithContext(ctx)
	db = db.Where("user_id = ?", userId)
	db = db.Where("status <> ?", StatusDel)

	// 执行查询
	var couponList []Coupon
	offset := limit * (page - 1)
	err := db.Limit(limit).Offset(offset).Find(&couponList).Error
	if err != nil {
		helpers.DBLogError(ctx, err)
		go helpers.HelixNotice(ctx, err.Error())

		return couponList, err
	}
	return couponList, nil
}

func (c *Coupon) Count(ctx context.Context, userId int64) (int64, error) {
	db := resource.Gorm.WithContext(ctx)
	db = db.Where("user_id = ?", userId)
	db = db.Where("status <> ?", StatusDel)

	// 执行查询
	var count int64
	err := db.Model(c).Count(&count).Error
	if err != nil {
		helpers.DBLogError(ctx, err)
		go helpers.HelixNotice(ctx, err.Error())
	}
	return count, err
}

// 修改试用数据
func (c *Coupon) Save(ctx context.Context) error {
	db := resource.Gorm.WithContext(ctx)
	err := db.Model(c).Updates(&c).Error
	if err != nil {
		helpers.DBLogError(ctx, err)
		go helpers.HelixNotice(ctx, err.Error())
	}

	return err
}

// 数据转化
func (c *Coupon) SwapData() map[string]interface{} {
	var couponData map[string]interface{}
	if c.ID <= 0 {
		return couponData
	}

	var rangeList []int64
	_ = json.Unmarshal([]byte(c.RangeList), &rangeList)

	couponData = map[string]interface{}{
		"id":          c.ID,
		"user_id":     c.UserId,
		"admin_id":    c.AdminId,
		"task_type":   c.TaskType,
		"range_list":  rangeList,
		"amount":      c.Amount,
		"rest_amount": c.RestAmount,
		"status":      c.Status,
		"start_time":  SwapFieldUnix(c.StartTime.Unix()),
		"end_time":    SwapFieldUnix(c.EndTime.Unix()),
		"created_at":  c.CreatedAt.Unix(),
		"updated_at":  c.UpdatedAt.Unix(),
	}
	return couponData
}

// QueryAvailableCouponAmountForTask 获取用户指定任务下可用的优惠券列表
func (c *Coupon) QueryAvailableCouponAmountForTask(ctx context.Context, userId int64, taskType int) (float64, error) {
	db := resource.Gorm.WithContext(ctx)
	db = db.Where("user_id = ?", userId)
	db = db.Where("status <> ?", StatusDel).Where("status <> ?", CouponStatusLose)
	db = db.Where("task_type = ?", taskType)
	db.Order(clause.OrderByColumn{Column: clause.Column{Name: "rest_amount"}})
	// 执行查询
	var couponList []*Coupon
	err := db.Find(&couponList).Error
	if err != nil {
		helpers.DBLogError(ctx, err)
		go helpers.HelixNotice(ctx, err.Error())
		return 0, err
	}
	// 计算代金券总额
	now := time.Now().Unix()
	var amount float64
	for _, coupon := range couponList {
		if now < coupon.StartTime.Unix() || now > coupon.EndTime.Unix() {
			continue
		}
		amount += coupon.RestAmount
	}
	return amount, nil
}

// Charge 使用代金券抵扣，将这个服务类的方法到到这是为了利用db事务，原子性查询和更新代金券
func (c *Coupon) Charge(ctx context.Context, userID int64, taskType uint64, cost float64) (string, float64, error) {
	if cost <= 0 {
		return "", 0, nil
	}
	// 设置查询条件
	db := resource.Gorm.WithContext(ctx)
	db = db.Where("user_id = ?", userID)
	db = db.Where("status <> ?", StatusDel).Where("status <> ?", CouponStatusLose)
	db = db.Where("task_type = ?", taskType)
	db.Order(clause.OrderByColumn{Column: clause.Column{Name: "rest_amount"}})
	// 开启事务
	tx := db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()
	// 查询代金券
	var coupons []*Coupon
	err := tx.Find(&coupons).Error
	if err != nil {
		tx.Rollback()
		helpers.DBLogError(ctx, err)
		return "", 0, err
	}
	// 遍历代金券
	now := time.Now().Unix()
	freezeCoupons := &FreezeCoupons{}
	for _, coupon := range coupons {
		if now < coupon.StartTime.Unix() || now > coupon.EndTime.Unix() {
			continue
		}
		freezeCoupon := &FreezeCoupon{}
		freezeCoupon.CouponID = coupon.ID
		if coupon.RestAmount > 0 && coupon.Amount >= coupon.RestAmount {
			if coupon.RestAmount >= cost {
				freezeCoupon.Amount = cost
				coupon.RestAmount -= cost
				cost = 0
				freezeCoupons.Coupons = append(freezeCoupons.Coupons, freezeCoupon)
				break
			} else {
				freezeCoupon.Amount = coupon.RestAmount
				cost -= coupon.RestAmount
				coupon.RestAmount = 0
				freezeCoupons.Coupons = append(freezeCoupons.Coupons, freezeCoupon)
			}
		}
	}
	// 更新优惠券
	for _, coupon := range coupons {
		err = tx.Model(coupon).Select([]string{"rest_amount"}).Updates(&coupon).Error
		if err != nil {
			tx.Rollback()
			helpers.DBLogError(ctx, err)
			return "", 0, err
		}
	}
	// 提交事务
	var freezeCouponsByte []byte
	if len(freezeCoupons.Coupons) > 0 {
		freezeCouponsByte, _ = json.Marshal(freezeCoupons)
	}
	return string(freezeCouponsByte), cost, tx.Commit().Error
}

// Return 归还代金券
func (c *Coupon) Return(ctx context.Context, freezeCouponsStr string) error {
	if len(freezeCouponsStr) <= 0 {
		return nil
	}
	var freezeCoupons FreezeCoupons
	_ = json.Unmarshal([]byte(freezeCouponsStr), &freezeCoupons)
	// 开启事务
	db := resource.Gorm.WithContext(ctx)
	tx := db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()
	// 更新代金券
	idToAmount := make(map[uint64]float64)
	var ids []uint64
	for _, freezeCoupon := range freezeCoupons.Coupons {
		ids = append(ids, freezeCoupon.CouponID)
		idToAmount[freezeCoupon.CouponID] = freezeCoupon.Amount
	}
	var coupons []Coupon
	tx.Where("id IN (?)", ids)
	err := tx.Find(&coupons).Error
	if err != nil {
		tx.Rollback()
		return err
	}
	for _, coupon := range coupons {
		coupon.RestAmount += idToAmount[coupon.ID]
		err = tx.Model(coupon).Updates(&coupon).Error
		if err != nil {
			tx.Rollback()
			return err
		}
	}
	// 提交事务
	return tx.Commit().Error
}
