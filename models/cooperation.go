package models

import (
	"context"
	"time"

	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/library/resource"
)

const (
	CoopType1  = 1  // 其他
	CoopType10 = 10 // 商务合作
	CoopType20 = 20 // 科研项目课题
	CoopType30 = 30 // 教育生态
	CoopType40 = 40 // 媒体

	Industry1  = 1  // 其他
	Industry10 = 10 // 药企
	Industry20 = 20 // CRO
	Industry30 = 30 // 科研机构
	Industry40 = 40 // 高校
	Industry50 = 50 // 医药生物技术公司
	Industry60 = 60 // AI相关领域

	BudgetType10 = 10 // 无预算
	BudgetType20 = 20 // 0-1万
	BudgetType30 = 30 // 1-10万
	BudgetType40 = 40 // 10-100万
	BudgetType50 = 50 // 100万+

	CustomerTypeJG = 10 // 机构
	CustomerTypeGR = 20 // 个人
)

var CoopTypeMap = map[int]string{
	CoopType1:  "其他",
	CoopType10: "商务合作",
	CoopType20: "科研项目课题",
	CoopType30: "教育生态",
	CoopType40: "媒体",
}
var IndustryMap = map[int]string{
	Industry1:  "其他",
	Industry10: "药企",
	Industry20: "CRO",
	Industry30: "科研机构",
	Industry40: "高校",
	Industry50: "医药生物技术公司",
	Industry60: "AI相关领域",
}
var BudgetTypeMap = map[int]string{
	BudgetType10: "无预算",
	BudgetType20: "0-1万",
	BudgetType30: "1-10万",
	BudgetType40: "10-100万",
	BudgetType50: "100万+",
}
var CustomerTypeMap = map[int]string{
	CustomerTypeJG: "机构",
	CustomerTypeGR: "个人",
}

type Coop struct {
	ID           uint64    `gorm:"id"`
	UserId       uint64    `gorm:"user_id"`
	AdminId      uint64    `gorm:"admin_id"`
	CoopType     uint64    `gorm:"coop_type"`
	ProductList  string    `gorm:"product_list"`
	Demand       string    `gorm:"demand"`
	CustomerType uint64    `gorm:"customer_type"`
	Industry     uint64    `gorm:"industry"`
	Company      string    `gorm:"company"`
	BudgetType   uint64    `gorm:"budget_type"`
	Name         string    `gorm:"name"`
	Position     string    `gorm:"position"`
	Phone        string    `gorm:"phone"`
	Email        string    `gorm:"email"`
	Status       int64     `gorm:"status"`
	Remark       string    `gorm:"remark"`
	DealTime     time.Time `gorm:"deal_time"`
	CreatedAt    time.Time `gorm:"created_at"`
	UpdatedAt    time.Time `gorm:"updated_at"`
}

func (c *Coop) TableName() string {
	return "helix_cooperation"
}

func (c *Coop) Add(ctx context.Context, coopNew Coop) (Coop, error) {
	coopNew.CreatedAt = time.Now()

	db := resource.Gorm.WithContext(ctx)
	err := db.Create(&coopNew).Error
	if err != nil {
		helpers.DBLogError(ctx, err)
		go helpers.HelixNotice(ctx, err.Error())
	}

	return coopNew, err
}
