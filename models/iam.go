package models

type LoginUserInfo struct {
	LoginType         string   `json:"loginType"`
	LoginUserID       string   `json:"loginUserId"`
	LoginUsername     string   `json:"loginUsername,omitempty"`
	DisplayName       string   `json:"displayName"`
	Email             string   `json:"email"`
	MobilePhone       string   `json:"mobilePhone,omitempty"`
	BceUserID         string   `json:"bceUserId"`
	BceAccountID      string   `json:"bceAccountId"`
	BceAccountName    string   `json:"bceAccountName"`
	FederationLogin   bool     `json:"federationLogin"`
	FederationType    string   `json:"federationType,omitempty"`
	Federation        any      `json:"federation,omitempty"`
	SessionID         string   `json:"sessionId,omitempty"`
	NeedResetPassword bool     `json:"needResetPassword"`
	MfaEnabled        bool     `json:"mfaEnabled"`
	AccountActivate   bool     `json:"accountActivate"`
	Credential        any      `json:"credential"`
	Roles             []string `json:"roles,omitempty"`
}

type StsCredential struct {
	AccessKeyID     string `json:"accessKeyId"`
	SecretAccessKey string `json:"secretAccessKey"`
	SessionToken    string `json:"sessionToken"`
	CreateTime      string `json:"createTime"`
	Expiration      string `json:"expiration"`
	UserID          string `json:"userId"`
}

type AccessKey struct {
	Access       string `json:"access"`
	Secret       string `json:"secret"`
	Enabled      any    `json:"enabled,omitempty"`
	Downloaded   any    `json:"downloaded,omitempty"`
	Description  string `json:"description,omitempty"`
	CredentialID string `json:"credential_id"`
	ProjectID    string `json:"project_id"`
	UserID       string `json:"user_id"`
	ModifyTime   any    `json:"modify_time,omitempty"`
}

type SessionContext struct {
	SessionID           string        `json:"sessionId"`
	TokenID             string        `json:"tokenId"`
	TokenExpiresAt      int64         `json:"tokenExpiresAt"`
	LoginType           string        `json:"loginType"`
	LoginUserInfo       LoginUserInfo `json:"loginUserInfo"`
	StsCredential       StsCredential `json:"stsCredential"`
	AccessKey           AccessKey     `json:"accessKey"`
	ForwardActions      []string      `json:"forwardActions"`
	RenderCookies       string        `json:"renderCookies,omitempty"`
	ThirdPartySessionID string        `json:"_thirdPartySessionId,omitempty"`
	RequestInfo         any           `json:"_requestInfo,omitempty"`
}
