package models

import (
	"context"
	"gorm.io/gorm"
	"time"

	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/library/resource"
)

const ProteinSearchLimit = 20

// 表结构
type ProteinLib struct {
	ID        uint64    `gorm:"id"`
	Name      string    `gorm:"name"`
	Config    string    `gorm:"config"`
	Md5       string    `gorm:"md5"`
	CreatedAt time.Time `gorm:"created_at"`
}

func (p *ProteinLib) TableName() string {
	return "helix_protein_lib"
}

// 获取示例 md5 值
func (p *ProteinLib) ExampleMd5(pdbStr, pointStr string) string {
	str := pdbStr + pointStr
	return helpers.Md5(str)
}

// 获取序列数据
func (p *ProteinLib) GetByMd5(ctx context.Context, md5 string) (ProteinLib, error) {
	db := resource.Gorm.WithContext(ctx)
	db = db.Where("md5 <> ?", "")

	// 拼接where
	condWhere := map[string]interface{} {
		"md5": md5,
	}

	// 执行查询
	proteinLibData := ProteinLib{}
	err := db.Where(condWhere).First(&proteinLibData).Error
	if err != nil && err != gorm.ErrRecordNotFound{
		helpers.DBLogError(ctx, err)
		go helpers.HelixNotice(ctx, err.Error())
	}

	return proteinLibData, err
}

// 获取序列数据
func (p *ProteinLib) GetList(ctx context.Context, keyword string, limit int) ([]ProteinLib, error) {
	// 获取数据信息
	db := resource.Gorm.WithContext(ctx)
	db = db.Where("name like ?", "%"+ keyword +"%")

	// 执行查询
	var proteinList []ProteinLib
	err := db.Limit(limit).Find(&proteinList).Error
	if err != nil {
		helpers.DBLogError(ctx, err)
		go helpers.HelixNotice(ctx, err.Error())
	}

	return proteinList, err
}

// 添加记录
func (p *ProteinLib) Add(ctx context.Context, proteinLibNew ProteinLib) (ProteinLib, error) {
	proteinLibNew.CreatedAt = time.Now()

	db := resource.Gorm.WithContext(ctx)
	err := db.Create(&proteinLibNew).Error
	if err != nil {
		helpers.DBLogError(ctx, err)
		go helpers.HelixNotice(ctx, err.Error())
	}
	return proteinLibNew, err
}

// 数据转换
func (p *ProteinLib) SwapData() map[string]interface{} {
	var proteinData map[string]interface{}
	if p.ID <= 0 {
		return proteinData
	}

	proteinData = map[string]interface{} {
		"id":         p.ID,
		"name":       p.Name,
		"created_at": p.CreatedAt.Unix(),
	}
	return proteinData
}
