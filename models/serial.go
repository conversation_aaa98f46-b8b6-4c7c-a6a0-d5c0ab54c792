package models

import (
	"context"
	"encoding/json"
	"gorm.io/gorm"
	"time"

	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/library/resource"
)

const (
	SerialFuncTypeTrain         int = 10
	SerialFuncTypeTrainClassify int = 11
	SerialFuncTypeForecast      int = 20
	SerialFuncTypeForecastRNA   int = 21
	SerialFuncTypeForecastCodon int = 22
	SerialFuncTypeForecast3UTR  int = 23
	SerialFuncTypeForecast5UTR  int = 24
	SerialFuncTypePretreat      int = 30
	SerialFuncTypeNoStruct      int = 40

	DataTypeString   int = 10
	DataTypeFile     int = 20
	DataTypeFileMore int = 21
)

// 表结构
type Serial struct {
	ID        uint64    `gorm:"id"`
	AdminId   uint64    `gorm:"admin_id"`
	Type      uint64    `gorm:"type"`
	FuncType  uint64    `gorm:"func_type"`
	DataType  uint64    `gorm:"data_type"`
	Name      string    `gorm:"name"`
	Desc      string    `gorm:"desc"`
	Protein   string    `gorm:"protein"`
	Serial    string    `gorm:"serial"`
	Config    string    `gorm:"config"`
	FileUrl   string    `gorm:"file_url"`
	Status    int64     `gorm:"status"`
	CreatedAt time.Time `gorm:"created_at"`
	UpdatedAt time.Time `gorm:"updated_at"`
}

func (s *Serial) TableName() string {
	return "helix_serial"
}

// 获取序列数据
func (s *Serial) GetList(ctx context.Context, tType, funcType, dataType int64) ([]Serial, error) {
	// 获取数据信息
	db := resource.Gorm.WithContext(ctx)

	condWhere := map[string]any{}
	if funcType != 0 {
		condWhere["func_type"] = funcType
	}

	if dataType != 0 {
		condWhere["data_type"] = dataType

	}
	// 拼接where
	condWhere["type"] = tType
	condWhere["status"] = ShowStatus

	// 执行查询
	var serialList []Serial
	err := db.Where(condWhere).Find(&serialList).Error
	if err != nil {
		helpers.DBLogError(ctx, err)
		go helpers.HelixNotice(ctx, err.Error())
	}

	return serialList, err
}

// 通过serial 获取
func (s *Serial) GetBySerial(ctx context.Context, tType int64, serial string) (Serial, error) {
	db := resource.Gorm.WithContext(ctx)

	// 拼接where
	db = db.Where("(serial like ? OR config like ?)", "%"+serial+"%", "%"+serial+"%")

	// 执行查询
	serialData := Serial{}
	err := db.First(&serialData).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		helpers.DBLogError(ctx, err)
		go helpers.HelixNotice(ctx, err.Error())
		return serialData, err
	}
	return serialData, nil
}

// 数据转换
func (s *Serial) SwapData() map[string]interface{} {
	var serialData map[string]interface{}
	if s.ID <= 0 {
		return serialData
	}

	var conf map[string]interface{}
	_ = json.Unmarshal([]byte(s.Config), &conf)

	serialData = map[string]interface{}{
		"id":         s.ID,
		"admin_id":   s.AdminId,
		"type":       s.Type,
		"func_type":  s.FuncType,
		"data_type":  s.DataType,
		"name":       s.Name,
		"protein":    s.Protein,
		"serial":     s.Serial,
		"config":     conf,
		"file_url":   s.FileUrl,
		"status":     s.Status,
		"created_at": s.CreatedAt.Unix(),
		"updated_at": s.UpdatedAt.Unix(),
	}
	return serialData
}
