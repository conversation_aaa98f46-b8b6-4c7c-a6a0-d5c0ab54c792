package models

var (
	LinearDesignDesignMode = map[string]struct{}{
		"cds":            {},
		"5utr":           {},
		"joint_5utr_cds": {},
	}

	LinearDesignSquenceType = map[string]struct{}{
		"Protein": {},
		"RNA":     {},
	}

	NucleicAcids = map[string]struct{}{
		"A": {},
		"T": {},
		"C": {},
		"G": {},
		"U": {},
	}
	LinearDesignVersionCost = map[string]float64{
		"basic":   2000,
		"plus":    2400,
		"advance": 2500,
	}

	// LinearFold和LinearPartition费用配置
	LinearFoldCost      = 10.0
	LinearPartitionCost = 10.0
)

const (
	Sequence5UTRLengthLimit = 200
	Sequence3UTRLengthLimit = 500
)

type LinearDesignTask struct {
	LinearDesignTaskConf
	Cost             float64 `json:"cost"`
	BillingUnitCount float64 `json:"billing_unit_count"`
}

type LinearDesignTaskConf struct {
	DesignMode       string                `json:"design_mode"` // 必填，可选值: "cds", "5utr", "joint_5utr_cds"
	Name             string                `json:"name"`
	Sequence         string                `json:"sequence"`                // sequence与sequence_file_url之间必需有一个。
	SequenceFileURL  string                `json:"sequence_file_url"`       // sequence与sequence_file_url之间必需有一个。sequence_file_url对应文件的内容的校验规则与sequence一致。
	SequenceType     string                `json:"sequence_type"`           // 必填，"Protein" 或 "RNA"
	Sequence5UTR     string                `json:"sequence_5utr,omitempty"` // 可选（设计模式为cds）。校验规则：仅允许核酸字符（包括，ATUGC），大小写都可，长度<=500
	Sequence3UTR     string                `json:"sequence_3utr,omitempty"` // 可选。校验规则：仅允许核酸字符（包括，ATUGC），大小写都可，长度<=500
	ParamCDS         LinearDesignParamCDS  `json:"param_cds"`               // 必填
	Param5UTR        LinearDesignParam5UTR `json:"param_5utr"`              // 可选
	Cost             float64               `json:"cost"`
	BillingUnitCount float64               `json:"billing_unit_count"`
	Level            int                   `json:"level"`
}

type LinearDesignParamCDS struct {
	CodonTable          string    `json:"codon_table"`                   // 必填，如: "Human", "Yeast", 或 "Custom"
	CodonTableURL       string    `json:"codon_table_url,omitempty"`     // 仅在 CodonTable 为 "Custom" 时必填
	BeamSize            []int     `json:"beam_size"`                     // 必填，允许值: -1 或 >=50
	CaiLambda           []float64 `json:"cai_lambda"`                    // 必填，basic版本固定为[0]
	TieLambda           []float64 `json:"tie_lambda"`                    // 必填，basic版本固定为[0]
	EnzymeCuttingSite   []string  `json:"enzyme_cutting_site,omitempty"` // 可选，核酸字符，每段长度>=4
	LimitStemLength     bool      `json:"limit_stem_length"`             // 必填
	S1                  int       `json:"S1,omitempty"`                  // LimitStemLength=true 时必填，>=30
	S2                  int       `json:"S2,omitempty"`                  // LimitStemLength=true 时必填，>=15
	Relax5Leader        bool      `json:"relax_5_leader"`                // 必填
	Leader5Length       int       `json:"5_leader_length,omitempty"`     // Relax5Leader=true 时必填
	DeleteSlipperySites bool      `json:"delete_splipery_sites"`         // 必填
	MaxGCContent        float64   `json:"max_gc_content,omitempty"`      // 可选，范围 [0.4, 1.0]
}

type LinearDesignParam5UTR struct {
	ExcludeMotifs           string  `json:"exclude_motifs,omitempty"`  // 可选，核酸字符和逗号分隔，每段长度>=3
	UTR5SimilarityThreshold float64 `json:"utr5_similarity_threshold"` // 必填，范围 [0.0, 1.0]
}

// LinearFold 任务配置
type LinearFoldTaskConf struct {
	Name             string            `json:"name"`
	Sequence         string            `json:"sequence"`
	SequenceFileURL  string            `json:"sequence_file_url"`
	NeedFold         bool              `json:"need_fold"`
	NeedPartition    bool              `json:"need_partition"`
	FoldConfig       *LinearFoldConfig `json:"fold_config,omitempty"`
	Cost             float64           `json:"cost"`
	BillingUnitCount float64           `json:"billing_unit_count"`
}

// LinearPartition 任务配置
type LinearPartitionTaskConf struct {
	Name             string                 `json:"name"`
	Sequence         string                 `json:"sequence"`
	SequenceFileURL  string                 `json:"sequence_file_url"`
	NeedFold         bool                   `json:"need_fold"`
	NeedPartition    bool                   `json:"need_partition"`
	PartitionConfig  *LinearPartitionConfig `json:"partition_config,omitempty"`
	Cost             float64                `json:"cost"`
	BillingUnitCount float64                `json:"billing_unit_count"`
}

// LinearFold 配置
type LinearFoldConfig struct {
	BeamSize       int     `json:"beam_size"`
	ZukerScore     float64 `json:"zuker_score,omitempty"`
	UseConstraints bool    `json:"use_constraints,omitempty"`
	ConstraintStr  string  `json:"constraint_str,omitempty"`
}

// LinearPartition 配置
type LinearPartitionConfig struct {
	BeamSize int `json:"beam_size"`
}
