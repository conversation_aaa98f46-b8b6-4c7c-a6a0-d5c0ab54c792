package models

import (
	"context"
	"encoding/json"
	"gorm.io/gorm"
	"strings"
	"time"

	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/library/resource"
)

const (
	ResultSerialLengthLimit  int = 1024
	ResultFormulaLengthLimit int = 256
)

// 表结构
type TaskResult struct {
	ID                   uint64    `gorm:"id"`
	TaskId               uint64    `gorm:"task_id"`
	Serial               string    `gorm:"serial"`
	Formula              string    `gorm:"formula"`
	Caco2Permeability    float64   `gorm:"caco2_permeability"`
	IntestinalAbsorption float64   `gorm:"intestinal_absorption"`
	OralBioavailability  float64   `gorm:"oral_bioavailability"`
	BarrierPermeant      float64   `gorm:"barrier_permeant"`
	ProteinBinding       float64   `gorm:"protein_binding"`
	Hepatotoxicity       float64   `gorm:"hepatotoxicity"`
	OralToxicity         float64   `gorm:"oral_toxicity"`
	LipinskiLaw          int64     `gorm:"lipinski_law"`
	GhoseLaw             int64     `gorm:"ghose_law"`
	Carcinogenicity      float64   `gorm:"carcinogenicity"`
	Detail               string    `gorm:"detail"`
	Pains                string    `gorm:"pains"`
	CreatedAt            time.Time `gorm:"created_at"`
}

type ResultSearchCond struct {
	Caco2Permeability    []string `json:"caco2_permeability"`
	IntestinalAbsorption []string `json:"intestinal_absorption"`
	OralBioavailability  []string `json:"oral_bioavailability"`
	BarrierPermeant      []string `json:"barrier_permeant"`
	ProteinBinding       []string `json:"protein_binding"`
	Hepatotoxicity       []string `json:"hepatotoxicity"`
	OralToxicity         []string `json:"oral_toxicity"`
	LipinskiLaw          []string `json:"lipinski_law"`
	GhoseLaw             []string `json:"ghose_law"`
	Carcinogenicity      []string `json:"carcinogenicity"`
}

func (r *TaskResult) TableName() string {
	return "helix_task_result"
}

// 添加数据
func (r *TaskResult) Add(ctx context.Context, resultNew TaskResult) (TaskResult, error) {
	db := resource.Gorm.WithContext(ctx)

	resultNew.CreatedAt = time.Now()
	err := db.Create(&resultNew).Error
	if err != nil {
		helpers.DBLogError(ctx, err)
		go helpers.HelixNotice(ctx, err.Error())
	}

	return resultNew, err
}

// 修改数据
func (r *TaskResult) Save(ctx context.Context) error {
	db := resource.Gorm.WithContext(ctx)
	err := db.Model(r).Updates(&r).Error
	if err != nil {
		helpers.DBLogError(ctx, err)
		go helpers.HelixNotice(ctx, err.Error())
	}

	return err
}

// 通过ids获取数据
func (r *TaskResult) GetListByIds(ctx context.Context, ids []int64) ([]TaskResult, error) {
	// 获取数据信息
	db := resource.Gorm.WithContext(ctx)

	// 拼接where
	condWhere := map[string]interface{}{
		"id": ids,
	}

	// 执行查询
	var TaskResultList []TaskResult
	err := db.Where(condWhere).Order("id asc").Find(&TaskResultList).Error
	if err != nil {
		helpers.DBLogError(ctx, err)
		go helpers.HelixNotice(ctx, err.Error())
	}
	return TaskResultList, err
}

// 通过条件获取数据
func (r *TaskResult) GetListByCond(ctx context.Context, taskId int64, cond ResultSearchCond, limit, page int, orderBy ...string) ([]TaskResult, error) {
	// 获取数据信息
	db := resource.Gorm.WithContext(ctx)
	db = resultBuildWhere(db, cond)

	condWhere := map[string]interface{}{
		"task_id": taskId,
	}

	// 排序处理
	order := "id desc"
	if len(orderBy) > 0 {
		order = orderBy[0]
	}

	// 执行查询
	var resDataList []TaskResult
	offset := limit * (page - 1)
	err := db.Where(condWhere).Limit(limit).Offset(offset).Order(order).Find(&resDataList).Error
	if err != nil {
		helpers.DBLogError(ctx, err)
		go helpers.HelixNotice(ctx, err.Error())
	}

	return resDataList, err
}

// 统计数据
func (r *TaskResult) CountByCond(ctx context.Context, taskId int64, cond ResultSearchCond) (int64, error) {
	// 获取数据信息
	db := resource.Gorm.WithContext(ctx)
	db = resultBuildWhere(db, cond)

	condWhere := map[string]interface{}{
		"task_id": taskId,
	}

	// 执行查询
	var count int64
	err := db.Model(r).Where(condWhere).Count(&count).Error
	if err != nil {
		helpers.DBLogError(ctx, err)
		go helpers.HelixNotice(ctx, err.Error())
	}

	return count, err
}

// 数据转化
func (r *TaskResult) SwapData() map[string]interface{} {
	var res map[string]interface{}
	if r.ID <= 0 {
		return res
	}

	var detail [][]interface{}
	_ = json.Unmarshal([]byte(r.Detail), &detail)
	var pains []string
	_ = json.Unmarshal([]byte(r.Pains), &pains)

	res = map[string]interface{}{
		"id":      r.ID,
		"task_id": r.TaskId,
		"serial":  r.Serial,
		"formula": r.Formula,
		"detail":  detail,
		"pains":   pains,
	}
	return res
}

// where build
func resultBuildWhere(db *gorm.DB, cond ResultSearchCond) *gorm.DB {
	caco2Permeability := strings.Join(cond.Caco2Permeability, ",")
	switch caco2Permeability {
	case "1":
		db = db.Where("caco2_permeability > ?", 0.5)
	case "2":
		db = db.Where("caco2_permeability <= ?", 0.5)
	}

	intestinalAbsorption := strings.Join(cond.IntestinalAbsorption, ",")
	switch intestinalAbsorption {
	case "1":
		db = db.Where("intestinal_absorption < ?", 30)
	case "2":
		db = db.Where("intestinal_absorption >= ?", 30).Where("intestinal_absorption <= ?", 80)
	case "3":
		db = db.Where("intestinal_absorption > ?", 80)
	case "1,2":
		db = db.Where("intestinal_absorption < ?", 80)
	case "1,3":
		db = db.Where("intestinal_absorption < ?", 30).Or("intestinal_absorption > ?", 80)
	case "2,3":
		db = db.Where("intestinal_absorption > ?", 30)
	}

	// 兼容老数据
	oralBioavailability := strings.Join(cond.OralBioavailability, ",")
	switch oralBioavailability {
	case "1":
		db = db.Where("oral_bioavailability < ?", 30)
	case "2":
		db = db.Where("oral_bioavailability >= ?", 30).Where("oral_bioavailability <= ?", 80)
	case "3":
		db = db.Where("oral_bioavailability > ?", 80)
	case "1,2":
		db = db.Where("oral_bioavailability < ?", 80)
	case "1,3":
		db = db.Where("oral_bioavailability < ?", 30).Or("oral_bioavailability > ?", 80)
	case "2,3":
		db = db.Where("oral_bioavailability > ?", 30)
	case "4":
		db = db.Where("oral_bioavailability > ?", 0.5)
	case "5":
		db = db.Where("oral_bioavailability <= ?", 0.5)
	}

	barrierPermeant := strings.Join(cond.BarrierPermeant, ",")
	switch barrierPermeant {
	case "1":
		db = db.Where("barrier_permeant > ?", 0.5)
	case "2":
		db = db.Where("barrier_permeant <= ?", 0.5)
	}

	proteinBinding := strings.Join(cond.ProteinBinding, ",")
	switch proteinBinding {
	case "1":
		db = db.Where("protein_binding > ?", 85)
	case "2":
		db = db.Where("protein_binding <= ?", 85)
	}

	hepatotoxicity := strings.Join(cond.Hepatotoxicity, ",")
	switch hepatotoxicity {
	case "1":
		db = db.Where("hepatotoxicity > ?", 0.5)
	case "2":
		db = db.Where("hepatotoxicity <= ?", 0.5)
	}

	// 兼容老数据
	oralToxicity := strings.Join(cond.OralToxicity, ",")
	switch oralToxicity {
	case "1":
		db = db.Where("oral_toxicity < ?", 50)
	case "2":
		db = db.Where("oral_toxicity >= ?", 50).Where("oral_toxicity <= ?", 500)
	case "3":
		db = db.Where("oral_toxicity > ?", 500)
	case "1,2":
		db = db.Where("oral_toxicity < ?", 500)
	case "1,3":
		db = db.Where("oral_toxicity < ?", 50).Or("oral_toxicity > ?", 500)
	case "2,3":
		db = db.Where("oral_toxicity > ?", 50)
	case "4":
		db = db.Where("oral_toxicity > ?", 0.5)
	case "5":
		db = db.Where("oral_toxicity <= ?", 0.5)
	}

	lipinskiLaw := strings.Join(cond.LipinskiLaw, ",")
	switch lipinskiLaw {
	case "1":
		db = db.Where("lipinski_law = ?", 0)
	case "2":
		db = db.Where("lipinski_law = ?", 1)
	case "3":
		db = db.Where("lipinski_law >= ?", 2)
	case "1,2":
		db = db.Where("lipinski_law in (?)", []int{0, 1})
	case "1,3":
		db = db.Where("lipinski_law in (?)", []int{0, 2})
	case "2,3":
		db = db.Where("lipinski_law in (?)", []int{1, 2})
	}

	ghoseLaw := strings.Join(cond.GhoseLaw, ",")
	switch ghoseLaw {
	case "1":
		db = db.Where("ghose_law = ?", 0)
	case "2":
		db = db.Where("ghose_law = ?", 1)
	case "3":
		db = db.Where("ghose_law >= ?", 2)
	case "1,2":
		db = db.Where("ghose_law in (?)", []int{0, 1})
	case "1,3":
		db = db.Where("ghose_law in (?)", []int{0, 2})
	case "2,3":
		db = db.Where("ghose_law in (?)", []int{1, 2})
	}

	carcinogenicity := strings.Join(cond.Carcinogenicity, ",")
	switch carcinogenicity {
	case "1":
		db = db.Where("carcinogenicity > ?", 0.5)
	case "2":
		db = db.Where("carcinogenicity <= ?", 0.5)
	}
	return db
}
