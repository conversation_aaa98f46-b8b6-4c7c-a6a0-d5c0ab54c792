package models

import (
	"context"
	"gorm.io/gorm"
	"time"

	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/library/resource"
)

type Subscribe struct {
	ID        uint64    `gorm:"id"`
	Email     string    `gorm:"email"`
	Content   string    `gorm:"content"`
	Status    int64     `gorm:"status"`
	CreatedAt time.Time `gorm:"created_at"`
	UpdatedAt time.Time `gorm:"updated_at"`
}

func (s *Subscribe) TableName() string {
	return "helix_subscribe_log"
}

type ContentConf struct {
	News   int64 `json:"news"`
	Module int64 `json:"module"`
}

// 通过 email 获取
func (s *Subscribe) GetByEmail(ctx context.Context, email string) (Subscribe, error) {
	db := resource.Gorm.WithContext(ctx)

	// 拼接where
	condWhere := map[string]interface{} {
		"email": email,
		"status": StatusNor,
	}

	// 执行查询
	subscribeData := Subscribe{}
	err := db.Where(condWhere).First(&subscribeData).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		helpers.DBLogError(ctx, err)
		go helpers.HelixNotice(ctx, err.Error())
		return subscribeData, err
	}
	return subscribeData, nil
}

// 添加
func (s *Subscribe) Add(ctx context.Context, subscribeNew Subscribe) (Subscribe, error) {
	subscribeNew.CreatedAt = time.Now()
	subscribeNew.UpdatedAt = time.Now()
	subscribeNew.Status = int64(StatusNor)

	db := resource.Gorm.WithContext(ctx)
	err := db.Create(&subscribeNew).Error
	if err != nil {
		helpers.DBLogError(ctx, err)
		go helpers.HelixNotice(ctx, err.Error())
	}

	return subscribeNew, err
}

// 修改
func (s *Subscribe) Save(ctx context.Context) error {
	db := resource.Gorm.WithContext(ctx)
	err := db.Model(s).Updates(&s).Error
	if err != nil {
		helpers.DBLogError(ctx, err)
		go helpers.HelixNotice(ctx, err.Error())
	}

	return err
}


