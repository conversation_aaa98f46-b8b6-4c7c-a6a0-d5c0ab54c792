package models

import (
	"context"
	"gorm.io/gorm"
	"strings"
	"time"

	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/library/resource"
)

const DrugSearchLimit = 10

// 表结构
type DrugLib struct {
	ID        uint64    `gorm:"id"`
	Cid       string    `gorm:"cid"`
	Name      string    `gorm:"name"`
	Smiles    string    `gorm:"smiles"`
	CreatedAt time.Time `gorm:"created_at"`
}

func (d *DrugLib) TableName() string {
	return "helix_drug_lib"
}

// GetList 获取序列数据
func (d *DrugLib) GetList(ctx context.Context, keyword string, limit int) ([]DrugLib, error) {
	// 获取数据信息
	db := resource.Gorm.WithContext(ctx)
	db = db.Where("(name like ? OR cid like ?)", "%"+keyword+"%", "%"+keyword+"%")

	// 执行查询
	var drugList []DrugLib
	err := db.Limit(limit).Find(&drugList).Error
	if err != nil {
		helpers.DBLogError(ctx, err)
		go helpers.HelixNotice(ctx, err.Error())
	}

	return drugList, err
}

// 添加记录
func (d *DrugLib) Add(ctx context.Context, DrugLibNew DrugLib) (DrugLib, error) {
	DrugLibNew.CreatedAt = time.Now()

	db := resource.Gorm.WithContext(ctx)
	err := db.Create(&DrugLibNew).Error
	if err != nil {
		helpers.DBLogError(ctx, err)
		go helpers.HelixNotice(ctx, err.Error())
	}
	return DrugLibNew, err
}

// 获取序列数据
func (d *DrugLib) GetByCid(ctx context.Context, cid string) (DrugLib, error) {
	db := resource.Gorm.WithContext(ctx)

	// 拼接where
	db = db.Where("cid = ?", cid)

	// 执行查询
	var drugInfo DrugLib
	err := db.First(&drugInfo).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		helpers.DBLogError(ctx, err)
		go helpers.HelixNotice(ctx, err.Error())
	}

	return drugInfo, err
}

// 获取序列数据
func (d *DrugLib) BatchGetByName(ctx context.Context, nameList []string) ([]DrugLib, error) {
	db := resource.Gorm.WithContext(ctx)

	// 拼接where
	db = db.Where("name in (?)", nameList)

	// 执行查询
	var drugList []DrugLib
	err := db.Find(&drugList).Error
	if err != nil {
		helpers.DBLogError(ctx, err)
		go helpers.HelixNotice(ctx, err.Error())
	}

	return drugList, err
}

// 获取序列数据
func (d *DrugLib) BatchGetByCid(ctx context.Context, cidList []string) ([]DrugLib, error) {
	db := resource.Gorm.WithContext(ctx)

	// 拼接where
	db = db.Where("cid in (?)", cidList)

	// 执行查询
	var drugList []DrugLib
	err := db.Find(&drugList).Error
	if err != nil {
		helpers.DBLogError(ctx, err)
		go helpers.HelixNotice(ctx, err.Error())
	}

	return drugList, err
}

// 数据转换
func (d *DrugLib) SwapData() map[string]interface{} {
	var drugData map[string]interface{}
	if d.ID <= 0 {
		return drugData
	}

	drugData = map[string]interface{}{
		"id":     d.ID,
		"cid":    strings.ToUpper(d.Cid),
		"name":   strings.ToUpper(d.Name),
		"smiles": d.Smiles,
	}
	return drugData
}
