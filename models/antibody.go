package models

const (
	AntibodyChainFunctionHchain  = "Hchain"
	AntibodyChainFunctionLchain  = "Lchain"
	AntibodyChainFunctionAntigen = "Antigen"
)

type AntibodyDesignTask struct {
	JobName      string                `json:"job_name,omitempty"`
	DesignNum    int                   `json:"design_num,omitempty"`
	DesignMode   string                `json:"design_mode,omitempty"`
	Diverse      bool                  `json:"diverse,omitempty"`
	Reference    string                `json:"reference,omitempty"`
	DesignChains []AntibodyDesignChain `json:"design_chains,omitempty"`
}

type ChainInfo struct {
	ChainID           string   `json:"chain_ID"`
	Length            int      `json:"length"`
	Function          string   `json:"function"`
	HlPair            string   `json:"hl_pair"`
	CdrComplete       bool     `json:"cdr_complete"`
	LengthFill        int      `json:"length_fill"`
	MissingFv         int      `json:"missing_fv"`
	MissingFc         int      `json:"missing_fc"`
	MissingAll        int      `json:"missing_all"`
	DesignableIndices []string `json:"designable_indices"`
}
