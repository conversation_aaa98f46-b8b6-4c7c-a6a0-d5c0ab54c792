# passport 配置

[TplConfig]
[TplConfig.paddlehelix]
Tpl = "paddlehelix"

[TplConfig.paddlehelix.Passgate]
ServicerName = "passgate"

[TplConfig.paddlehelix.Session]
ServicerName = "session"
AppID = 3000

# otp鉴权配置
[TplConfig.paddlehelix.Otp]
KeyVersion   = 1
TokenVersion = 1
Key  = "zmNBbcWy6h8TUkLG2PHPUUTe6vtukSyQFCGlX9clmMMEH0OLrS1IKoNPqywauWlL"
ApID = 3000

# 用于生成头像url的签名，找passport申请
# http://passport.sys.baidu.com/docs/new/ppui-book/open/portrait/
[TplConfig.paddlehelix.Portrait]
KeyVersion = "1"                           # 字符串版本号
Key = "3226ceb052a225fd7f26ff7a1ff52cb6"   # 32字节的秘钥
IV  = "f09728e122a317e0"                   # 16字节的向量