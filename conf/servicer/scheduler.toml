Name = "scheduler"

# 连接超时，
ConnTimeOut = 1000
# 写数据超时
WriteTimeOut = 2000
# 读数据超时
ReadTimeOut = 2000
# 请求失败后的重试次数：总请求次数 = Retry + 1
Retry = 2

# 资源使用策略，非必选，默认使用 RoundRobin
# RoundRobin: 依次轮询
[Strategy]
Name="RoundRobin"

# 资源定位：BNS 配置，支持智能 BNS
#[Resource.BNS]
#BNSName = ""
#EnableSmartBNS = false
#UsePort = "newc"

# 资源定位：手动配置 - 使用IP、端口
[Resource.Manual]
[[Resource.Manual.default]]
Host = "*************"
Port = 8199
