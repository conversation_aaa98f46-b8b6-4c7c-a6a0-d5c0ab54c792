// This APP Use GDP V2

package main

import (
	"context"
	"flag"
	"log"
	"os"

	_ "icode.baidu.com/baidu/gdp/automaxprocs"
	"icode.baidu.com/baidu/gdp/env"
	"icode.baidu.com/helix_web/bootstrap"
	"icode.baidu.com/helix_web/jobs"
)

// 命令行输入参数处理
var config = flag.String("conf", "./conf/app.toml", "app config file")

// main main 是程序的入口函数，负责解析命令行参数、初始化配置、启动应用程序、执行job任务和监控等。
// 如果发生错误，则使用log.Fatalln输出并退出程序。
func main() {
	flag.Parse()
	config, err := bootstrap.ParserAppConfig(*config)
	if err != nil {
		log.Fatalln(err)
	}
	env.Default = config.Env

	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	bootstrap.MustInit(ctx)
	app := bootstrap.NewApp(ctx, config)

	// 执行job
	go jobs.SyncTask(ctx)
	go jobs.SyncTaskAA(ctx)
	go jobs.SyncPretreat(ctx)
	go jobs.SubmitTask(ctx)

	// 监控
	go bootstrap.Profiling(ctx)

	// 退出处理
	exitCode := app.StartWithGracefulShutdown()
	os.Exit(exitCode)
}
