<style type="text/css">
  td {
    padding: 0 60px;
  }
  tr {
    height: 0;
  }
  a:hover {
    color: #2b3ee5 !important;
  }
</style>

<table
  style="
    width: 100%;
    max-width: 768px;
    position: relative;
    left: 0;
    right: 0;
    margin: 0 auto;
    border-spacing: 0;
    border-collapse: collapse;
    color: #333;
    font-family: PingFangSC-Regular;
    background-image: url(https://easydl-download.bj.bcebos.com/helix_public/wave.png);
    background-size: contain;
    background-repeat: no-repeat;
    background-color: #f8fcff;
  "
>
  <tbody>
    <tr>
      <td style="padding-top: 60px; padding-bottom: 72px">
        <img
          height="38"
          width="280"
          src="https://easydl-download.bj.bcebos.com/helix_public/logo.png"
        />
      </td>
    </tr>
    <tr>
      <td
        style="
          padding-bottom: 20px;
          font-size: 24px;
          color: #020735;
          line-height: 30px;
        "
      >
        <span>Hi:</span>
      </td>
    </tr>

    <tr>
      <td
        style="
          padding-bottom: 60px;
          font-size: 18px;
          color: #020735;
          line-height: 30px;
        "
      >
        <span>You have successfully subscribed to PaddleHelix Newletter.</span>
      </td>
    </tr>
    <tr>
      <td
        style="
          padding-bottom: 60px;
          font-size: 14px;
          color: #323865;
          line-height: 26px;
          position: relative;
        "
      >
        <span
          style="
            position: absolute;
            background-image: url(https://easydl-download.bj.bcebos.com/helix_public/quote.png);
            width: 40px;
            height: 40px;
            background-repeat: no-repeat;
            z-index: -1;
            background-size: cover;
            top: -20px;
          "
        ></span>
        <span>
          &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
We hope you have been enjoying the content we have shared with you.
PaddleHelix is a platform built on Baidu deep learning framework PaddlePaddle. We provides robust AI capabilities that address the needs in areas including drug discovery, vaccine design and precision medicine. We look forwards to sharing more updates and news of PaddleHelix with you in the future.</span
        >
      </td>
    </tr>
  </tbody>
</table>
<table
  style="
    width: 100%;
    color: #777a9d;
    max-width: 768px;
    position: relative;
    left: 0;
    right: 0;
    margin: 0 auto;
    text-align: center;
    border-collapse: collapse;
    border-spacing: 0;
    font-size: 12px;
    line-height: 18px;
    font-family: PingFangSC-Regular;
    background-image: url(https://easydl-download.bj.bcebos.com/helix_public/background.png);
    background-size: contain;
    background-repeat: no-repeat;
  "
>
  <tbody style="margin: 0 60px">
  <tr>
    <td style="padding-top: 28px; padding-bottom: 4px">
      Please do not reply to this email directly
    </td>
  </tr>
  <tr>
    <td style="padding-bottom: 16px">
      If you have any questions or advices, please contact us via
      <a style="color: #777a9d" href="mailto:<EMAIL>"
      ><EMAIL></a
      >
    </td>
  </tr>
  <tr>
    <td style="padding-bottom: 48px">
      <a
              href="https://paddlehelix.baidu.com?type=unsubscribe&email=xxxemail&lang=en"
              target="blank"
              style="color: #777a9d"
      > Manage your subscription </a
      >&nbsp;&nbsp;&nbsp;<a
              href="https://paddlehelix.baidu.com/app/duty"
              target="blank"
              style="color: #777a9d"
      > User Agreement </a
      >&nbsp;&nbsp;&nbsp;<a
              href="https://www.baidu.com/duty/"
              target="blank"
              style="color: #777a9d"
      > Disclaimer </a
      >
    </td>
  </tr>

  <tr>
    <td
            style="
          font-size: 24px;
          color: #c1cfdb;
          text-align: center;
          line-height: 34px;
          padding-bottom: 12px;
        "
    >
      <span>PaddleHelix</span>
    </td>
  </tr>
  <tr>
    <td
            style="
          position: relative;
          font-size: 14px;
          color: #c1cfdb;
          letter-spacing: 3.4px;
          line-height: 18px;
          letter-spacing: 3.4px;
          padding-bottom: 40px;
        "
    >
        <span
                style="
            display: block;
            height: 1px;
            width: 48px;
            position: absolute;
            background-image: linear-gradient(
              270deg,
              #c1cfdb 0%,
              rgba(220, 231, 241, 0) 100%
            );
            top: 9px;
            left: 200px;
          "
        ></span>
      <span
              style="
            display: block;
            height: 1px;
            width: 48px;
            position: absolute;
            background-image: linear-gradient(
              270deg,
              rgba(220, 231, 241, 0) 0%,
              #c1cfdb 100%
            );
            top: 9px;
            right: 204px;
          "
      ></span>
      <span>An AI + computational biology platform built on Baidu PaddlePaddle</span>
    </td>
  </tr>
  </tbody>
</table>
