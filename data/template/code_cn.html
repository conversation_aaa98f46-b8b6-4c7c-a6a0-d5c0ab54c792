<style type="text/css">
  td {
    padding: 0 60px;
  }
  tr {
    height: 0;
  }
  a:hover {
    color: #2b3ee5 !important;
  }
</style>

<table
  style="
    width: 100%;
    max-width: 768px;
    position: relative;
    left: 0;
    right: 0;
    margin: 0 auto;
    border-spacing: 0;
    border-collapse: collapse;
    color: #333;
    font-family: PingFangSC-Regular;
    background-image: url(https://easydl-download.bj.bcebos.com/helix_public/wave.png);
    background-size: cover;
    background-repeat: no-repeat;
    background-color: #f8fcff;
  "
>
  <tbody>
    <tr>
      <td style="padding-top: 60px; padding-bottom: 72px">
        <img
          height="38"
          width="280"
          src="https://easydl-download.bj.bcebos.com/helix_public/logo.png"
        />
      </td>
    </tr>
    <tr>
      <td
        style="
          padding-bottom: 20px;
          font-size: 24px;
          color: #020735;
          line-height: 30px;
        "
      >
        <span>尊敬的PaddleHelix用户:</span>
      </td>
    </tr>

    <tr>
      <td
        style="
          padding-bottom: 20px;
          font-size: 18px;
          color: #020735;
          line-height: 30px;
        "
      >
        <span>您好！感谢您订阅螺旋桨PaddleHelix的最新动态。您的验证码为：</span>
      </td>
    </tr>
    <tr>
      <td
        style="
          padding-bottom: 60px;
          font-size: 14px;
          color: #323865;
          line-height: 26px;
          position: relative;
        "
      >
        <span
          style="
            font-family: Helvetica-Bold;
            font-size: 32px;
            color: #020735;
            letter-spacing: 1px;
            line-height: 40px;
          "
        >
        code-code
        </span>
        <span
          style="
            opacity: 0.6;
            font-size: 14px;
            color: #555b8d;
            line-height: 26px;
          "
          >（请在1小时内完成验证）</span
        >
      </td>
    </tr>
  </tbody>
</table>
<table
  style="
    width: 100%;
    color: #777a9d;
    max-width: 768px;
    position: relative;
    left: 0;
    right: 0;
    margin: 0 auto;
    text-align: center;
    border-collapse: collapse;
    border-spacing: 0;
    font-size: 12px;
    line-height: 18px;
    font-family: PingFangSC-Regular;
    background-image: url(https://easydl-download.bj.bcebos.com/helix_public/background.png);
    background-size: contain;
    background-repeat: no-repeat;
  "
>
  <tbody style="margin: 0 60px">
    <tr>
      <td style="padding-top: 28px; padding-bottom: 4px">
        此为系统邮件，请勿回复
      </td>
    </tr>
    <tr>
      <td style="padding-bottom: 16px">
        如果您有任何咨询或建议，请发送邮件至
        <a style="color: #777a9d" href="mailto:<EMAIL>"
          ><EMAIL></a
        >
        联系我们。
      </td>
    </tr>
    <tr>
      <td style="padding-bottom: 48px">
        <a
          href="https://paddlehelix.baidu.com?type=unsubscribe&email=xxxemail&lang=cn"
          target="blank"
          style="color: #777a9d"
          >《管理您的订阅选项》</a
        >&nbsp;&nbsp;&nbsp;<a
          href="https://paddlehelix.baidu.com/app/duty"
          target="blank"
          style="color: #777a9d"
          >《用户协议》</a
        >&nbsp;&nbsp;&nbsp;<a
          href="https://www.baidu.com/duty/"
          target="blank"
          style="color: #777a9d"
          >《免责声明》</a
        >
      </td>
    </tr>

    <tr>
      <td
        style="
          font-size: 24px;
          color: #c1cfdb;
          text-align: center;
          line-height: 34px;
          padding-bottom: 12px;
        "
      >
        <span>螺旋桨PaddleHelix团队</span>
      </td>
    </tr>
    <tr>
      <td
        style="
          position: relative;
          font-size: 14px;
          color: #c1cfdb;
          letter-spacing: 3.4px;
          line-height: 18px;
          letter-spacing: 3.4px;
          padding-bottom: 40px;
        "
      >
        <span
          style="
            display: block;
            height: 1px;
            width: 48px;
            position: absolute;
            background-image: linear-gradient(
              270deg,
              #c1cfdb 0%,
              rgba(220, 231, 241, 0) 100%
            );
            top: 9px;
            left: 200px;
          "
        ></span>
        <span
          style="
            display: block;
            height: 1px;
            width: 48px;
            position: absolute;
            background-image: linear-gradient(
              270deg,
              rgba(220, 231, 241, 0) 0%,
              #c1cfdb 100%
            );
            top: 9px;
            right: 204px;
          "
        ></span>
        <span>基于百度飞桨的AI+生物计算平台</span>
      </td>
    </tr>
  </tbody>
</table>
