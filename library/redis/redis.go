package redis

import (
	"context"
	"errors"
	"time"

	"github.com/go-redis/redis/v8"
	"github.com/google/uuid"
	gredis "icode.baidu.com/baidu/gdp/redis"

	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/library/resource"
)

const (
	MaxLockRetries   = 10 // 获取分布式锁的最大尝试次数
	MaxUnLockRetries = 10 // 释放分布式锁的最大尝试次数
	RetryDelay       = 1  // 获取分布式锁失败的等待时间，单位是秒

	UserLimitTime   = 3600
	SerialLimitTime = 86400

	CaptchaLimitTime = 600

	TaskEmailLimitTime = 259200 // 3 days

	SubscribeEmailTime = 86400 // 1 day

	TaskUserNumTime     = 86400 * 1000 // 1000 天
	TaskUserNumLockTime = 3            // 3s
	UserCommentLockTime = 1            // 1s

	UserBalanceLockPrefix        = "user_balance_lock_"
	UserBalanceLockExpireSeconds = 3
)

const (
	CaptchaPrefix = "captcha_"

	UserMessagePrefix = "usermsg_"

	TaskEmailPrefix = "task_email_"

	UserPopupPrefix = "user_popup_"

	UserHelixFold3TaskNumPrefix = "user_helixfold3_task_num_"
	UserHelixFold3TaskNumLock   = "user_helixfold3_task_lock_"
	UserTaskNumPrefix           = "user_task_num_"
	UserTaskNumLock             = "user_task_lock_"
	UserCookiePrefix            = "token_"
	UserEmailPrefix             = "sub_email_"
	UserPortraitBoxPrefix       = "user_box_"

	UserHelixFold3UsedPrefix = "user_helixfold3_used_"
	UserAwardPrefix          = "user_award_"
	UserPortraitLock         = "user_portrait_lock_"
	UserCommentLock          = "user_comment_lock_"

	PassportUserPrefix = "pass_user_"
	PassportUserTime   = 180

	IAMUserPrefix = "iam_user_"
	IAMUserTime   = 3600
)

// 获取缓存
func Get(ctx context.Context, key string) string {
	redisClient := resource.RedisClient

	resStr, err := redisClient.Get(ctx, key).Result()
	if err != nil && err != redis.Nil {
		go resource.LoggerService.Error(ctx, err.Error())
		go helpers.HelixNotice(ctx, "---redis error:"+err.Error())
		return ""
	}

	return resStr
}

// 写入缓存(expire 单位为 s)
func Set(ctx context.Context, key string, value interface{}, expire int) bool {
	redisClient := resource.RedisClient

	expiration := time.Second * time.Duration(expire)
	_, err := redisClient.Set(ctx, key, value, expiration).Result()
	if err != nil {
		go resource.LoggerService.Error(ctx, err.Error())
		go helpers.HelixNotice(ctx, "---redis error:"+err.Error())
	}

	return true
}

// 锁
func SetNX(ctx context.Context, key string, value interface{}, expire int) bool {
	redisClient := resource.RedisClient

	expiration := time.Second * time.Duration(expire)
	res, err := redisClient.SetNX(ctx, key, value, expiration).Result()
	if err != nil {
		go resource.LoggerService.Error(ctx, err.Error())
		go helpers.HelixNotice(ctx, "---redis error:"+err.Error())
	}

	return res
}

// 自增
func Incr(ctx context.Context, key string) bool {
	redisClient := resource.RedisClient

	_, err := redisClient.Incr(ctx, key).Result()
	if err != nil {
		go resource.LoggerService.Error(ctx, err.Error())
		go helpers.HelixNotice(ctx, "---redis error:"+err.Error())
	}

	return true
}

// 自增
func IncrBy(ctx context.Context, key string, value int64) bool {
	redisClient := resource.RedisClient

	_, err := redisClient.IncrBy(ctx, key, value).Result()
	if err != nil {
		go resource.LoggerService.Error(ctx, err.Error())
		go helpers.HelixNotice(ctx, "---redis error:"+err.Error())
	}

	return true
}

// 删除缓存
func Del(ctx context.Context, key string) bool {
	redisClient := resource.RedisClient
	_, err := redisClient.Del(ctx, key).Result()
	if err != nil {
		go resource.LoggerService.Error(ctx, err.Error())
		go helpers.HelixNotice(ctx, "---redis error:"+err.Error())
	}

	return true
}

// 像集合中添加数据
func SAdd(ctx context.Context, key string, value interface{}) bool {
	redisClient := resource.RedisClient
	_, err := redisClient.SAdd(ctx, key, value).Result()
	if err != nil {
		go resource.LoggerService.Error(ctx, err.Error())
		go helpers.HelixNotice(ctx, "---redis error:"+err.Error())
	}

	return true
}

// 获取集合中数据
func SMembers(ctx context.Context, key string) []string {
	redisClient := resource.RedisClient

	strSlice, err := redisClient.SMembers(ctx, key).Result()
	if err != nil && err != redis.Nil {
		go resource.LoggerService.Error(ctx, err.Error())
		go helpers.HelixNotice(ctx, "---redis error:"+err.Error())

		return []string{}
	}

	return strSlice
}

// 向有序集合中添加数据
func ZAdd(ctx context.Context, key string, score float64, value interface{}) bool {
	redisClient := resource.RedisClient

	members := &redis.Z{
		Score:  score,
		Member: value,
	}
	_, err := redisClient.ZAdd(ctx, key, members).Result()
	if err != nil {
		go resource.LoggerService.Error(ctx, err.Error())
		go helpers.HelixNotice(ctx, "---redis error:"+err.Error())
	}

	return true
}

// 获取有序集合中数据
func ZRangeWithScores(ctx context.Context, key string, start, stop int64) []redis.Z {
	redisClient := resource.RedisClient

	zSlice, err := redisClient.ZRangeWithScores(ctx, key, start, stop).Result()
	if err != nil && err != redis.Nil {
		go resource.LoggerService.Error(ctx, err.Error())
		go helpers.HelixNotice(ctx, "---redis error:"+err.Error())

		return []redis.Z{}
	}

	return zSlice
}

// Lock 获取分布式锁
func Lock(ctx context.Context, lockKey string, expire int) (string, bool) {
	lockValue := uuid.New().String()
	// 循环获取锁
	for attempt := 1; attempt <= MaxLockRetries; attempt++ {
		// 使用NX确保键不存在时才设置
		res := SetNX(ctx, lockKey, lockValue, expire)
		if !res && attempt >= MaxLockRetries {
			return "", false
		}
		if res {
			// 成功获取锁
			return lockValue, true
		}
		// 等待1s后重试
		select {
		case <-time.After(RetryDelay * time.Second):
			continue
		case <-ctx.Done():
			return "", false
		}
	}
	return "", false
}

// UnLock 释放分布式锁
func UnLock(ctx context.Context, lockKey string, lockValue string) error {
	redisClient := resource.RedisClient
	// 定义命令执行管道
	pipe := func(p gredis.Pipeliner) error {
		// 查看客户端有没有权限释放锁
		val, err := p.Get(ctx, lockKey).Result()
		if err != nil && !errors.Is(err, redis.Nil) {
			return err
		}
		if errors.Is(err, redis.Nil) || val != lockValue {
			return nil
		}
		// 释放锁
		_, err = p.Del(ctx, lockKey).Result()
		return err
	}
	// 循环释放锁
	var err error
	for attempt := 1; attempt <= MaxUnLockRetries; attempt++ {
		err = redisClient.TxPipelined(ctx, pipe)
		if err == nil {
			return nil
		}
		// 等待1s后重试
		select {
		case <-time.After(RetryDelay * time.Second):
			continue
		case <-ctx.Done():
			return nil
		}
	}
	return err
}
