package ctxutils

import (
	"context"
	"icode.baidu.com/helix_web/models"
)

// GetIAMUserID 获取登陆用户IAM ID
func GetIAMUserID(ctx context.Context) string {
	userInfo := ctx.Value("user_info").(models.UserInfo)
	return userInfo.IAMUserID
}

// GetIAMUserDomainID 获取登陆用户IAM AccountID
func GetIAMUserDomainID(ctx context.Context) string {
	userInfo := ctx.Value("user_info").(models.UserInfo)
	return userInfo.IAMUserDomainID
}
