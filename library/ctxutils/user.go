package ctxutils

import (
	"context"

	"icode.baidu.com/helix_web/models"
)

// GetUserInfo 获取登陆用户信息
func GetUserInfo(ctx context.Context) models.UserInfo {
	return ctx.Value("user_info").(models.UserInfo)
}

// GetUserID 获取登陆用户ID
func GetUserID(ctx context.Context) int64 {
	return GetUserInfo(ctx).UserID
}

// GetGitHubUserRealId 获取github用户信息，不是github登录用户则返回0
func GetGitHubUserRealId(ctx context.Context) int64 {
	userInfo := ctx.Value("user_info").(models.UserInfo)
	if userInfo.Source != int64(models.SourceGitHub) {
		return 0
	}
	return userInfo.RealID
}

// GetBillingUnitCount 获取计费单元的数量
func GetBillingUnitCount(ctx context.Context) float64 {
	return ctx.Value("billing_unit_count").(float64)
}

// GetIsTrial 获取是否是试用
func GetIsTrial(ctx context.Context) bool {
	isTrial := ctx.Value(models.IsTrialKey)
	if isTrial == nil {
		return false
	}
	return isTrial.(bool)
}

// 获取试用ID
func GetTrialID(ctx context.Context) uint64 {
	trialID := ctx.Value(models.TrialIDKey)
	if trialID == nil {
		return 0
	}
	return trialID.(uint64)
}
