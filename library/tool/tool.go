package tool

import (
	"encoding/json"
	"errors"
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"sync"

	"icode.baidu.com/baidu/gdp/conf"
	"icode.baidu.com/baidu/gdp/env"
	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/models"
)

const (
	antibodyDesignCheckerPath = "./tool/antibody_design_checker/main.py"
	hF3RefCheckerPath         = "./tool/hf3_ref_checker/main.py"

	pythonEnvChecker = "checker"
)

var (
	once   sync.Once
	config Config
)

type Hf3RefCheckParam struct {
	CifPath          string `json:"cif_path"`
	ReferChainID     string `json:"refer_chain_id"`
	Sequence         string `json:"sequence"`
	HitPdbCode       string `json:"hit_pdb_code"`
	KalignBinaryPath string `json:"kalign_binary_path"`
}

type AntibodyDesignCheckParam = models.AntibodyDesignTask

type Config struct {
	PythonEnvPath string
}

func initConf() {
	once.Do(func() {
		err := conf.Parse(env.ConfDir()+"/tool.toml", &config)
		if err != nil {
			return
		}
	})
}

func Hf3RefCheck(param Hf3RefCheckParam) error {
	initConf()
	param.KalignBinaryPath = fmt.Sprintf("%s/%s/bin/kalign", config.PythonEnvPath, pythonEnvChecker)
	paramJson, _ := json.Marshal(param)
	cmd := exec.Command(fmt.Sprintf("%s/%s/bin/python", config.PythonEnvPath, pythonEnvChecker),
		hF3RefCheckerPath, "--input_json", string(paramJson))
	if _, err := cmd.CombinedOutput(); err != nil {
		return errors.New("指定实体与参考链序列不符，请确保序列相似性 ≥90%，且长度 ≥6残基。")
	}
	return nil
}

func AntibodyDesignCheck(param AntibodyDesignCheckParam) (map[string]models.ChainInfo, error) {
	initConf()
	// 将参数写入临时文件
	paramJson, _ := json.Marshal(param)
	fileName := helpers.GetRandomString(10) + "_intput.json"
	tempDir := os.TempDir()
	filePath := filepath.Join(tempDir, fileName)
	tempFile, err := os.Create(filePath)
	if err != nil {
		return nil, fmt.Errorf("create temp file failed: %w", err)
	}
	defer os.Remove(tempFile.Name())
	if _, err = tempFile.Write(paramJson); err != nil {
		return nil, fmt.Errorf("write temp file failed: %w", err)
	}

	outputFile := helpers.GetRandomString(10) + "_output.json"
	defer func() {
		_ = exec.Command("rm", outputFile).Run()
	}()
	cmd := exec.Command(fmt.Sprintf("%s/%s/bin/python", config.PythonEnvPath, pythonEnvChecker),
		antibodyDesignCheckerPath, "--input_json", tempFile.Name(), "--chain_save", outputFile)
	output, err := cmd.CombinedOutput()
	if err != nil {
		return nil, errors.New(string(output))
	}
	data, err := os.ReadFile(outputFile)
	if err != nil {
		return nil, err
	}
	var resp map[string]models.ChainInfo
	if err := json.Unmarshal(data, &resp); err != nil {
		return nil, err
	}
	return resp, nil
}
