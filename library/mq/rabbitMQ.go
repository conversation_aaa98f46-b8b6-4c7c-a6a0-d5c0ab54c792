package mq

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"

	"github.com/streadway/amqp"
	"icode.baidu.com/baidu/gdp/conf"
	"icode.baidu.com/baidu/gdp/env"
	"icode.baidu.com/baidu/gdp/logit"
	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/library/resource"
)

const (
	SyncTaskResultQueue = "task-result"
	SubmitTaskQueue     = "task-submit"

	SyncPretreatResultQueue = "pretreat-result"
)

// 消息队列结构体
type RabbitMQ struct {
	conn   *amqp.Connection // 持有连接对象
	mqConf *mqConfig
}

// 配置信息
type mqConfig struct {
	Host     string
	UserAuth string
}

// 初始化配置
func GetInstance() (*RabbitMQ, error) {
	var mqConf mqConfig
	if err := conf.Parse(env.ConfDir()+"/rabbitmq.toml", &mqConf); err != nil {
		return nil, errors.New("rabbitmq.toml conf err " + err.Error())
	}
	if len(mqConf.Host) <= 0 || len(mqConf.UserAuth) <= 0 {
		return nil, errors.New("rabbitmq.toml conf few")
	}

	dialStr := fmt.Sprintf("amqp://%s@%s", mqConf.UserAuth, mqConf.Host)
	conn, _ := amqp.Dial(dialStr)

	instance := &RabbitMQ{
		conn:   conn,
		mqConf: &mqConf,
	}
	return instance, nil
}

// 发送消息
func (mq *RabbitMQ) SendMessage(ctx context.Context, queue string, message interface{}) error {
	ch, err := mq.conn.Channel()
	if err != nil {
		return err
	}
	defer ch.Close()

	_, err = ch.QueueDeclare(queue,true,false,false, false,nil)
	if err != nil {
		return err
	}

	body, _ := json.Marshal(message)
	err = ch.Publish(
		"",    // exchange
		queue, // routing key
		false, // mandatory
		false, // immediate
		amqp.Publishing{
			DeliveryMode: amqp.Persistent,
			ContentType:  "text/plain",
			Body:         body,
		})
	if err != nil {
		return err
	}

	// 打印日志
	logFields := []logit.Field{
		logit.String("queue", queue),
		logit.String("host", mq.mqConf.Host),
	}
	resource.LoggerService.Notice(ctx, string(body), logFields...)
	return nil
}

// 消费消息
func (mq *RabbitMQ) Consumer(ctx context.Context, queue string, fn func(context.Context, []byte) bool) error {
	ch, err := mq.conn.Channel()
	if err != nil {
		return err
	}
	defer ch.Close()

	_, err = ch.QueueDeclare(queue,true,false,false, false,nil)
	if err != nil {
		return err
	}

	msgList, err := ch.Consume(
		queue,             // queue
		"",     // consumer
		false,  // auto-ack
		false,  // exclusive
		false,  // no-local
		false,  // no-wait
		nil,    // args
	)
	if err != nil {
		return err
	}

	logFields := []logit.Field{
		logit.String("queue", queue),
		logit.String("host", mq.mqConf.Host),
	}
	for d := range msgList {
		logit.AddMetaFields(ctx,
			logit.LogIDField(logit.NewLogID()),
		)

		resource.LoggerService.Notice(ctx, string(d.Body), logFields...)
		res := fn(ctx, d.Body)
		if res {
			go helpers.HelixNotice(ctx, "--- consumer success ---"+string(d.Body))

			resource.LoggerService.Notice(ctx, "----- consumer success ------")
			_ = d.Ack(false)
		}
	}
	return nil
}

// 关闭conn
func (mq *RabbitMQ) Close() {
	_ = mq.conn.Close()
}
