package chpc

import (
	"sync"

	"icode.baidu.com/baidu/gdp/conf"
	"icode.baidu.com/baidu/gdp/env"
)

var (
	once sync.Once
)

type CHPClient struct {
	AK                 string
	SK                 string
	Endpoint           string
	Host               string
	TestHost           string
	Port               string
	JobSubmitPath      string
	JobSubmitPathDebug string
}

var client *CHPClient

// NewCHPClient 新建一个CHPClient实例，并返回指向该实例的指针
// 如果初始化失败，则返回nil
func NewCHPClient() *CHPClient {
	once.Do(func() {
		err := conf.Parse(env.ConfDir()+"/chpc.toml", &client)
		if err != nil {
			return
		}
		client.Endpoint = client.Host
		if env.RunMode() != "release" {
			client.Endpoint = client.TestHost
		}
		if client.Port != "" {
			client.Endpoint = client.Endpoint + ":" + client.Port
		}
	})
	return client
}
