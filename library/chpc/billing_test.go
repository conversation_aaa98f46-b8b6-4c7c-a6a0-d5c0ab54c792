package chpc

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"testing"

	bcehttp "github.com/baidubce/bce-sdk-go/http"
)

func Test_queryIAMUserCoupons(t *testing.T) {
	req := &bcehttp.Request{}
	ctx := context.Background()
	AssembleStsRequest(ctx, req, "c48dacc1901c49d7adb976bab511f710", "POST", "coupon.bce-internal.baidu.com:8661", "/v3/coupon/order/available/coupons")
	message := &QueryCouponReq{
		AccountID:   "c48dacc1901c49d7adb976bab511f710",
		ProductType: "CHPC",
		Region:      "global",
	}
	messageJson, _ := json.Marshal(message)
	req.SetBody(ioutil.NopCloser(bytes.NewReader(messageJson)))
	req.SetLength(int64(len(messageJson)))

	resp, err := bcehttp.Execute(req)
	if err != nil {
		fmt.Println(err)
		return
	}
	defer resp.Body().Close()

	body, err := ioutil.ReadAll(resp.Body())
	if err != nil {
		fmt.Println(err)
		return
	}
	financeCouponResp := QueryCouponResp{}
	_ = json.Unmarshal(body, &financeCouponResp)
	totalAmount := 0.0
	for _, coupon := range financeCouponResp.Coupons {
		totalAmount += coupon.Balance
		fmt.Println(coupon)
	}
	fmt.Println(totalAmount)
}

func Test_queryIAMUserCash(t *testing.T) {
	req := &bcehttp.Request{}
	ctx := context.Background()
	req.SetParam("accountId", "c48dacc1901c49d7adb976bab511f710")
	AssembleStsRequest(ctx, req, "c48dacc1901c49d7adb976bab511f710", "GET", billingConfig.FinanceEndpoint, billingConfig.URI)

	resp, err := bcehttp.Execute(req)
	if err != nil {
		fmt.Println(err)
		return
	}
	defer resp.Body().Close()

	body, err := ioutil.ReadAll(resp.Body())
	if err != nil {
		fmt.Println(err)
		return
	}
	financeResp := FinanceResp{}
	_ = json.Unmarshal(body, &financeResp)
	fmt.Println(financeResp.TotalAmount)
}

func Test_createOrder(t *testing.T) {
	req := &bcehttp.Request{}
	ctx := context.Background()
	req.SetParam("accountId", "1545b9ab1a0f46b2bf5ebf32461987fe")
	AssembleStsRequest(ctx, req, "1545b9ab1a0f46b2bf5ebf32461987fe", "POST", "orderfacade.bce-console.sdns.baidu.com", "/v1/order")

	resp, err := bcehttp.Execute(req)
	if err != nil {
		fmt.Println(err)
		return
	}
	defer resp.Body().Close()

	body, err := ioutil.ReadAll(resp.Body())
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Println(string(body))
}
