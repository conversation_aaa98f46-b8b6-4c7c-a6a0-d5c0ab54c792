package chpc

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"io/ioutil"
	"math"
	"strconv"
	"time"

	bcehttp "github.com/baidubce/bce-sdk-go/http"

	"icode.baidu.com/baidu/gdp/conf"
	"icode.baidu.com/baidu/gdp/env"
	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/library/redis"
)

var (
	FreezeBalanceLockPrefix        = "freeze_balance_lock_"
	FreezeBalanceLockExpireSeconds = 1
	FreezeBalancePrefix            = "freeze_balance_"
	FreezeBalanceExpireSeconds     = 24 * 60 * 60

	billingConfig *BillingConfig
)

type BillingConfig struct {
	FinanceEndpoint  string
	CouponEndpoint   string
	URI              string
	FinanceStatusURI string
	CouponURI        string
}

func init() {
	err := conf.Parse(env.ConfDir()+"/billing.toml", &billingConfig)
	if err != nil {
		panic(err)
	}
}

type FinanceResp struct {
	Cash        float64 `json:"cash"`        // 现金余额
	Rebate      float64 `json:"rebate"`      // 返点余额
	BaihuiCoin  float64 `json:"baihuiCoin"`  // 可忽略
	TotalAmount float64 `json:"totalAmount"` // 余额总数
}

type FinanceStatusResp struct {
	Cash             float64 `json:"cash"`
	Rebate           float64 `json:"rebate"`
	Debt             float64 `json:"debt"`
	AvailableBalance float64 `json:"availableBalance"`
}

type FinanceCoupon struct {
	Balance         float64   `json:"balance"`
	TotalAmount     float64   `json:"totalAmount"`
	SuitedPayMethod string    `json:"suitedPayMethod"`
	ProductName     string    `json:"productName"`
	CreateTime      time.Time `json:"createTime"`
	BeginTime       time.Time `json:"beginTime"`
	ProductID       int       `json:"productId"`
	ProductTypes    []string  `json:"productTypes"`
}

type QueryCouponReq struct {
	AccountID      string  `json:"accountId"`
	ProductType    string  `json:"productType"`
	OrderItemPrice float64 `json:"orderItemPrice"`
	Region         string  `json:"region"`
	TotalPrice     float64 `json:"totalPrice"`
}

type QueryCouponResp struct {
	Coupons []*FinanceCoupon `json:"coupons"`
}

// queryIAMUserCoupons 查询指定用户的 IAM 优惠券，返回一个 float64 类型的数值，表示总金额。如果出现错误则返回 error
func queryIAMUserCoupons(ctx context.Context, accountID string) (float64, error) {
	if len(accountID) <= 0 {
		return 0, nil
	}

	req := &bcehttp.Request{}
	AssembleStsRequest(ctx, req, accountID, "POST", billingConfig.CouponEndpoint, billingConfig.CouponURI)
	message := &QueryCouponReq{
		AccountID:   accountID,
		ProductType: "CHPC",
		Region:      "global",
	}
	messageJson, _ := json.Marshal(message)
	req.SetBody(ioutil.NopCloser(bytes.NewReader(messageJson)))
	req.SetLength(int64(len(messageJson)))

	resp, err := bcehttp.Execute(req)
	if err != nil {
		helpers.LogError(ctx, err)
		go helpers.HelixNotice(ctx, err.Error())
		return 0, err
	}
	defer resp.Body().Close()

	body, err := ioutil.ReadAll(resp.Body())
	if err != nil {
		helpers.LogError(ctx, err)
		return 0, err
	}
	financeCouponResp := QueryCouponResp{}
	_ = json.Unmarshal(body, &financeCouponResp)
	totalAmount := 0.0
	for _, coupon := range financeCouponResp.Coupons {
		totalAmount += coupon.Balance
	}
	return totalAmount, nil
}

// queryIAMUserCash 查询指定用户的现金余额，参数为上下文、账号ID，返回值为float64和error
func queryIAMUserCash(ctx context.Context, accountID string) (float64, error) {
	if len(accountID) <= 0 {
		return 0, nil
	}

	req := &bcehttp.Request{}
	req.SetParam("accountId", accountID)
	AssembleStsRequest(ctx, req, accountID, "GET", billingConfig.FinanceEndpoint, billingConfig.URI)

	resp, err := bcehttp.Execute(req)
	if err != nil {
		helpers.LogError(ctx, err)
		go helpers.HelixNotice(ctx, err.Error())
		return 0, err
	}
	defer resp.Body().Close()

	body, err := ioutil.ReadAll(resp.Body())
	if err != nil {
		helpers.LogError(ctx, err)
		return 0, err
	}
	financeResp := FinanceResp{}
	_ = json.Unmarshal(body, &financeResp)
	return financeResp.TotalAmount, nil
}

// QueryIAMUserBalance 查询IAM用户余额，返回一个浮点数类型的结果，如果出错则返回nil和error类型的错误信息
func QueryIAMUserBalance(ctx context.Context, accountID string) (float64, error) {
	if len(accountID) <= 0 {
		return 0, nil
	}

	cash, err := queryIAMUserCash(ctx, accountID)
	if err != nil {
		return 0, err
	}
	coupons, err := queryIAMUserCoupons(ctx, accountID)
	if err != nil {
		return 0, err
	}
	return cash + coupons, nil
}

// QueryAvailableBalance 查询可用余额，包括IAM用户余额和冻结余额的总和
// ctx context.Context: 上下文信息，包含请求信息等
// accountID string: IAM用户ID，不能为空字符串
func QueryAvailableBalance(ctx context.Context, accountID string) (float64, error) {
	if len(accountID) <= 0 {
		return 0, errors.New("iam account id is empty")
	}
	amount, err := QueryIAMUserBalance(ctx, accountID)
	if err != nil {
		return 0, err
	}
	// 获取用户冻结余额
	freezeBalanceKey := FreezeBalancePrefix + accountID
	freezeBalanceStr := redis.Get(ctx, freezeBalanceKey)
	var freezeBalance float64
	if len(freezeBalanceStr) > 0 {
		freezeBalance, err = strconv.ParseFloat(freezeBalanceStr, 64)
		if err != nil {
			return 0, err
		}
	}
	return math.Max(amount-freezeBalance, 0), nil
}

// Charge Charge 是一个函数，用于对账号进行冻结金额的操作
// ctx: 上下文信息，context.Context类型
// accountID: 账号ID，string类型，不能为空字符串
// cost: 需要冻结的金额，float64类型，大于等于0
// 返回值：error类型，表示错误信息，如果成功则返回nil
func Charge(ctx context.Context, accountID string, cost float64) error {
	if len(accountID) <= 0 {
		return errors.New("iam account id is empty")
	}
	if cost <= 0 {
		return nil
	}
	// 获取分布式锁
	lockKey := FreezeBalanceLockPrefix + accountID
	lockValue, res := redis.Lock(ctx, lockKey, FreezeBalanceLockExpireSeconds)
	if !res {
		return errors.New("get distributed lock failure")
	}
	defer func() {
		_ = redis.UnLock(ctx, lockKey, lockValue)
	}()
	// 获取用户余额
	amount, err := QueryIAMUserBalance(ctx, accountID)
	if err != nil {
		return err
	}
	// 获取用户冻结余额
	freezeBalanceKey := FreezeBalancePrefix + accountID
	freezeBalanceStr := redis.Get(ctx, freezeBalanceKey)
	var freezeBalance float64
	if len(freezeBalanceStr) > 0 {
		freezeBalance, err = strconv.ParseFloat(freezeBalanceStr, 64)
		if err != nil {
			return err
		}
	}
	// 计算用户实际余额
	amount = amount - freezeBalance
	if amount < cost {
		return errors.New("balance not enough")
	}
	// 更新冻结余额
	freezeBalance += cost
	res = redis.Set(ctx, freezeBalanceKey, freezeBalance, FreezeBalanceExpireSeconds)
	if !res {
		return errors.New("redis update freeze balance error")
	}
	return nil
}

// Return Return 函数返回一个error类型，如果cost小于等于0或者accountID长度小于等于0则返回nil，否则进行以下操作：
// 1. 获取分布式锁，防止并发操作；
// 2. 获取用户冻结余额，如果存在则更新用户冻结余额，并设置过期时间；
// 3. 如果用户冻结余额不足，直接返回nil。
func Return(ctx context.Context, accountID string, cost float64) error {
	if cost <= 0 || len(accountID) <= 0 {
		return nil
	}
	// 获取分布式锁
	lockKey := FreezeBalanceLockPrefix + accountID
	lockValue, res := redis.Lock(ctx, lockKey, FreezeBalanceLockExpireSeconds)
	if !res {
		return errors.New("get distributed lock failure")
	}
	defer func() {
		_ = redis.UnLock(ctx, lockKey, lockValue)
	}()
	// 获取用户冻结余额
	freezeBalanceKey := FreezeBalancePrefix + accountID
	freezeBalanceStr := redis.Get(ctx, freezeBalanceKey)
	if len(freezeBalanceStr) > 0 {
		freezeBalance, err := strconv.ParseFloat(freezeBalanceStr, 64)
		if err != nil {
			return err
		}
		if freezeBalance < cost {
			return nil
		}
		// 更新用户冻结余额
		freezeBalance -= cost
		res = redis.Set(ctx, freezeBalanceKey, freezeBalance, FreezeBalanceExpireSeconds)
		if !res {
			return errors.New("redis update freeze balance error")
		}
	}
	return nil
}

// QueryIAMUserIsArrears 查询IAM用户是否欠费，参数为上下文、账号ID，返回值为布尔类型（是否欠费）和错误类型
func QueryIAMUserIsArrears(ctx context.Context, accountID string) (bool, error) {
	if len(accountID) <= 0 {
		return false, nil
	}

	req := &bcehttp.Request{}
	req.SetParam("accountId", accountID)
	AssembleStsRequest(ctx, req, accountID, "GET", billingConfig.FinanceEndpoint, billingConfig.FinanceStatusURI)

	resp, err := bcehttp.Execute(req)
	if err != nil {
		helpers.LogError(ctx, err)
		go helpers.HelixNotice(ctx, err.Error())
		return false, err
	}
	defer resp.Body().Close()

	body, err := ioutil.ReadAll(resp.Body())
	if err != nil {
		helpers.LogError(ctx, err)
		return false, err
	}
	financeStatusResp := FinanceStatusResp{}
	_ = json.Unmarshal(body, &financeStatusResp)
	return financeStatusResp.Debt > 0.0, nil
}
