package algorithm

import (
	"strconv"
	"sync"

	"google.golang.org/grpc"

	"icode.baidu.com/baidu/gdp/conf"
	"icode.baidu.com/baidu/gdp/env"
	pb "icode.baidu.com/helix_web/proto/generate/proto"
)

var (
	once         sync.Once
	config       Config
	client       *grpc.ClientConn
	serialClient pb.SerialServiceClient
)

type Config struct {
	IP       string
	Port     int
	EndPoint string
}

// initGrpcClient 初始化grpc客户端，只会执行一次
// 参数：无
// 返回值：无
func initGrpcClient() {
	once.Do(func() {
		err := conf.Parse(env.ConfDir()+"/algorithm.toml", &config)
		if err != nil {
			return
		}
		config.EndPoint = config.IP + ":" + strconv.Itoa(config.Port)
		client, err = grpc.Dial(config.EndPoint, grpc.WithInsecure())
		if err != nil {
			return
		}
		serialClient = pb.NewSerialServiceClient(client)
	})
}

// GetSerialServiceClient 获取SerialServiceClient实例，用于与SerialService进行gRPC通信
// 如果serialClient为nil，则初始化grpcClient并返回serialClient
func GetSerialServiceClient() pb.SerialServiceClient {
	initGrpcClient()
	return serialClient
}
