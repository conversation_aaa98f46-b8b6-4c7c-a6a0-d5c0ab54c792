package bce

import (
	"errors"
	"io/ioutil"
	"strconv"
	"strings"

	"github.com/baidubce/bce-sdk-go/services/bos"
	"github.com/baidubce/bce-sdk-go/services/sts"
	"icode.baidu.com/baidu/gdp/conf"
	"icode.baidu.com/baidu/gdp/env"
	"icode.baidu.com/helix_web/helpers"
)

const (
	ObjectUrlExpireTime = 300  // 300s
)

type bosConfig struct {
	AK        string
	SK        string
	Endpoint  string
	DefaultBucket string
}
var bosConf *bosConfig

// 初始化函数
func initConf() error {
	if err := conf.Parse(env.ConfDir() + "/bos.toml", &bosConf); err != nil {
		return errors.New("bos.toml conf err "+ err.Error())
	}

	if len(bosConf.AK) <= 0 || len(bosConf.SK) <= 0 || len(bosConf.Endpoint) <= 0 {
		return errors.New("bos.toml conf param few")
	}
	if len(bosConf.DefaultBucket) <= 0 {
		return errors.New("bos.toml defaultBucket empty")
	}
	return nil
}

// 获取上传 token
func GetSessionToken(userId int64) (map[string]interface{}, error){
	var resp map[string]interface{}
	err := initConf()
	if err != nil {
		return resp, err
	}

	// 创建STS服务的Client对象，Endpoint使用默认值
	stsClient, err := sts.NewClient(bosConf.AK, bosConf.SK)
	if err != nil {
		return resp, err
	}

	// 上传路径
	uploadPath := "/helix_upload/" + strconv.Itoa(int(userId)) +"/"

	aclStr := "{\"accessControlList\":[{\"resource\":[\"bucket-bucket*\"],\"permission\":[\"PutObject\"],\"effect\":\"Allow\",\"service\":\"bce:bos\",\"region\":\"bj\"}]}"
	aclStr = strings.Replace(aclStr, "bucket-bucket", bosConf.DefaultBucket + uploadPath, 1)
	stsResult, err := stsClient.GetSessionToken(120, aclStr)
	if err != nil {
		return resp, err
	}

	resp = map[string]interface{} {
		"temp_ak": strings.ToLower(helpers.GetRandomString(6)) + stsResult.AccessKeyId,
		"temp_sk": strings.ToLower(helpers.GetRandomString(6)) + stsResult.SecretAccessKey,
		"session_token": helpers.GetRandomString(6) + stsResult.SessionToken,
		"endpoint": bosConf.Endpoint,
		"upload_path": uploadPath,
		"bucket": bosConf.DefaultBucket,
	}
	return resp, nil
}

// 获取文件内容
func GetObject(bucket, object string) (string, error){
	err := initConf()
	if err != nil {
		return "", err
	}

	// 处理bucket
	if len(bucket) == 0 {
		bucket = bosConf.DefaultBucket
	}

	bosClient, err := bos.NewClient(bosConf.AK, bosConf.SK, bosConf.Endpoint)
	if err != nil {
		return "", err
	}

	objectRes, err := bosClient.BasicGetObject(bucket, object)
	if err != nil {
		return "", err
	}

	bodyData, err := ioutil.ReadAll(objectRes.Body)
	if err != nil {
		return "", err
	}
	defer objectRes.Body.Close()

	return string(bodyData), nil
}

// 获取访问链接
func GenerateObjectUrl(bucket, object string, expireInSeconds int) (string, error){
	err := initConf()
	if err != nil {
		return "", err
	}
	if bucket != "easydl-download" && bucket != "bml-test-test" {
		return "", errors.New("bucket error")
	}

	bosClient, err := bos.NewClient(bosConf.AK, bosConf.SK, bosConf.Endpoint)
	if err != nil {
		return "", err
	}

	objectUrl := bosClient.BasicGeneratePresignedUrl(bucket, object, expireInSeconds)
	objectUrl = strings.Replace(objectUrl, "http:", "https:", 1)

	return objectUrl, nil
}
