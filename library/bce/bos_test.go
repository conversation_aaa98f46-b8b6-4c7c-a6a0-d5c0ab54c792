package bce

import (
	"testing"
)

func TestGetSessionToken(t *testing.T) {
	_, err := GetSessionToken(1)
	if err != nil {
		t.<PERSON><PERSON><PERSON>("%s", err.Error())
	}
}

func TestGetObject(t *testing.T) {
	tests := []struct {
		input1 string
		input2 string
		output int
	}{
		{"", "xxx", 0},
		{"bml-test-test", "output/233/virtual_screening_infer_result_to_display_20210914145311.json", 10},
	}

	for _, test := range tests {
		got, _ := GetObject(test.input1, test.input2)
		if len(got) < test.output {
			t.Errorf("want(%d), got(%d)", test.output, len(got))
		}
	}
}

func TestGenerateObjectUrl(t *testing.T) {
	tests := []struct {
		input1 string
		input2 string
		input3 int
	}{
		{"bml-test-test", "", 10},
		{"bml-test-test", "output/233/virtual_screening_infer_result_to_display_20210914145311.json", 10},
	}

	for _, test := range tests {
		_, err := GenerateObjectUrl(test.input1, test.input2, test.input3)
		if err != nil {
			t.Errorf("%s", err.Error())
		}
	}
}