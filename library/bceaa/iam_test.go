package bceaa

import (
	"fmt"
	"testing"
)

func TestIamGetUserInfo(t *testing.T) {
	InitIAMConf()
	accountID := "b734b6dda2824c0391a2e95951e57e8c"
	mapping, err := iamGetUserInfo(accountID)
	if err != nil {
		t.<PERSON><PERSON>("iamGetUserInfo failed: %v", err)
	}
	if mapping.IAMAccountID != accountID {
		t.<PERSON><PERSON><PERSON>("Expected IAMAccountID %s, got %s", accountID, mapping.IAMAccountID)
	}
	if mapping.PassportID == 0 {
		t.<PERSON><PERSON><PERSON>("Expected PassportID to be non-zero")
	}
	if mapping.UCID == 0 {
		t.<PERSON><PERSON><PERSON>("Expected UCID to be non-zero")
	}
	fmt.Println(mapping)
}
