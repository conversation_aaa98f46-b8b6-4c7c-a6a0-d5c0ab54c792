package bceaa

import (
	"testing"
)

// TestGetSessionToken 测试函数，用于获取会话令牌
// 参数：t *testing.T - 类型为*testing.T的指针，表示测试对象
func TestGetSessionToken(t *testing.T) {
	_, err := GetSessionToken(1)
	if err != nil {
		t.<PERSON><PERSON><PERSON>("%s", err.Error())
	}
}

// TestGetObject 测试函数，用于获取对象信息
// 参数t：*testing.T类型，表示单元测试的上下文，不可为空
// 返回值int：表示测试结果，0表示通过，非零表示失败
func TestGetObject(t *testing.T) {
	tests := []struct {
		input1 string
		input2 string
		output int
	}{
		{"", "xxx", 0},
		{"bml-test-test", "output/233/virtual_screening_infer_result_to_display_20210914145311.json", 10},
	}

	for _, test := range tests {
		got, _ := GetObject(test.input1, test.input2)
		if len(got) < test.output {
			t.Errorf("want(%d), got(%d)", test.output, len(got))
		}
	}
}

// TestGenerateObjectUrl 测试函数GenerateObjectUrl，用于生成对象URL。
// 参数t *testing.T：表示单元测试的结构体指针，用于记录测试过程中发生的错误。
// 返回值类型为空，但会在发生错误时调用t.Errorf()来输出错误信息。
func TestGenerateObjectUrl(t *testing.T) {
	tests := []struct {
		input1 string
		input2 string
		input3 int
	}{
		{"bml-test-test", "", 10},
		{"bml-test-test", "output/233/virtual_screening_infer_result_to_display_20210914145311.json", 10},
	}

	for _, test := range tests {
		_, err := GenerateObjectURL(test.input1, test.input2, test.input3)
		if err != nil {
			t.Errorf("%s", err.Error())
		}
	}
}
