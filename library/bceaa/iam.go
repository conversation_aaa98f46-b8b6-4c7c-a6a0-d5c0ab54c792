package bceaa

import (
	"net/http"
	"strconv"
	"strings"

	iamhttp "icode.baidu.com/baidu/bce-iam/sdk-go/http"
	"icode.baidu.com/baidu/bce-iam/sdk-go/iam"
)

type iamConfig struct {
	Endpoint string
	UserName string
	Password string
}

type IAMAccountNameResponse struct {
	LoginName   string `json:"loginName"`
	AccountName string `json:"account_name"`
}

type PassportIAMAccountMapping struct {
	DisplayName  string
	PassportID   int64
	IAMAccountID string
	UCID         int64
}

var iamBceClientConfiguration iam.BceClientConfiguration

func InitIAMConf() error {
	// var iamConf iamConfig
	// if err := conf.Parse(env.ConfDir()+"/iam.toml", &iamConf); err != nil {
	// 	return errors.New("iam.toml conf err " + err.Error())
	// }

	// if len(iamConf.Endpoint) <= 0 || len(iamConf.UserName) <= 0 || len(iamConf.Password) <= 0 {
	// 	return errors.New("iam.toml conf param invalid")
	// }

	iamBceClientConfiguration = iam.BceClientConfiguration{
		Endpoint: "http://iam.bj.bce-internal.baidu.com",
		UserName: "console_chpc",
		Password: "1aP5ZRTrFjV1wQkOsz9eZf9u1sLsEujV",
		Version:  "/v3",
		Domain:   "Default",
		Retry:    &iam.NoRetryPolicy{},
	}
	return nil
}

func ValidateHttpRequest(req *http.Request) (*iam.Token, error) {
	client := iam.NewBceClient(&iamBceClientConfiguration)
	// iam.Token contain User Detail Info
	// 		token.User.ID: 主用户时为账户ID，子用户时为子用户ID
	// 		token.User.Name：主用户时为root，子用户时为子用户名
	// 		token.User.Domain.ID: 账户ID(accountID)
	// 		token.User.Domain.Name: PASSPORT:**********
	return client.ValidatorRequest(req)
}

func iamGetUserInfo(accountID string) (mapping PassportIAMAccountMapping, err error) {
	mapping.IAMAccountID = accountID
	client := iam.NewBceClient(&iamBceClientConfiguration)
	tokenid, err := client.GetConsoleTokenID()
	if err != nil {
		return
	}
	bceRequest := &iam.BceRequest{}
	bceRequest.SetProtocol("https")
	bceRequest.SetHeader("X-Auth-Token", tokenid)
	bceRequest.SetHeader("X-Subuser-Support", "true")
	bceRequest.SetUri("/v3/accounts/name/" + accountID)
	bceRequest.SetMethod(iamhttp.GET)
	resp := &iam.BceResponse{}
	if err = client.SendRequest(bceRequest, resp, client.AgentConfiguration != nil); err != nil {
		return
	}
	jsonBody := &IAMAccountNameResponse{}
	if err = resp.ParseJsonBody(jsonBody); err != nil {
		return
	}
	mapping.DisplayName = jsonBody.LoginName
	if jsonBody.AccountName != "" {
		ids := strings.Split(jsonBody.AccountName, " ")
		for _, id := range ids {
			kv := strings.Split(id, ":")
			if len(kv) == 2 {
				if kv[0] == "PASSPORT" {
					mapping.PassportID, _ = strconv.ParseInt(kv[1], 10, 64)
				} else if kv[0] == "UC" {
					mapping.UCID, _ = strconv.ParseInt(kv[1], 10, 64)
				}
			}
		}
	}
	return
}
