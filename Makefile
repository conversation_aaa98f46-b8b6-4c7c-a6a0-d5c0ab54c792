# init project path
HOMEDIR := $(shell pwd)
OUTDIR  := $(HOMEDIR)/output

# init command params
GO      := go
GOPATH  := $(shell $(GO) env GOPATH)
GOMOD   := $(GO) mod
GOBUILD := $(GO) build
GOTEST  := $(GO) test -gcflags="-N -l"
GOPKGS  := $$($(GO) list ./...| grep -vE "vendor")

# test cover files
COVPROF := $(HOMEDIR)/covprof.out  # coverage profile
COVFUNC := $(HOMEDIR)/covfunc.txt  # coverage profile information for each function
COVHTML := $(HOMEDIR)/covhtml.html # HTML representation of coverage profile

# make, make all
all: prepare compile package

# set proxy env
set-env:
	$(GO) env -w GO111MODULE=on
	$(GO) env -w GONOPROXY=\*\*.baidu.com\*\*
	$(GO) env -w GOPROXY=https://goproxy.baidu-int.com
	$(GO) env -w GONOSUMDB=\*

# make prepare, download dependencies
prepare: gomod
gomod: set-env
	$(GOMOD) download

# make compile
compile: build
build:
	$(shell rm -rf $(HOMEDIR)/bin)
	$(shell mkdir -p $(HOMEDIR)/bin)
	CGO_ENABLED=0  GOOS=linux  GOARCH=amd64 $(GOBUILD) -o $(HOMEDIR)/bin/$(APPNAME)

# package阶段，对编译产出进行打包，输出到output目录（可单独执行命令: make package）
package: package-bin
package-bin:
	$(shell rm -rf $(OUTDIR))
	$(shell mkdir -p $(OUTDIR))
	$(shell cp -a bin $(OUTDIR)/bin)
	$(shell cp -a conf_online $(OUTDIR)/conf)
	$(shell cp -a data $(OUTDIR)/data)
	$(shell cp -a tool $(OUTDIR)/tool)
	$(shell wget https://paddle-helix.bj.bcebos.com/checker.tar.gz -P $(OUTDIR)/)

# make test, test your code
test: set-env test-case
test-case:
	$(GOTEST) -v -cover $(GOPKGS)

# make clean
clean:
	$(GO) clean
	rm -rf $(OUTDIR)

# avoid filename conflict and speed up build
.PHONY: all prepare compile test package clean build
