# SubmitHelixVS 接口文档

## 接口概述
`SubmitHelixVS` 是一个用于提交 HelixVS 虚拟筛选任务的接口，属于分子对接和虚拟筛选功能模块。

## 基本信息
- **接口名称**: SubmitHelixVS
- **功能描述**: 提交 HelixVS 虚拟筛选任务，用于进行分子对接和虚拟筛选计算
- **任务类型**: TaskTypeVirtualVS (71)
- **功能类型**: 预测 (FuncTypeForecast)

## 请求参数

### 必需参数

| 参数名 | 类型 | 描述 | 限制 |
|--------|------|------|------|
| `pdb_path` | string | PDB 文件路径 | 长度范围: [1, 512] |
| `pdb_name` | string | PDB 文件名称 | 长度范围: [1, 200] 字符 |
| `name` | string | 任务名称 | 长度限制: ≤ 200 字符 |

### 可选参数

| 参数名 | 类型 | 默认值 | 描述 | 限制 |
|--------|------|--------|------|------|
| `mol_num` | int | 0 | 分子数量 | - |
| `filter_str` | string | "" | 过滤字符串 | - |
| `input_ligand_library` | string | "" | 输入配体库 | 支持的库: "Targetmol_CherryPick", "Lifechemicals", "ChemDiv", "topscience database" |
| `custom_lig_path` | string | "" | 自定义配体路径 | 如果提供则不使用预定义配体库 |
| `config` | object | {} | 配置参数 | 详见配置参数说明 |

### 配置参数 (config)

| 参数名 | 类型 | 描述 | 限制 |
|--------|------|------|------|
| `center_x` | float64 | 中心点 X 坐标 | 范围: (-10000, 10000) |
| `center_y` | float64 | 中心点 Y 坐标 | 范围: (-10000, 10000) |
| `center_z` | float64 | 中心点 Z 坐标 | 范围: (-10000, 10000) |
| `size_x` | float64 | X 方向尺寸 | 范围: (-10000, 10000) |
| `size_y` | float64 | Y 方向尺寸 | 范围: (-10000, 10000) |
| `size_z` | float64 | Z 方向尺寸 | 范围: (-10000, 10000) |
| `pocket_source` | string | 口袋来源 | 可选值: "auto_search", "user_defined", "example" |
| `protein_type` | string | 蛋白质类型 | 可选值: "target", "not_target" |

## 预定义配体库及费用

| 配体库名称 | 费用系数 |
|------------|----------|
| Targetmol_CherryPick | 3.4 |
| Lifechemicals | 82.00 |
| ChemDiv | 283.00 |
| topscience database | 1687.00 |

## 费用计算
费用计算公式：`13.68 * 1.3 * math.Pow(amount, 0.9) * 0.65 / 0.5`

其中 `amount` 为配体库对应的费用系数。

## 响应格式

### 成功响应
```json
{
  "code": 0,
  "logId": "请求日志ID",
  "msg": "",
  "data": {
    "task_id": 12345
  }
}
```

### 错误响应
```json
{
  "code": 错误码,
  "logId": "请求日志ID", 
  "msg": "错误信息",
  "data": null
}
```

## 错误码说明

| 错误码 | 描述 |
|--------|------|
| 2001 | 参数错误 |
| 5001 | 数据库错误 |
| 7001 | 账户错误（余额不足、服务未开通等） |
| 8002 | 任务提交错误 |

## 常见错误信息

- `name is invalid. The param name should not exceed 200 characters in length` - 任务名称超长
- `pdb_path is valid, length out of range [1, 512]` - PDB 路径长度超限
- `pdb_name is valid, length out of range [1, 200]` - PDB 名称长度超限
- `input_ligand_library xxx is invalid` - 不支持的配体库
- `config point error` - 配置参数中的坐标或尺寸超出范围
- `config protein type error` - 蛋白质类型参数错误
- `pdb file load failure` - PDB 文件加载失败
- `pdb file store failure` - PDB 文件存储失败
- `chpc service is not open` - CHPC 服务未开通

## 业务逻辑说明

1. **参数验证**: 验证必需参数的格式和长度限制
2. **配体库处理**: 如果未提供自定义配体路径，则使用预定义配体库
3. **文件处理**: 自动处理 PDB 文件的存储和路径转换
4. **费用计算**: 根据配体库类型计算任务费用
5. **余额检查**: 验证用户账户余额是否充足
6. **服务检查**: 验证 CHPC 服务是否已开通
7. **任务创建**: 创建任务记录并提交给调度系统

## 注意事项

1. 任务名称会自动处理，如果为空会生成默认名称
2. PDB 文件路径如果以 "qt" 结尾会自动去除
3. 如果 PDB 文件不在 "paddle-helix" 存储桶中，会自动迁移
4. 配置参数中的坐标值会自动格式化为 3 位小数
5. 任务提交失败时会自动将任务状态设置为失败

## 示例请求

```json
{
  "pdb_path": "https://example.com/protein.pdb",
  "pdb_name": "target_protein",
  "name": "虚拟筛选任务_001",
  "mol_num": 1000,
  "input_ligand_library": "ChemDiv",
  "config": {
    "center_x": 10.5,
    "center_y": 15.2,
    "center_z": 8.7,
    "size_x": 20.0,
    "size_y": 20.0,
    "size_z": 20.0,
    "pocket_source": "user_defined",
    "protein_type": "target"
  }
}
```

## 技术实现细节

### 函数签名
```go
func SubmitHelixVS(ctx context.Context, req ghttp.Request) ghttp.Response
```

### 主要处理流程
1. 解析请求参数
2. 参数验证和格式化
3. 配体库费用计算
4. 文件处理和存储
5. 服务权限检查
6. 任务创建和提交
7. 返回结果

### 相关常量
- `MaxTaskNameLimit`: 200 (任务名称最大长度)
- `MaxFileUrlLimit`: 512 (文件URL最大长度)
- `TaskTypeVirtualVS`: 71 (虚拟筛选任务类型)
- `FuncTypeForecast`: 20 (预测功能类型)
