package services

import (
	"context"
	"encoding/json"
	"errors"
	"io/ioutil"
	"net/http"
	"net/url"
	"strings"

	"icode.baidu.com/baidu/gdp/env"
)

var clientIDMap = map[string]string{
	env.RunModeDebug:   "Ov23licT7iI5fC8Cw1BL",
	env.RunModeTest:    "Ov23licT7iI5fC8Cw1BL",
	env.RunModeRelease: "0e1d54f7bc43d54ae386",
}
var clientSecretMap = map[string]string{
	env.RunModeDebug:   "cdd7cf3cac9f0b41b628e7007510c27ed73af7f3",
	env.RunModeTest:    "cdd7cf3cac9f0b41b628e7007510c27ed73af7f3",
	env.RunModeRelease: "981cd3c64d5a26fd6f335a147f65c1a4e2aab320",
}

type GitUserResp struct {
	LoginName string `json:"login"`
	Id        int64  `json:"id"`
	Name      string `json:"name"`
	AvatarUrl string `json:"avatar_url"`
}

type AccessTokenResp struct {
	AccessToken string `json:"access_token"`
}

// 获取令牌
func GitAccessToken(ctx context.Context, code string) (string, error) {
	reqUrl := "https://github.com/login/oauth/access_token"

	data := url.Values{}
	data.Set("client_id", clientIDMap[env.RunMode()])
	data.Set("client_secret", clientSecretMap[env.RunMode()])
	data.Set("code", code)

	req, err := http.NewRequest("POST", reqUrl, strings.NewReader(data.Encode()))
	if err != nil {
		return "", err
	}
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	req.Header.Set("Accept", "application/json")

	// 内网机器无法访问github，所以需要设置代理
	agentURL, _ := url.Parse("http://agent.baidu.com:8891")
	client := &http.Client{
		Transport: &http.Transport{
			Proxy: http.ProxyURL(agentURL),
		},
	}
	resp, err := client.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}

	var result AccessTokenResp
	err = json.Unmarshal(body, &result)
	if err != nil {
		return "", err
	}

	if result.AccessToken == "" {
		return "", errors.New("get access token failed")
	}

	return result.AccessToken, nil
}

// 获取用户信息
func GitUserInfo(ctx context.Context, accessToken string) (GitUserResp, error) {
	var res GitUserResp

	req, _ := http.NewRequest("GET", "https://api.github.com/user", nil)
	req.Header.Set("Authorization", "token "+accessToken)
	req.Header.Set("Content-Type", "application/json;charset=UTF-8")

	// 内网机器无法访问github，所以需要设置代理
	agentURL, _ := url.Parse("http://agent.baidu.com:8891")
	client := &http.Client{
		Transport: &http.Transport{
			Proxy: http.ProxyURL(agentURL),
		},
	}

	resp, err := client.Do(req)
	if err != nil {
		return res, err
	}
	defer resp.Body.Close()

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return res, err
	}
	_ = json.Unmarshal(body, &res)

	return res, nil
}
