package services

import (
	"context"
	"encoding/json"
	"errors"
	"os"
	"strconv"
	"strings"
	"time"

	"gorm.io/gorm"

	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/library/redis"
	"icode.baidu.com/helix_web/library/resource"
	"icode.baidu.com/helix_web/models"
)

// 示例
func AddTest(ctx context.Context) {
	db := resource.Gorm.WithContext(ctx)

	_ = db.Transaction(func(tx *gorm.DB) error {
		// 在事务中执行一些 db 操作（从这里开始，您应该使用 'tx' 而不是 'db'）
		if err := tx.Create(&models.User{Username: "test001"}).Error; err != nil {
			return err
		}

		if err := tx.Create(&models.User{Username: "test002"}).Error; err != nil {
			return err
		}

		// 返回 nil 提交事务
		return nil
	})
}

// 预提交
func PreSubmitTask(ctx context.Context, task models.Task) {
	resp, err := SubmitTaskNew(ctx, task)
	if err != nil {
		helpers.LogError(ctx, err)

		go helpers.HelixNotice(ctx, "---submit scheduler failed---"+err.Error())
	} else {
		// 提交成功
		task.Status = int64(models.TaskStatusDoing)
		task.ServerTaskId = uint64(resp.TaskId)
		_ = task.Save(ctx)
	}
}

// PreSubmitTaskChpc 预提交，仅针对chpc调度集群
func PreSubmitTaskChpc(ctx context.Context, task models.Task) error {
	resp, err := SubmitJob(ctx, task)
	if err != nil {
		helpers.LogError(ctx, err)
		go helpers.HelixNotice(ctx, "---submit scheduler failed---"+err.Error())
		return err
	}
	// 提交成功
	task.OrderID = resp.OrderID
	task.Status = int64(models.TaskStatusDoing)
	task.ServerTaskId = uint64(resp.TaskId)
	_ = task.Save(ctx)
	return nil
}

// PreSubmitTaskHF3Agab 预提交，仅针对HF3Agab
func PreSubmitTaskHF3Agab(ctx context.Context, task models.Task) (*ServerResult, error) {
	resp, err := SubmitJob(ctx, task)
	if err != nil {
		helpers.LogError(ctx, err)
		go helpers.HelixNotice(ctx, "---submit scheduler failed---"+err.Error())
		return nil, err
	}
	// 提交成功
	task.Status = int64(models.TaskStatusDoing)
	task.ServerTaskId = uint64(resp.TaskId)
	_ = task.Save(ctx)
	return nil, nil
}

// 获取用户通知消息
// 从 redis 获取taskId
func GetUserMessage(ctx context.Context, userId int64) ([]map[string]interface{}, error) {
	var messages []map[string]interface{}

	cacheKey := redis.UserMessagePrefix + strconv.FormatInt(userId, 10)
	strSlice := redis.SMembers(ctx, cacheKey)
	taskIds := helpers.StrSliceToInt64(strSlice)
	if len(taskIds) > 0 {
		taskM := models.Task{}
		taskList, err := taskM.GetListByIds(ctx, taskIds, "finish_time asc")
		if err != nil {
			return messages, errors.New("data error, repeat")
		}

		for _, taskData := range taskList {
			item := map[string]interface{}{
				"task_id":     taskData.ID,
				"name":        taskData.Name,
				"status":      taskData.Status,
				"finish_time": taskData.FinishTime.Unix(),
			}
			messages = append(messages, item)
		}

		// 删除 redis 中的消息
		redis.Del(ctx, cacheKey)
	}

	return messages, nil
}

// MQ 更新 task
func taskFn() func(context.Context, []byte) bool {
	return func(ctx context.Context, message []byte) bool {
		var resp MqResp
		_ = json.Unmarshal(message, &resp)

		if resp.TaskId <= 0 {
			return true
		}
		if resp.TrainStatus != int64(TrainStatusSucc) &&
			resp.TrainStatus != int64(TrainStatusFailed) &&
			resp.TrainStatus != int64(TrainStatusCancel) {
			return true
		}

		taskM := models.Task{}
		taskData, err := taskM.GetTaskById(ctx, resp.TaskId)
		if err != nil {
			go helpers.HelixNotice(ctx, "cann‘t find task, task_id:"+strconv.Itoa(int(taskData.ID)))
			return false
		}

		// check 任务状态
		if taskData.ID > 0 && taskData.Status == int64(models.TaskStatusDoing) {
			if resp.FinishTime <= 0 {
				resp.FinishTime = time.Now().Unix()
			}

			content := map[string]interface{}{
				"file_url":       resp.FileUrl,
				"rudder_task_id": resp.RudderTaskId,
				"error_code":     resp.ErrorCode,
				"log_path":       resp.LogPath,
				"download_url":   resp.DownloadUrl,
			}
			result, _ := json.Marshal(content)

			taskData.Result = string(result)
			taskData.Status = int64(TaskStatusMap[int(resp.TrainStatus)])
			taskData.FinishTime = time.Now()
			taskData.Resource = resp.Resource
			taskData.ChargeStartTime = time.Unix(resp.ChargeStartTime, 0)
			taskData.ChargeEndTime = time.Unix(resp.ChargeEndTime, 0)
			taskData.JobFailReason = resp.JobFailReason

			// 任务运行成功
			if resp.TrainStatus == int64(TrainStatusSucc) {
				// admet 特殊处理 (训练成功计入task_result、并且是基于基线模型的训练)
				if taskData.Type == uint64(models.TaskTypeAdmet) &&
					taskData.ParentID == uint64(models.DefaultParentId) {
					addTaskResult(ctx, taskData, resp.FileUrl)
				}

				// 付费模块
				if models.CheckTaskChargeType(int(taskData.Type)) {
					chargeRes := DoCharge(ctx, taskData)
					if chargeRes {
						taskData.ChargeTab = uint64(models.ChargeTabFinish)
					}
				}
			} else {
				// 任务没有成功，归还优惠券
				couponM := models.Coupon{}
				_ = couponM.Return(ctx, taskData.Coupons)
			}

			// cameo 特殊处理
			if taskData.Type == uint64(models.TaskTypeProtein) {
				sendEmailToCameo(ctx, taskData, resp.FileUrl)
				sendEmailToCasp(ctx, taskData, resp.FileUrl)
			}

			// 处理完成后再更新任务状态
			err = taskData.Save(ctx)
			if err != nil {
				go helpers.HelixNotice(ctx, "update task status failure, task_id:"+strconv.Itoa(int(taskData.ID)))
				return false
			}

			// 通知消息（写入redis）
			cacheKey := redis.UserMessagePrefix + strconv.Itoa(int(taskData.UserId))
			redis.SAdd(ctx, cacheKey, resp.FileUrl)
		}

		return true
	}
}

// admet 预测结果结构体
type admetResult struct {
	Input  []string          `json:"input"`
	Output [][][]interface{} `json:"output"`
	Pains  [][]string        `json:"pains_smarts"`
}

// admet 添加到任务结果表
func addTaskResult(ctx context.Context, taskData models.Task, fileUrl string) {
	contentStr := getFileContent(fileUrl)
	contentStr = strings.Replace(contentStr, "NaN", "null", -1)
	if len(contentStr) == 0 {
		return
	}

	// 拼接数据
	var resultStrut admetResult
	err := json.Unmarshal([]byte(contentStr), &resultStrut)
	if err != nil {
		return
	}

	resultM := models.TaskResult{}
	for key, val := range resultStrut.Input {
		if key >= len(resultStrut.Output) {
			continue
		}

		// 数据处理
		output := resultStrut.Output[key]
		if len(output) < 50 || len([]rune(val)) > models.ResultSerialLengthLimit {
			continue
		}

		validContent := true
		for _, valSlice := range output {
			if len(valSlice) < 5 {
				validContent = false
				break
			}
		}
		if !validContent {
			continue
		}

		// pains 处理
		var pains []string
		if key < len(resultStrut.Pains) {
			pains = resultStrut.Pains[key]
		}

		detailByte, _ := json.Marshal(output)
		painsByte, _ := json.Marshal(pains)
		resultData := models.TaskResult{
			TaskId:               taskData.ID,
			Serial:               val,
			Formula:              helpers.AssertString(output[0][4]),
			Caco2Permeability:    helpers.FormatFloat(helpers.AssertFloat64(output[15][4]), 4),
			IntestinalAbsorption: helpers.FormatFloat(helpers.AssertFloat64(output[16][4]), 4),
			OralBioavailability:  helpers.FormatFloat(helpers.AssertFloat64(output[19][4]), 4),
			BarrierPermeant:      helpers.FormatFloat(helpers.AssertFloat64(output[20][4]), 4),
			ProteinBinding:       helpers.FormatFloat(helpers.AssertFloat64(output[21][4]), 4),
			Hepatotoxicity:       helpers.FormatFloat(helpers.AssertFloat64(output[37][4]), 4),
			OralToxicity:         helpers.FormatFloat(helpers.AssertFloat64(output[33][4]), 4),
			LipinskiLaw:          int64(helpers.AssertFloat64(output[49][4])),
			GhoseLaw:             int64(helpers.AssertFloat64(output[50][4])),
			Carcinogenicity:      helpers.FormatFloat(helpers.AssertFloat64(output[35][4]), 4),
			Detail:               string(detailByte),
			Pains:                string(painsByte),
		}
		_, err = resultM.Add(ctx, resultData)
		if err != nil {
			go helpers.HelixNotice(ctx, "flow insert failed, task_id:"+strconv.Itoa(int(taskData.ID)))
		}
	}
}

// 蛋白质预测结果结构体
type proteinResult struct {
	PredictResultList []resultDetail `json:"predict_result_list"`
	UserSequence      string         `json:"user_sequence"`
}
type resultDetail struct {
	Name    string  `json:"name"`
	TmScore float64 `json:"plddt_score"`
	FileUrl string  `json:"file_url"`
	PdbUrl  string  `json:"pdb_url"`
}

// 发送邮件给cameo
func sendEmailToCameo(ctx context.Context, taskData models.Task, fileUrl string) {
	var taskConf models.TaskConfig

	err := json.Unmarshal([]byte(taskData.Config), &taskConf)
	if err != nil || taskConf.IsCameo != true || taskConf.IsCasp == true {
		return
	}

	// 获取用户邮箱
	cacheKey := redis.TaskEmailPrefix + strconv.Itoa(int(taskData.ID))
	email := redis.Get(ctx, cacheKey)
	redis.Del(ctx, cacheKey)
	if len(email) == 0 {
		return
	}

	var filePath string
	subject := "Protein Structure Prediction result（PaddleHelix）"
	emailContent := "Hi:\n\n  Prediction task failed (task title is " + taskData.Name + ")"
	if taskData.Status == int64(models.TaskStatusSucc) {
		contentStr := getFileContent(fileUrl)
		if len(contentStr) == 0 {
			return
		}

		var jsonRes proteinResult
		_ = json.Unmarshal([]byte(contentStr), &jsonRes)
		if len(jsonRes.PredictResultList) == 0 {
			return
		}

		needFileUrl := ""
		tmScoreNeed := float64(0)
		for _, item := range jsonRes.PredictResultList {
			tmScore := item.TmScore
			if tmScore <= tmScoreNeed || len(item.PdbUrl) == 0 {
				continue
			}

			needFileUrl = item.PdbUrl
			tmScoreNeed = tmScore
		}

		pdbStr := getFileContent(needFileUrl)
		if len(pdbStr) == 0 {
			return
		}

		// 写入文件
		filePath = "/tmp/" + helpers.GetRandomString(10) + ".pdb"
		err = helpers.WriteFile(filePath, pdbStr)
		if err != nil {
			helpers.LogError(ctx, err)
			return
		}
		defer os.Remove(filePath)

		emailContent = "Hi:\n\n  Prediction task success (task title is " + taskData.Name + ")"
	}

	// 发送邮件
	err = helpers.SendMail(helpers.FromUser, email, subject, emailContent, filePath)
	if err != nil {
		helpers.LogError(ctx, err)
	}
}

func sendEmailToCasp(ctx context.Context, taskData models.Task, fileUrl string) {
	var taskConf models.TaskConfig

	err := json.Unmarshal([]byte(taskData.Config), &taskConf)
	if err != nil || taskConf.IsCasp != true {
		return
	}

	// 获取用户邮箱
	cacheKey := redis.TaskEmailPrefix + strconv.Itoa(int(taskData.ID))
	email := redis.Get(ctx, cacheKey)
	redis.Del(ctx, cacheKey)
	if len(email) == 0 {
		return
	}

	subject := taskData.Name + "_hFold"
	emailContent := "Hi:\n\n  Prediction task failed (task title is " + taskData.Name + ")"
	if taskData.Status == int64(models.TaskStatusSucc) {
		contentStr := getFileContent(fileUrl)
		if len(contentStr) == 0 {
			return
		}

		var jsonRes proteinResult
		_ = json.Unmarshal([]byte(contentStr), &jsonRes)
		if len(jsonRes.PredictResultList) == 0 {
			return
		}

		needFileUrl := ""
		tmScoreNeed := float64(0)
		for _, item := range jsonRes.PredictResultList {
			tmScore := item.TmScore
			if tmScore <= tmScoreNeed {
				continue
			}

			realFileUrl := item.FileUrl
			if len(realFileUrl) == 0 {
				continue
			}

			needFileUrl = realFileUrl
			tmScoreNeed = tmScore
		}

		pdbStr := getFileContent(needFileUrl)
		if len(pdbStr) == 0 {
			return
		}

		emailContent = "Hi:\n\n success (task title is " + taskData.Name + ")"
		emailContent = "\n\nPFRMAT TS\nTARGET " + taskData.Name + "\nAUTHOR 0776-3167-7774\nMETHOD CALCULATE TEMPLATE AND MSA" + "" +
			" FROM BFD AND UNICLUST\nMETHOD USE 48 LAYERS TRANSFORMERS AND 8 ENSEMBLES\nMETHOD USE SEVERAL RECYCLES\nMODEL 1\nPARENT N/A\n" + pdbStr
	}

	// 发送邮件
	err = helpers.SendMail(helpers.FromUser, email, subject, emailContent, "")
	if err != nil {
		helpers.LogError(ctx, err)
	}
}

// GetTaskCount 获取任务数量
func GetTaskCount(ctx context.Context, taskType int) int64 {
	newTask := models.Task{}
	count, _ := newTask.GetTaskCount(ctx, taskType)
	return count
}
