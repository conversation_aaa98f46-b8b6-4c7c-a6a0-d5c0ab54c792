package services

import (
	"context"
	"errors"
	"gorm.io/gorm"
	"sync"

	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/models"
)

var (
	once       sync.Once
	ccdService *CcdService
)

type CcdService struct {
	ccdReadDB *models.Ccd
}

func NewCcdService() *CcdService {
	once.Do(func() {
		ccdService = &CcdService{
			ccdReadDB: &models.Ccd{},
		}
	})
	return ccdService
}

// QueryCcdList 检索CCD列表
func (s *CcdService) QueryCcdList(ctx context.Context, key string, pageNum int, pageSize int) ([]models.Ccd, error) {
	ccdCond := models.CcdCond{
		Key:      key,
		PageNum:  pageNum,
		PageSize: pageSize,
	}
	var ccdList []models.Ccd
	ccdList, err := s.ccdReadDB.QueryCcdList(ctx, ccdCond)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		helpers.DBLogError(ctx, err)
		go helpers.HelixNotice(ctx, err.Error())
		return nil, err
	}
	return ccdList, nil
}
