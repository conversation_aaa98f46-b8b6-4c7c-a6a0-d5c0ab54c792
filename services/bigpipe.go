package services

import (
	"context"
	"encoding/json"
	"io/ioutil"
	"net/http"
	"net/url"
	"strconv"
	"time"

	"icode.baidu.com/baidu/gdp/env"
	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/models"
)

const (
	defaultToken    = "$1$Yi9zT4qC$TMwgtkwwiqVV/wakR4Oc60"
	defaultUsername = "tianzhi"
	defaultMethod   = "publish"
)
var pipeUrlMap = map[string]string{
	env.RunModeDebug: "http://ws-nj02.dmop.baidu.com:8081/rest/pipe/easydl-charge-test-pipe",
	env.RunModeTest: "http://ws-nj02.dmop.baidu.com:8081/rest/pipe/easydl-charge-test-pipe",
	env.RunModeRelease: "http://ws-nj02.dmop.baidu.com:8081/rest/pipe/easydl-charge-pipe",
}

type PipeReq struct {
	Expires  int64  `json:"expires"`
	Username string `json:"username"`
	Sign     string `json:"sign"`
	Method   string `json:"method"`
	MsgGuid  string `json:"msg_guid"`
	Message  string `json:"message"`
}
type PipeResp struct {
	TopicMsgid int64 `json:"topic_msgid"`
	Pipelet    int64 `json:"pipelet"`
	Status     int64 `json:"status"`
}
type PipeMsg struct {
	Uuid        string
	TaskId      string
	AccountId   string
	AccountType string
	ApiId       string
	StartTime   string
	EndTime     string
	UsageNumber string
	Duration    string
}

// 推送到bigpipe
func PushBigpipe(ctx context.Context, pipeMsg PipeMsg, chargeType int64) error {
	message := time.Now().Format("2006-01-02 15:04:05") +
		": uuid["+ pipeMsg.Uuid +
		"] taskId[" + pipeMsg.TaskId +
		"] accountId[" + pipeMsg.AccountId +
		"] accountType[" + pipeMsg.AccountType +
		"] apiId[" + pipeMsg.ApiId +
		"] startTime[" + pipeMsg.StartTime +
		"] endTime[" + pipeMsg.EndTime + "]"
	if chargeType == models.ChargeTypeNum {
		message = "paddlehelixcount: "+ message + " chargeCnt[" + pipeMsg.UsageNumber + "] errorCode[0]"
	} else {
		message = "paddlehelixduration: "+ message + " chargeCnt[" + pipeMsg.Duration + "] errorCode[0]"
	}

	// 数据组装
	expires := time.Now().Add(600*time.Second).Unix()
	postData := url.Values{
		"expires":  {strconv.Itoa(int(expires))},
		"username": {defaultUsername},
		"method":   {defaultMethod},
		"sign":     {helpers.Md5(strconv.Itoa(int(expires)) + defaultToken)},
		"msg_guid": {strconv.Itoa(int(expires))},
		"message":  {message},
	}
	reqUrl := pipeUrlMap[env.RunMode()]

	// 写日志
	messageJson, _ := json.Marshal(postData)
	helpers.LogNotice(ctx, string(messageJson))
	go helpers.HelixNotice(ctx, string(messageJson) + "---url:" + reqUrl)

	// 发送请求
	resp, err := http.PostForm(reqUrl, postData)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return err
	}

	// 计入日志
	helpers.LogNotice(ctx, string(body))
	go helpers.HelixNotice(ctx, string(body))
	return nil
}


