package services

import (
	"context"

	"icode.baidu.com/baidu/gdp/env"
	"icode.baidu.com/helix_web/library/mqaa"

	"icode.baidu.com/helix_web/library/mq"
)

// 消费预处理任务
func ConsumerPretreat(ctx context.Context) error {
	rabbitM, err := mq.GetInstance()
	if err != nil {
		return err
	}
	defer rabbitM.Close()

	err = rabbitM.Consumer(ctx, mq.SyncPretreatResultQueue, pretreatFn())
	return err
}

// 消费任务
func ConsumerTask(ctx context.Context) error {
	rabbitM, err := mq.GetInstance()
	if err != nil {
		return err
	}
	defer rabbitM.Close()

	err = rabbitM.Consumer(ctx, mq.SyncTaskResultQueue, taskFn())
	return err
}

// 消费HelixFoldAA任务
func ConsumerTaskAA(ctx context.Context) error {
	rabbitM, err := mqaa.GetInstance()
	if err != nil {
		return err
	}
	defer rabbitM.Close()

	if env.RunMode() == "debug" {
		err = rabbitM.Consumer(ctx, mqaa.SyncTaskAAResultQueueDebug, taskFn())
	} else if env.RunMode() == "test" {
		err = rabbitM.Consumer(ctx, mqaa.SyncTaskAAResultQueueTest, taskFn())
	} else {
		err = rabbitM.Consumer(ctx, mqaa.SyncTaskAAResultQueue, taskFn())
	}

	return err
}
