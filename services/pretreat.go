package services

import (
	"context"
	"encoding/json"
	"time"

	"icode.baidu.com/helix_web/models"
)

// MQ 更新 task
func pretreatFn() func(context.Context, []byte)bool {
	return func(ctx context.Context, message []byte)bool {
		var resp MqResp
		_ = json.Unmarshal(message, &resp)

		if resp.TaskId <= 0 || resp.RudderTaskId <= 0 {
			return true
		}
		if resp.TrainStatus != int64(TrainStatusSucc) && resp.TrainStatus != int64(TrainStatusFailed) {
			return true
		}

		pretreatM := models.Pretreat{}
		pretreatData, err := pretreatM.GetById(ctx, resp.TaskId)
		if err != nil {
			return false
		}

		if pretreatData.ID > 0 && pretreatData.Status == int64(models.TaskStatusDoing) {
			if resp.FinishTime <= 0 {
				resp.FinishTime = time.Now().Unix()
			}

			content := map[string]interface{}{
				"file_url":   resp.FileUrl,
				"error_code": resp.ErrorCode,
			}
			result, _ := json.Marshal(content)

			pretreatData.Result = string(result)
			pretreatData.Status = int64(TaskStatusMap[int(resp.TrainStatus)])
			pretreatData.FinishTime = time.Unix(resp.FinishTime, 0)
			err = pretreatData.Save(ctx)
			if err != nil {
				return false
			}
		}

		return true
	}
}
