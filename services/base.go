package services

import (
	"context"
	"encoding/json"
	"strings"

	"icode.baidu.com/baidu/gdp/codec"
	"icode.baidu.com/baidu/gdp/ghttp"
	"icode.baidu.com/baidu/gdp/net/ral"
	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/library/bce"
)

func getFileContent(fileUrl string) string {
	bucket, object := helpers.DealBosFileUrl(fileUrl)
	if len(bucket) == 0 || len(object) == 0 {
		return ""
	}

	contentStr, err := bce.GetObject(bucket, object)
	if err != nil || len(contentStr) == 0 {
		return ""
	}

	contentStr = strings.Trim(contentStr, " ")
	return contentStr
}

// call
func callService(ctx context.Context, message interface{}, serviceName, reqPath string, resp interface{}) error {
	messageJson, _ := json.Marshal(message)
	helpers.LogNotice(ctx, string(messageJson))
	go helpers.HelixNotice(ctx, string(messageJson) + "---service:uri---" + serviceName + ":" + reqPath)

	ralReq := &ghttp.RalPostRequest {
		PostData: message,
		Encoder: codec.JSONEncoder,
	}
	ralReq.Path = reqPath
	ralReq.Header = map[string][]string{
		"Log-Id": {},
		"Content-Type": {"application/json;charset=UTF-8"},
	}

	ralResp := &ghttp.RalResponse {
		Data:    &resp,
		Decoder: codec.JSONDecoder,
	}
	err := ral.RAL(ctx, serviceName, ralReq, ralResp)
	if err != nil {
		helpers.LogError(ctx, err)
		go helpers.HelixNotice(ctx, err.Error())
		return err
	}

	// 写数据日志
	respJson, _ := json.Marshal(ralResp.Data)
	helpers.LogNotice(ctx, string(respJson))
	go helpers.HelixNotice(ctx, string(respJson))

	return nil
}

