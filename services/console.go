package services

import (
	"context"
	"errors"

	"icode.baidu.com/helix_web/models"
)

const (
	AccountPassType = "passport"
	AccountUCType   = "uc"

	// apiId
	HelixFold1       = 1174
	HelixFold2       = 1175
	HelixFold3       = 1176
	HelixFoldSingle1 = 1177
	HelixFoldSingle2 = 1178
	HelixFoldSingle3 = 1179
	LinearDesign1    = 1180
	LinearDesign2    = 1181
	LinearDesign3    = 1182
	AdmetForecast    = 1264
	AdmetApi         = 1265
	ProteinComplex1  = 1262
	ProteinComplex2  = 1263
	KYKT             = 1437
	UTR              = 1438
	LinearFold       = 1439
	LinearPartition  = 1440

	NoCloudIdCode = 11000
)

var PackageApiList = []int{
	LinearDesign1,
	LinearDesign2,
	LinearDesign3,
	AdmetForecast,
	AdmetApi,
}
var accountTypeMap = map[int]string{
	models.SourcePassport: AccountPassType,
	models.SourceUC:       AccountUCType,
	models.SourceGitHub:   AccountPassType,
}
var AllApiList = []int{
	HelixFold1,
	Helix<PERSON>old2,
	He<PERSON><PERSON>old3,
	Helix<PERSON><PERSON><PERSON>ingle1,
	He<PERSON>FoldSingle2,
	HelixFoldSingle3,
	LinearDesign1,
	LinearDesign2,
	LinearDesign3,
	AdmetForecast,
	AdmetApi,
	ProteinComplex1,
	ProteinComplex2,
	KYKT,
	UTR,
	LinearFold,
	LinearPartition,
}

// 通过devApiId 获取用户信息
type InfoResp struct {
	Success bool              `json:"success"`
	Code    int64             `json:"code"`
	Result  AccountInfoResult `json:"result"`
}
type AccountInfoResult struct {
	RealId      int64  `json:"realId"`
	AccountType string `json:"userType"`
	Name        string `json:"name"`
}

func GetAccountInfo(ctx context.Context, appId int64) (AccountInfoResult, error) {
	message := map[string]interface{}{
		"devAppId":    appId,
		"checkEasyDL": false,
	}

	resp := &InfoResp{}
	err := callService(ctx, message, "console", "/console/easydl/inner/app/info", resp)
	if err != nil {
		return AccountInfoResult{}, err
	}

	// 返回数据处理
	if !resp.Success {
		return AccountInfoResult{}, errors.New("appid get account info failed")
	}
	return resp.Result, nil
}

// 获取用量信息
type FundResp struct {
	Success bool              `json:"success"`
	Code    int64             `json:"code"`
	Result  AccountFundResult `json:"result"`
}
type AccountFundResult struct {
	Cash        float64 `json:"cash"`
	Rebate      float64 `json:"rebate"`
	TotalAmount float64 `json:"totalAmount"`
}

func GetAccountFund(ctx context.Context, accountId, source int64) (AccountFundResult, error) {
	message := map[string]interface{}{
		"accountId":   accountId,
		"accountType": accountTypeMap[int(source)],
	}

	resp := &FundResp{}
	err := callService(ctx, message, "console", "/console/easydl/inner/accountfund", resp)
	if err != nil {
		return AccountFundResult{}, err
	}

	// 返回数据处理
	if !resp.Success {
		return AccountFundResult{}, errors.New("get account info failed")
	}
	return resp.Result, nil
}

// 获取用量信息
type QuotaResp struct {
	Success bool                `json:"success"`
	Result  map[int64]QuotaItem `json:"result"`
}
type QuotaItem struct {
	Left  int64 `json:"left"`
	Total int64 `json:"total"`
}

func GetQuotaSimple(ctx context.Context, accountId, source int64, apiList []int) (map[int64]QuotaItem, error) {
	message := map[string]interface{}{
		"account_id":   accountId,
		"account_type": accountTypeMap[int(source)],
		"api_list":     apiList,
	}

	resp := &QuotaResp{}
	err := callService(ctx, message, "control", "/control/quota/simple", resp)
	if err != nil {
		return map[int64]QuotaItem{}, err
	}

	// 返回数据处理
	if !resp.Success {
		return map[int64]QuotaItem{}, errors.New("success is false")
	}
	return resp.Result, nil
}

// 获取资源包
type packageResp struct {
	Success bool          `json:"success"`
	Result  PackageResult `json:"result"`
}
type PackageResult struct {
	TotalCount int64         `json:"totalCount"`
	Items      []PackageItem `json:"result"`
}
type PackageItem struct {
	ConsoleApiId   string `json:"consoleApiId"`
	ResourceStatus string `json:"resourceStatus"`
	PackageId      string `json:"packageId"`
	Usage          string `json:"usage"`
	Total          string `json:"total"`
}

func GetPackageList(ctx context.Context, accountId, source int64, apiList []int) ([]PackageItem, error) {
	message := map[string]interface{}{
		"account_id":   accountId,
		"account_type": accountTypeMap[int(source)],
		"api_list":     apiList,
	}

	resp := &packageResp{}
	err := callService(ctx, message, "control", "/control/resource/package/list", resp)
	if err != nil {
		return []PackageItem{}, err
	}

	// 返回数据处理
	if !resp.Success {
		return []PackageItem{}, errors.New("success is false")
	}
	return resp.Result.Items, nil
}

// 获取资源包状态
const OrderStatusOpened = "1"  // 欠费状态
const OrderStatusStopped = "2" // 欠费状态
type OrderResp struct {
	Success bool                `json:"success"`
	Result  map[int64]OrderItem `json:"result"`
}
type OrderItem struct {
	PackageExist    bool   `json:"packageExist"`
	Status          string `json:"status"`
	OrderCreateTime int64  `json:"orderCreateTime"`
}

func GetOrderStatus(ctx context.Context, accountId, source int64, apiList []int) (map[int64]OrderItem, error) {
	message := map[string]interface{}{
		"account_id":   accountId,
		"account_type": accountTypeMap[int(source)],
		"api_list":     apiList,
	}

	resp := &OrderResp{}
	err := callService(ctx, message, "control", "/control/order/infoByApi", resp)
	if err != nil {
		return map[int64]OrderItem{}, err
	}

	// 返回数据处理
	if !resp.Success {
		return map[int64]OrderItem{}, errors.New("success is false")
	}
	return resp.Result, nil
}
