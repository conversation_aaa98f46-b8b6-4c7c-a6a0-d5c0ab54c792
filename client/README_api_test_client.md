# Helix Web API 测试客户端

这是一个用于测试 Helix Web API 的命令行工具，实现了以下功能：

1. 调用 `/user/login` 接口登录
2. 用登录接口返回的 token 调用 `/batch/submit/helixfoldaa/forecast` 接口提交任务
3. 用登录接口返回的token 调用 `/task/list` 查询任务列表
4. 用提交任务返回的任务id调用 `/task/info` 查询任务详情

## 安装依赖

```bash
pip install requests pycryptodome
```

## 使用方法

### 1. 运行完整测试流程

```bash
python api_test_client.py --username your_username --password your_plain_password
```

### 2. 指定服务器地址

```bash
python api_test_client.py --url http://your-server:8080 --username your_username --password your_plain_password
```

### 3. 使用自定义蛋白质序列

```bash
python api_test_client.py --username your_username --password your_plain_password --sequence "MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVATPRGYVLAGG"
```

### 4. 只获取任务列表

```bash
python api_test_client.py --username your_username --password your_plain_password --list-only
```

### 5. 只提交任务

```bash
python api_test_client.py --username your_username --password your_plain_password --submit-only
```

### 6. 查询指定任务详情

```bash
python api_test_client.py --username your_username --password your_plain_password --task-id 12345
```

## 参数说明

- `--url`: API服务器地址，默认为 `http://localhost:8080`
- `--username`: 用户名（必需）
- `--password`: 明文密码（必需，脚本会自动加密）
- `--sequence`: 蛋白质序列（可选，不提供时使用默认测试序列）
- `--task-id`: 指定要查询的任务ID
- `--list-only`: 只获取任务列表
- `--submit-only`: 只提交任务

## 注意事项

1. **密码加密**: 现在只需输入明文密码，脚本会自动加密。
2. **Token处理**: 登录成功后，工具会自动从响应头中获取Authorization token，并在后续请求中使用。
3. **默认序列**: 如果不提供蛋白质序列，工具会使用一个默认的测试序列。
4. **错误处理**: 工具会显示详细的错误信息，包括HTTP状态码和响应内容。

## 示例输出

```
================================================================================
开始运行 Helix Web API 完整测试
================================================================================
正在登录用户: testuser
✅ 登录成功
使用默认测试序列: MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVATPRGYVLAGG...
正在提交任务: test_task_1703123456
✅ 任务提交成功，任务ID: 12345
等待2秒...
正在获取任务列表...
✅ 获取任务列表成功，共 15 个任务
当前页显示 10 个任务:
--------------------------------------------------------------------------------
 1. 任务ID:    12345 | 名称: test_task_1703123456 | 状态: 进行中   | 创建时间: 1703123456
 2. 任务ID:    12344 | 名称: previous_task        | 状态: 完成     | 创建时间: 1703123000
正在获取任务详情: 12345
✅ 获取任务详情成功:
--------------------------------------------------------------------------------
任务ID: 12345
任务名称: test_task_1703123456
任务类型: 15
任务状态: 进行中
创建时间: 1703123456
更新时间: 1703123456
配置信息: {
  "entities": [
    {
      "sequence": "MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVATPRGYVLAGG",
      "type": "protein"
    }
  ],
  "ensemble": 1,
  "model_type": "HelixFold3",
  "recycle": 10
}
================================================================================
✅ 所有测试完成！
================================================================================
```

## 故障排除

1. **登录失败**: 检查用户名和密码是否正确。
2. **连接失败**: 检查服务器地址是否正确，确保服务器正在运行。
3. **权限错误**: 确保用户有提交任务的权限。
4. **任务提交失败**: 检查蛋白质序列格式是否正确，确保不超过长度限制。 