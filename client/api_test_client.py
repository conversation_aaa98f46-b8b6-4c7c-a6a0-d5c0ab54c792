#!/usr/bin/env python3
"""
Helix Web API 测试客户端
功能：
1. 调用 /user/login 接口登录
2. 用登录接口返回的 token 调用 /batch/submit/helixfoldaa/forecast 接口提交任务
3. 用登录接口返回的token 调用 /task/list 查询任务列表
4. 用提交任务返回的任务id调用 /task/info 查询任务详情
"""

import argparse
import json
import requests
import sys
import time
from typing import Dict, Any, Optional
import binascii
from Crypto.Cipher import AES
import re
import os

# AES密钥（16字节）
AES_KEY = bytes.fromhex("8f6a40c7e5b1ac6fb2d74a0e967b3c1f2a53c9de4b48df51a178af035e4d0b2a")

def pkcs7_pad(data: bytes, block_size: int = 16) -> bytes:
    pad_len = block_size - len(data) % block_size
    return data + bytes([pad_len] * pad_len)

def aes_encrypt_to_hex(plaintext: str, key: bytes) -> str:
    cipher = AES.new(key, AES.MODE_ECB)
    padded = pkcs7_pad(plaintext.encode('utf-8'))
    encrypted = cipher.encrypt(padded)
    return binascii.hexlify(encrypted).decode('utf-8')

class HelixAPIClient:
    """Helix Web API 客户端"""
    
    def __init__(self, base_url: str = "http://localhost:8080"):
        self.base_url = base_url.rstrip('/')
        self.token = None
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json',
            'User-Agent': 'HelixAPITestClient/1.0'
        })
    
    def login(self, username: str, password: str) -> bool:
        """
        用户登录
        
        Args:
            username: 用户名
            password: 明文密码（自动加密）
            
        Returns:
            bool: 登录是否成功
        """
        try:
            url = f"{self.base_url}/user/login"
            # 自动加密明文密码
            encrypted_password = aes_encrypt_to_hex(password, AES_KEY)
            data = {
                "username": username,
                "password": encrypted_password
            }
            
            print(f"正在登录用户: {username}")
            response = self.session.post(url, json=data)
            
            text = response.text.strip()
            print("响应内容:", text)
            print("HTTP状态码:", response.status_code)

            # 检查是否为 JSONP 格式
            if text.startswith("callback(") and text.endswith(")"):
                json_str = text[len("callback("):-1]
            else:
                json_str = text

            result = json.loads(json_str)
            
            if result.get('code') == 0:
                # 从响应头中获取token
                auth_header = response.headers.get('Authorization')
                if auth_header:
                    print(f"返回header中的token: {auth_header}")
                    self.token = auth_header
                    self.session.headers.update({'Authorization': self.token})
                    print("✅ 登录成功")
                    return True
                else:
                    print("❌ 登录失败: 响应头中没有找到Authorization token")
                    return False
            else:
                print(f"❌ 登录失败: {result.get('message', '未知错误')}")
                return False
                
        except Exception as e:
            print(f"❌ 登录异常: {e}")
            return False
    
    def submit_helixfoldaa_task(self, task_name: str = None, sequence: str = None) -> Optional[int]:
        """
        提交 HelixFoldAA 任务
        
        Args:
            task_name: 任务名称
            sequence: 蛋白质序列
            
        Returns:
            int: 任务ID，失败返回None
        """
        if not self.token:
            print("❌ 请先登录")
            return None
        print("token:", self.token)
        try:
            url = f"{self.base_url}/batch/submit/helixfoldaa/forecast"
            
            # 如果没有提供序列，使用默认的测试序列
            if not sequence:
                sequence = "MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG"
                print(f"使用默认测试序列: {sequence[:20]}...")
            
            if not task_name:
                task_name = f"test_task_{int(time.time())}"
            
            data = {
                "tasks": [{
                    "name": task_name,
                    "entities": [{
                        "type": "protein",
                        "sequence": sequence,
                        "count": 1
                    }],
                    "recycle": 10,
                    "ensemble": 1,
                    "model_type": "HelixFold3"
                }]
            }
            
            print(f"正在提交任务: {task_name}")
            response = self.session.post(url, json=data)
            
            if response.status_code == 200:
                text = response.text.strip()
                # 检查是否为 JSONP 格式
                if text.startswith("callback(") and text.endswith(")"):
                    json_str = text[len("callback("):-1]
                else:
                    json_str = text
                result = json.loads(json_str)
                if result.get('code') == 0:
                    task_ids = result.get('data', {}).get('task_ids', [])
                    if task_ids:
                        task_id = task_ids[0]
                        print(f"✅ 任务提交成功，任务ID: {task_id}")
                        return task_id
                    else:
                        print("❌ 任务提交失败: 响应中没有任务ID")
                        return None
                else:
                    print(f"❌ 任务提交失败: {result.get('message', '未知错误')}")
                    return None
            else:
                print(f"❌ 任务提交失败: HTTP {response.status_code}")
                print(f"响应内容: {response.text}")
                return None
                
        except Exception as e:
            # 尝试打印 response 内容
            try:
                print(f"响应内容: {response.text}")
            except Exception:
                print("无法获取 response.text")
            print(f"❌ 任务提交异常: {e}")
            return None
    
    def get_task_list(self, limit: int = 10, page: int = 1) -> bool:
        """
        获取任务列表
        
        Args:
            limit: 每页数量
            page: 页码
            
        Returns:
            bool: 是否成功
        """
        if not self.token:
            print("❌ 请先登录")
            return False
            
        try:
            url = f"{self.base_url}/task/list"
            data = {
                "limit": limit,
                "page": page
            }
            
            print("正在获取任务列表...")
            response = self.session.post(url, json=data)
            
            if response.status_code == 200:
                text = response.text.strip()
                # 检查是否为 JSONP 格式
                if text.startswith("callback(") and text.endswith(")"):
                    json_str = text[len("callback("):-1]
                else:
                    json_str = text
                result = json.loads(json_str)
                if result.get('code') == 0:
                    task_data = result.get('data', {})
                    total = task_data.get('total', 0)
                    items = task_data.get('items', [])
                    
                    print(f"✅ 获取任务列表成功，共 {total} 个任务")
                    print(f"当前页显示 {len(items)} 个任务:")
                    print("-" * 80)
                    
                    for i, task in enumerate(items, 1):
                        task_id = task.get('task_id', 'N/A')
                        name = task.get('name', 'N/A')
                        status = task.get('status', 'N/A')
                        created_at = task.get('created_at', 'N/A')
                        
                        # 转换状态码为可读状态
                        status_text = self._get_status_text(status)
                        
                        print(f"{i:2d}. 任务ID: {task_id:>8} | 名称: {name:<20} | 状态: {status_text:<8} | 创建时间: {created_at}")
                    
                    return True
                else:
                    print(f"❌ 获取任务列表失败: {result.get('message', '未知错误')}")
                    return False
            else:
                print(f"❌ 获取任务列表失败: HTTP {response.status_code}")
                print(f"响应内容: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ 获取任务列表异常: {e}")
            return False
    
    def get_task_info(self, task_id: int) -> bool:
        """
        获取任务详情
        
        Args:
            task_id: 任务ID
            
        Returns:
            bool: 是否成功
        """
        if not self.token:
            print("❌ 请先登录")
            return False
            
        try:
            url = f"{self.base_url}/task/info"
            data = {
                "task_id": task_id
            }
            
            print(f"正在获取任务详情: {task_id}")
            response = self.session.post(url, json=data)
            
            if response.status_code == 200:
                text = response.text.strip()
                # 检查是否为 JSONP 格式
                if text.startswith("callback(") and text.endswith(")"):
                    json_str = text[len("callback("):-1]
                else:
                    json_str = text
                result = json.loads(json_str)
                if result.get('code') == 0:
                    task_data = result.get('data', {})
                    
                    print("✅ 获取任务详情成功:")
                    print("-" * 80)
                    print(f"任务ID: {task_data.get('task_id', 'N/A')}")
                    print(f"任务名称: {task_data.get('name', 'N/A')}")
                    print(f"任务类型: {task_data.get('type', 'N/A')}")
                    print(f"任务状态: {self._get_status_text(task_data.get('status', 'N/A'))}")
                    print(f"创建时间: {task_data.get('created_at', 'N/A')}")
                    print(f"更新时间: {task_data.get('updated_at', 'N/A')}")
                    
                    # 显示配置信息
                    config = task_data.get('config', {})
                    if config:
                        print(f"配置信息: {json.dumps(config, indent=2, ensure_ascii=False)}")
                    
                    # 显示结果信息
                    result_data = task_data.get('result', {})
                    if result_data:
                        print(f"结果信息: {json.dumps(result_data, indent=2, ensure_ascii=False)}")
                    
                    return True
                else:
                    print(f"❌ 获取任务详情失败: {result.get('message', '未知错误')}")
                    return False
            else:
                print(f"❌ 获取任务详情失败: HTTP {response.status_code}")
                print(f"响应内容: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ 获取任务详情异常: {e}")
            return False
    
    def _get_status_text(self, status: Any) -> str:
        """将状态码转换为可读文本"""
        status_map = {
            1: "进行中",
            2: "完成",
            -1: "失败",
            -2: "取消"
        }
        return status_map.get(status, f"未知({status})")
    
    def cancel_task(self, task_id: int) -> bool:
        """
        取消任务
        Args:
            task_id: 任务ID
        Returns:
            bool: 是否成功
        """
        if not self.token:
            print("❌ 请先登录")
            return False
        try:
            url = f"{self.base_url}/task/cancel"
            data = {"task_id": task_id}
            print(f"正在取消任务: {task_id}")
            response = self.session.post(url, json=data)
            if response.status_code == 200:
                text = response.text.strip()
                # 检查是否为 JSONP 格式
                if text.startswith("callback(") and text.endswith(")"):
                    json_str = text[len("callback("):-1]
                else:
                    json_str = text
                result = json.loads(json_str)
                if result.get('code') == 0:
                    print(f"✅ 任务取消成功: {task_id}")
                    return True
                else:
                    print(f"❌ 任务取消失败: {result.get('message', '未知错误')}")
                    return False
            else:
                print(f"❌ 任务取消失败: HTTP {response.status_code}")
                print(f"响应内容: {response.text}")
                return False
        except Exception as e:
            print(f"❌ 任务取消异常: {e}")
            return False
    
    def run_full_test(self, username: str, password: str, sequence: str = None) -> bool:
        """
        运行完整的测试流程
        
        Args:
            username: 用户名
            password: 密码
            sequence: 蛋白质序列（可选）
            
        Returns:
            bool: 测试是否全部成功
        """
        print("=" * 80)
        print("开始运行 Helix Web API 完整测试")
        print("=" * 80)
        
        # 1. 登录
        if not self.login(username, password):
            return False
        
        # 2. 提交任务
        task_id = self.submit_helixfoldaa_task(sequence=sequence)
        if not task_id:
            return False
        
        # 等待一下，确保任务已经创建
        print("等待2秒...")
        time.sleep(2)
        
        # 3. 获取任务列表
        if not self.get_task_list():
            return False
        
        # 4. 获取任务详情
        if not self.get_task_info(task_id):
            return False
        
        # 5. 取消任务
        if not self.cancel_task(task_id):
            return False
        
        print("=" * 80)
        print("✅ 所有测试完成！")
        print("=" * 80)
        return True

    def upload_file(self, file_path: str, upload_file_name: str = None) -> bool:
        """
        上传文件到 /file/upload 接口
        Args:
            file_path: 本地文件路径
            upload_file_name: 上传时的文件名（可选）
        Returns:
            bool: 是否上传成功
        """
        if not self.token:
            print("❌ 请先登录")
            return False
        if not os.path.isfile(file_path):
            print(f"❌ 文件不存在: {file_path}")
            return False
        if not upload_file_name:
            upload_file_name = os.path.basename(file_path)
        url = f"{self.base_url}/file/upload"
        files = {
            'data': (upload_file_name, open(file_path, 'rb'))
        }
        data = {
            'file_name': upload_file_name
        }
        headers = self.session.headers.copy()
        # requests会自动设置Content-Type为multipart/form-data
        headers.pop('Content-Type', None)
        print(f"正在上传文件: {file_path} -> {upload_file_name}")
        try:
            response = self.session.post(url, data=data, files=files, headers=headers)
            print("HTTP状态码:", response.status_code)
            text = response.text.strip()
            print("响应内容:", text)
            if response.status_code == 200:
                # 检查是否为 JSONP 格式
                if text.startswith("callback(") and text.endswith(")"):
                    json_str = text[len("callback("):-1]
                else:
                    json_str = text
                result = json.loads(json_str)
                if result.get('code') == 0:
                    print(f"✅ 文件上传成功: {result.get('data', '')}")
                    return True
                else:
                    print(f"❌ 文件上传失败: {result.get('message', '未知错误')}")
                    return False
            else:
                print(f"❌ 文件上传失败: HTTP {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 文件上传异常: {e}")
            return False


def main():
    parser = argparse.ArgumentParser(description='Helix Web API 测试客户端')
    parser.add_argument('--url', default='http://localhost:8080', help='API服务器地址')
    parser.add_argument('--username', required=True, help='用户名')
    parser.add_argument('--password', required=True, help='密码（需要是加密后的密码）')
    parser.add_argument('--sequence', help='蛋白质序列（可选）')
    parser.add_argument('--task-id', type=int, help='查询指定任务ID的详情')
    parser.add_argument('--list-only', action='store_true', help='只获取任务列表')
    parser.add_argument('--submit-only', action='store_true', help='只提交任务')
    parser.add_argument('--upload-file', help='要上传的本地文件路径')
    parser.add_argument('--upload-file-name', help='上传时的文件名（可选）')
    args = parser.parse_args()
    
    client = HelixAPIClient(args.url)
    
    try:
        if args.upload_file:
            # 只上传文件
            if client.login(args.username, args.password):
                if not client.upload_file(args.upload_file, args.upload_file_name):
                    sys.exit(1)
            else:
                sys.exit(1)
        elif args.list_only:
            # 只获取任务列表
            if client.login(args.username, args.password):
                client.get_task_list()
            else:
                sys.exit(1)
        elif args.submit_only:
            # 只提交任务
            if client.login(args.username, args.password):
                task_id = client.submit_helixfoldaa_task(sequence=args.sequence)
                if task_id:
                    print(f"任务ID: {task_id}")
                else:
                    sys.exit(1)
            else:
                sys.exit(1)
        elif args.task_id:
            # 查询指定任务详情
            if client.login(args.username, args.password):
                if not client.get_task_info(args.task_id):
                    sys.exit(1)
            else:
                sys.exit(1)
        else:
            # 运行完整测试
            if not client.run_full_test(args.username, args.password, args.sequence):
                sys.exit(1)
                
    except KeyboardInterrupt:
        print("\n用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"程序异常: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main() 