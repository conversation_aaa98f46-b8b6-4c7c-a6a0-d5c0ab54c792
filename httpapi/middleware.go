package httpapi

import (
	"context"
	"fmt"
	"net"
	"net/http"
	"runtime"
	"strconv"
	"strings"
	"time"

	"icode.baidu.com/baidu/gdp/env"
	"icode.baidu.com/baidu/gdp/ghttp"
	"icode.baidu.com/baidu/gdp/logit"

	"icode.baidu.com/helix_web/controllers"
	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/library/chpc"
	"icode.baidu.com/helix_web/models"
	"icode.baidu.com/helix_web/services"
)

// RecoverMiddleWareFunc 全局错误捕捉中间件
func RecoverMiddleWareFunc(logger logit.Logger) ghttp.MiddleWareFunc {
	return func(ctx context.Context, w ghttp.Writer, req ghttp.Request, next ghttp.MiddleWareQueue) bool {
		defer func() {
			if err := recover(); err != nil {
				trace := make([]byte, 4096)
				runtime.Stack(trace[:], false)
				title := fmt.Sprintf("panic:%v", err)

				// 堆栈
				traceStr := string(trace)
				logFields := []logit.Field{
					logit.String("panic_trace", traceStr),
				}
				logger.Error(ctx, title, logFields...)
				// go helpers.HelixNotice(ctx, title)

				// json 返回给请求方
				var resp ghttp.Response
				resp, ok := err.(ghttp.Response)
				if !ok {
					resp = helpers.NewException(ctx, "COMMON_ERR")
				}

				_ = resp.WriteTo(w)
				return
			}
		}()

		return next.Next(ctx, w, req)
	}
}

// AuthMiddleWareFunc 用户验证中间件
func AuthMiddleWareFunc(logger logit.Logger) ghttp.MiddleWareFunc {
	if logger == nil {
		panic("logger is nil")
	}
	return func(ctx context.Context, w ghttp.Writer, req ghttp.Request, next ghttp.MiddleWareQueue) bool {
		// 登录验证
		var userInfo models.UserInfo
		if env.RunMode() != env.RunModeDebug {
			var userRaw models.User
			var err error
			// iam
			userRaw, _ = iamLoginFrontEnd(ctx, req)

			if userRaw.RealID <= 0 && env.RunMode() == env.RunModeTest {
				// qa环境增加一个线上uc账户的登录
				userRaw, _ = ucLogin(ctx, w, req)
			}

			// github
			if userRaw.RealID <= 0 {
				userRaw, _ = gitLogin(ctx, w, req)
			}

			// check
			if userRaw.RealID <= 0 {
				panic(helpers.FailReturn(ctx, helpers.LoginErrorCode, "not logged in"))
			}

			// 查询记录
			user, err := userRaw.GetUserByRealID(ctx, int64(userRaw.RealID), userRaw.UserID, int64(userRaw.Source))
			if err != nil {
				panic(helpers.FailReturn(ctx, helpers.DBErrorCode, "data error, please repeat"))
			}
			if user.ID == 0 && userRaw.UserID == userRaw.UserDomainID {
				// 如果是主账户，查询是否有之前无IAM信息的用户
				userNew, err := getUserByRealId(ctx, int64(userRaw.RealID), "")
				if err != nil {
					panic(helpers.FailReturn(ctx, helpers.DBErrorCode, "data error, please repeat"))
				}
				if userNew.ID != 0 && userNew.UserDomainID == "" {
					user = userNew
				}
			}
			if user.ID == 0 {
				user, err = userRaw.Add(ctx, userRaw)
				if err != nil {
					panic(helpers.FailReturn(ctx, helpers.DBErrorCode, "data error, please repeat"))
				}
			}

			// 修改信息
			user.DisplayName = userRaw.DisplayName
			user.Username = userRaw.Username
			user.UserDomainID = userRaw.UserDomainID
			user.UserID = userRaw.UserID
			// 普通用户如果开通了chpc，自动转为商业用户
			if user.Type == uint64(models.UserTypeNormal) && chpc.IsOpenCHPCService(user.UserDomainID) {
				user.Type = uint64(models.UserTypeCharge)
			}
			user.LoginTime = time.Now()
			_ = user.Save(ctx)

			userInfo = models.UserInfo{
				UserID:          int64(user.ID),
				RealID:          int64(user.RealID),
				IAMUserID:       user.UserID,
				IAMUserDomainID: user.UserDomainID,
				DisplayName:     user.DisplayName,
				Type:            int64(user.Type),
				Source:          int64(user.Source),
			}

			// 测试账号特殊处理
			if user.NeedSwitch > 0 {
				userInfo.RealID = int64(models.DefaultInnerRealID)
			}
		} else {
			// mock 数据
			userM := models.User{}
			userR, _ := userM.GetUserByID(ctx, int64(1))
			userR.LoginTime = time.Now()
			_ = userR.Save(ctx)

			userInfo = models.UserInfo{
				UserID:      int64(userR.ID),
				RealID:      int64(userR.RealID),
				Type:        int64(userR.Type),
				Source:      int64(userR.Source),
				DisplayName: "大老虎",
			}
		}

		newCtx := context.WithValue(ctx, "user_info", userInfo)
		return next.Next(newCtx, w, req)
	}
}

var innerIpMap = map[string][]string{
	env.RunModeRelease: {"*************"},
	env.RunModeTest:    {},
	env.RunModeDebug:   {"************"},
}

func VerifyInnerFunc(logger logit.Logger) ghttp.MiddleWareFunc {
	if logger == nil {
		panic("logger is nil")
	}
	return func(ctx context.Context, w ghttp.Writer, req ghttp.Request, next ghttp.MiddleWareQueue) bool {
		host, _, _ := net.SplitHostPort(req.RemoteAddr())
		ipList := innerIpMap[env.RunMode()]
		if !helpers.CheckInSliceByString(host, ipList) {
			panic(helpers.FailReturn(ctx, helpers.CommonErrorCode, "请求非法"))
		}

		return next.Next(ctx, w, req)
	}
}

// permissionMiddleWareFunc 用户验证中间件
func VerifyMiddleWareFunc(logger logit.Logger) ghttp.MiddleWareFunc {
	if logger == nil {
		panic("logger is nil")
	}
	return func(ctx context.Context, w ghttp.Writer, req ghttp.Request, next ghttp.MiddleWareQueue) bool {
		userInfo := ctx.Value("user_info").(models.UserInfo)

		uri := req.RequestURI()
		var err error
		endIndex := strings.Index(uri, "?")
		if endIndex > 0 {
			uri = uri[0:endIndex]
		}

		// 请求日志
		logNew := models.RequestLog{
			Uri:    uri,
			UserId: uint64(userInfo.UserID),
		}
		_, _ = logNew.Add(ctx, logNew)

		// 权限校验
		taskType := uriToTaskType[uri]
		if taskType > 0 {
			ctx, err = controllers.CheckUserPermission(ctx, int64(taskType), true)
			if err != nil {
				panic(helpers.FailReturn(ctx, helpers.LogicErrorCode, err.Error()))
			}
		}

		return next.Next(ctx, w, req)
	}
}

var uriToTaskType = map[string]int{
	"/submit/antibody/forecast":         models.TaskTypeAntibody,
	"/submit/covid/forecast":            models.TaskTypeNewCrown,
	"/submit/protein/mutation/forecast": models.TaskTypeProteinMutation,
	"/submit/admet/forecast":            models.TaskTypeAdmet,
	// "/submit/self/admet/forecast":       models.TaskTypeSelfAdmet,
	"/submit/mol/docking/forecast":             models.TaskTypeVirtualFilter,
	"/submit/helixvs/forecast":                 models.TaskTypeVirtualVS,
	"/submit/mol/act/struct/forecast":          models.TaskTypeMolActStruct,
	"/submit/mol/act/serial/forecast":          models.TaskTypeMolActivity,
	"/submit/mol/formation/forecast":           models.TaskTypeMolFormation,
	"/submit/protein/func/forecast":            models.TaskTypeProteinFunc,
	"/submit/compound/forecast":                models.TaskTypeCompound,
	"/submit/linear/fold/forecast":             models.TaskTypeLinearFold,
	"/submit/linear/partition/forecast":        models.TaskTypeLinearPartition,
	"/submit/exact/drug/forecast":              models.TaskTypeExactDrug,
	"/submit/combine/forecast":                 models.TaskTypeDoubleDrug,
	"/submit/rna/forecast":                     models.TaskTypeLinearDesign,
	"/submit/protein/forecast":                 models.TaskTypeProtein,
	"/submit/protein/single/forecast":          models.TaskTypeProteinSingle,
	"/submit/utr/forecast":                     models.TaskTypeUTR,
	"/submit/protein/complex/forecast":         models.TaskTypeProteinComplex,
	"/api/submit/admet":                        models.TaskTypeAdmet,
	"/submit/mmgbsa/forecast":                  models.TaskTypeMMGBSA,
	"/submit/helix/dock/forecast":              models.TaskTypeHelixDock,
	"/submit/kykt/forecast":                    models.TaskTypeKYKT,
	"/submit/mol/frame/forecast":               models.TaskTypeHelixVSSyn,
	"/batch/submit/helixfoldaa/forecast":       models.TaskTypeHelixFoldAA,
	"/submit/protein/relaxation/forecast":      models.TaskTypeProteinRelaxation,
	"/submit/hf3agab/forecast":                 models.TaskTypeHF3Agab,
	"/batch/submit/miniproteindesign/forecast": models.TaskTypeMiniProteinDesign,
	"/batch/submit/antibodydesign/forecast":    models.TaskTypeAntibodyDesign,

	//"/api/submit/helixfold3": models.TaskTypeHelixFoldAA,
	"/api/submit/kykt": models.TaskTypeKYKT,
}

// 清理cookie
func clearCookie(w ghttp.Writer, cookieName string) {
	cookieKey := http.Cookie{
		Name:   cookieName,
		Value:  "",
		MaxAge: -1,
		Domain: ".baidu.com",
		Path:   "/",
	}
	http.SetCookie(w, &cookieKey)
}

// AuthMiddleWareFunc 用户验证中间件
func Auth2MiddleWareFunc(logger logit.Logger) ghttp.MiddleWareFunc {
	if logger == nil {
		panic("logger is nil")
	}
	return func(ctx context.Context, w ghttp.Writer, req ghttp.Request, next ghttp.MiddleWareQueue) bool {
		// 登录验证
		var userInfo models.UserInfo
		if env.RunMode() != env.RunModeDebug {
			// iam
			userRaw, _ := iamLoginFrontEnd(ctx, req)

			// qa环境增加一个线上uc账户的登录
			if userRaw.RealID <= 0 && env.RunMode() == env.RunModeTest {
				userRaw, _ = ucLogin(ctx, w, req)
			}

			// github
			if userRaw.RealID <= 0 {
				userRaw, _ = gitLogin(ctx, w, req)
			}

			// check
			if userRaw.RealID > 0 {
				user, err := userRaw.GetUserByRealID(ctx, int64(userRaw.RealID), userRaw.UserID, int64(userRaw.Source))
				if err != nil {
					panic(helpers.FailReturn(ctx, helpers.DBErrorCode, "data error, please repeat"))
				}
				if user.ID == 0 {
					user, err = userRaw.Add(ctx, userRaw)
					if err != nil {
						panic(helpers.FailReturn(ctx, helpers.DBErrorCode, "data error, please repeat"))
					}
				}

				user.LoginTime = time.Now()
				_ = user.Save(ctx)
				userInfo = models.UserInfo{
					UserID:      int64(user.ID),
					RealID:      int64(user.RealID),
					DisplayName: user.DisplayName,
					Type:        int64(user.Type),
					Source:      int64(user.Source),
				}

				// 测试账号特殊处理
				if user.NeedSwitch > 0 {
					userInfo.RealID = int64(models.DefaultInnerRealID)
				}
			}
		} else {
			// mock 数据
			userM := models.User{}
			userR, _ := userM.GetUserByID(ctx, int64(1))
			userR.LoginTime = time.Now()
			_ = userR.Save(ctx)

			userInfo = models.UserInfo{
				UserID:      int64(userR.ID),
				RealID:      int64(userR.RealID),
				Type:        int64(userR.Type),
				Source:      int64(userR.Source),
				DisplayName: "大老虎",
			}
		}

		newCtx := context.WithValue(ctx, "user_info", userInfo)
		return next.Next(newCtx, w, req)
	}
}

// ApiAuthMiddleWareFunc ApiAuthMiddleWareFunc 是一个实现了ghttp.MiddleWareFunc接口的函数，用于进行API认证中间件的功能。
// 该中间件会根据请求头中的"X-dev-origin-user-id"和"X-dev-user-account-type"来获取用户信息，并将其存储在context中。
// 如果运行模式不是debug模式，则会从header中获取信息，否则会使用mock数据。
// 如果获取到的用户信息无效或者用户类型不合法，则会panic。
// 参数logger是一个logit.Logger类型的日志记录器，如果为nil，则会panic。
// 返回值是bool类型，表示是否继续执行下一个中间件或者请求处理函数。
func ApiAuthMiddleWareFunc(logger logit.Logger) ghttp.MiddleWareFunc {
	if logger == nil {
		panic("logger is nil")
	}
	return func(ctx context.Context, w ghttp.Writer, req ghttp.Request, next ghttp.MiddleWareQueue) bool {
		// 登录验证
		var userInfo models.UserInfo
		if env.RunMode() != env.RunModeDebug {
			// IAM登录验证
			userRaw, err := iamLogin(ctx, w, req)
			if err == nil {
				userInfo = models.UserInfo{
					UserID:          int64(userRaw.ID),
					RealID:          int64(userRaw.RealID),
					IAMUserID:       userRaw.UserID,
					IAMUserDomainID: userRaw.UserDomainID,
					DisplayName:     userRaw.DisplayName,
					Type:            int64(userRaw.Type),
					Source:          int64(userRaw.Source),
				}
				newCtx := context.WithValue(ctx, "user_info", userInfo)
				return next.Next(newCtx, w, req)
			}

			// IAM验证失败 回落到Console认证
			// 从 header 获取信息
			accountId := req.HeaderDefault("X-dev-origin-user-id", "")
			accountType := req.HeaderDefault("X-dev-user-account-type", "")
			if accountId == "" || accountType == "" {
				appId := req.HeaderDefault("appid", "")
				if appId == "" {
					panic(helpers.FailReturn(ctx, helpers.LogicErrorCode, "非法请求"))
				}

				// 去console取用户信息
				appIdInt, _ := strconv.Atoi(appId)
				accountInfo, err := services.GetAccountInfo(ctx, int64(appIdInt))
				if err != nil {
					panic(helpers.FailReturn(ctx, helpers.LogicErrorCode, "data error，please repeat"))
				}

				accountId = strconv.Itoa(int(accountInfo.RealId))
				accountType = accountInfo.AccountType
			}
			if accountType != models.AccountPassType && accountType != models.AccountUCType {
				panic(helpers.FailReturn(ctx, helpers.LogicErrorCode, "user type legal"))
			}
			source := models.SourceStringToInt[accountType]
			// 获取用户信息
			userM := models.User{}
			realId, _ := strconv.Atoi(accountId)
			userRaw, _ = userM.GetUserByRealID(ctx, int64(realId), "", int64(source))
			if userRaw.ID <= 0 {
				panic(helpers.FailReturn(ctx, helpers.LogicErrorCode, "user legal"))
			}

			// 修改登录时间
			userRaw.LoginTime = time.Now()
			_ = userRaw.Save(ctx)

			userInfo = models.UserInfo{
				UserID:      int64(userRaw.ID),
				RealID:      int64(userRaw.RealID),
				DisplayName: userRaw.DisplayName,
				Type:        int64(userRaw.Type),
				Source:      int64(userRaw.Source),
			}

			// 测试账号特殊处理
			if userRaw.NeedSwitch > 0 {
				userInfo.RealID = int64(models.DefaultInnerRealID)
			}
		} else {
			// mock 数据
			userM := models.User{}
			userR, _ := userM.GetUserByID(ctx, int64(1))
			userR.LoginTime = time.Now()
			_ = userR.Save(ctx)

			userInfo = models.UserInfo{
				UserID:      int64(userR.ID),
				RealID:      int64(userR.RealID),
				Type:        int64(userR.Type),
				Source:      int64(userR.Source),
				DisplayName: "大老虎",
			}
		}

		newCtx := context.WithValue(ctx, "user_info", userInfo)
		return next.Next(newCtx, w, req)
	}
}
