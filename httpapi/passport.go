package httpapi

import (
	"context"
	"encoding/json"

	"icode.baidu.com/baidu/gdp/env"
	"icode.baidu.com/baidu/gdp/ghttp"
	"icode.baidu.com/baidu/gdp/passport"
	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/library/redis"
	"icode.baidu.com/helix_web/models"
)

const (
	passportTpl    = "paddlehelix"
	passCookieName = "BDUSS"
)

// passLogin 登录功能，使用百度passport进行登录，返回用户信息和错误信息
func passLogin(ctx context.Context, w ghttp.Writer, req ghttp.Request) (models.User, error) {
	cookie, ok := req.Cookie(passCookieName)
	if !ok || cookie.Value == "" {
		return models.User{}, nil
	}

	// 读缓存
	cacheKey := redis.PassportUserPrefix + cookie.Value
	cacheData := redis.Get(ctx, cacheKey)
	if cacheData != "" {
		var userData models.User
		_ = json.Unmarshal([]byte(cacheData), &userData)
		if userData.RealID > 0 {
			return userData, nil
		}
	}

	err := passport.Init(env.ConfDir() + "/passport.toml")
	if err != nil {
		helpers.LogError(ctx, err)
		return models.User{}, nil
	}

	// 生成authToken
	tplConf, err := passport.GetTplConfig(passportTpl)
	if err != nil {
		helpers.LogError(ctx, err)
		return models.User{}, nil
	}
	authToken := NewAuthToken(tplConf.Otp.Key, uint32(tplConf.Otp.ApID))

	// 发起验证
	sessionReq := passport.SessionReq{
		ClientIP:  []byte(req.RemoteAddr()),
		Bduss:     cookie.Value,
		Tpl:       passportTpl,
		AuthToken: authToken,
	}
	sessionResp, err := passport.Session(ctx, &sessionReq)
	if err != nil {
		clearCookie(w, passCookieName)
		return models.User{}, nil
	}

	// 判断获取数据是否正确
	if sessionResp.Status != passport.SessionRetSuccess {
		clearCookie(w, passCookieName)
		return models.User{}, nil
	}

	// 数据组装
	username := sessionResp.UserName
	displayName := sessionResp.DisplayName
	if username == "" {
		username = displayName
	}
	if username == "" {
		username = sessionResp.SecureMobile
		displayName = sessionResp.SecureMobile
	}
	user := models.User{
		RealID:      uint64(sessionResp.UserID),
		Username:    username,
		DisplayName: sessionResp.DisplayName,
		Phone:       sessionResp.SecureMobile,
		Email:       sessionResp.SecureEmail,
		Type:        uint64(models.UserTypeNormal),
		Source:      uint64(models.SourcePassport),
	}

	// 从Cookie获取用户账号ID
	accountIDCookie, ok := req.Cookie("accountId")
	if !ok || len(accountIDCookie.Value) <= 0 {
		userStr, _ := json.Marshal(user)
		redis.Set(ctx, cacheKey, userStr, redis.PassportUserTime)
		return user, nil
	}
	accountID := accountIDCookie.Value

	// 如果解析成功，表示的确是IAM用户，解析出对应的PassportID、IAMAccountID、UCID映射
	mapping, err := iamGetUserInfo(ctx, accountID)
	if err != nil {
		userStr, _ := json.Marshal(user)
		redis.Set(ctx, cacheKey, userStr, redis.PassportUserTime)
		return user, nil
	}

	// 如果是当前登录用户，则更新用户信息
	if mapping.PassportID == sessionResp.UserID {
		user.UserID = accountID
		user.UserDomainID = accountID
	}

	userStr, _ := json.Marshal(user)
	redis.Set(ctx, cacheKey, userStr, redis.PassportUserTime)
	return user, nil
}
