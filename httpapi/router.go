package httpapi

import (
	"context"

	"icode.baidu.com/baidu/gdp/env"
	"icode.baidu.com/baidu/gdp/ghttp"
	"icode.baidu.com/baidu/gdp/ghttp/pprof"

	"icode.baidu.com/helix_web/controllers"
	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/library/resource"
)

// Router 获取web路由
func Router(ser *ghttp.DefaultServer) ghttp.Router {
	logger := resource.LoggerService

	// 若http server 内部出现异常，将通过此logger打印日志
	ghttp.DefaultLogger = logger

	router := ghttp.NewRouter()
	router.SetLogger(logger)

	if ser.WriteTimeout > 0 {
		// 若server有设置WriteTimeout，则整体加上超时控制
		// 1.日志中可以打印出由于server超时导致的504异常
		// 2.业务逻辑可以更及时的终止运行
		router.Use(ghttp.NewTimeoutMiddleWareFunc(ser.WriteTimeout, nil))
	}

	// 注册日志中间件，初始化日志功能，打印访问日志(access_log)
	// 若需要对日志字段进行调整，请修改这里
	router.Use(ghttp.NewLogMiddleWareFunc(logger, ghttp.DefaultServerLogFields))

	// 注册panic Recover中间件，可以处理handlerFunc里 出现的panic
	// 需要注意的是，若是使用go xxx()自己新开启的协程，是不能recover的
	router.Use(RecoverMiddleWareFunc(logger))

	// 监控
	registerPprof(router)

	// 不需要验证的路由
	router.HandleFunc("any", "/", func(ctx context.Context, req ghttp.Request) ghttp.Response {
		return helpers.SuccReturn(ctx, nil)
	})
	router.HandleFunc("post", "/feedbar/show", controllers.GetFeedbarShow)
	router.HandleFunc("post", "/achievement/show", controllers.GetAchievementShow)
	router.HandleFunc("post", "/serial/list", controllers.GetSerialList)
	router.HandleFunc("post", "/captcha/get", controllers.GetCaptcha)
	router.HandleFunc("post", "/feedback/submit", controllers.SubmitFeedback)
	router.HandleFunc("post", "/apply/submit", controllers.SubmitApply)
	router.HandleFunc("post", "/protein/forecast/cameo", controllers.SubmitProteinForCameo)
	router.HandleFunc("post", "/protein/forecast/casp", controllers.SubmitProteinForCasp)
	router.HandleFunc("post", "/drug/search", controllers.SearchDrug)
	router.HandleFunc("post", "/protein/search", controllers.SearchProtein)
	router.HandleFunc("post", "/email/send", controllers.SendEmail)
	router.HandleFunc("post", "/msg/subscribe", controllers.SubscribeMsg)
	router.HandleFunc("post", "/msg/unsubscribe", controllers.UnSubscribeMsg)
	router.HandleFunc("post", "/github/verify", controllers.VerifyGithub)

	// 反馈
	router.HandleFunc("post", "/user/portrait/submit", controllers.SubmitUserPortrait, Auth2MiddleWareFunc(logger))
	router.HandleFunc("post", "/comment/submit", controllers.SubmitComment, Auth2MiddleWareFunc(logger))
	router.HandleFunc("post", "/portrait/close", controllers.ClosePortrait, Auth2MiddleWareFunc(logger))

	// 3.0 首页
	router.HandleFunc("post", "/dot/data", controllers.DataDot, Auth2MiddleWareFunc(logger))
	router.HandleFunc("post", "/cooperation/submit", controllers.SubmitCoop, Auth2MiddleWareFunc(logger))
	router.HandleFunc("post", "/overall/list", controllers.GetOverallList)
	router.HandleFunc("post", "/skill/list", controllers.GetSkillList)
	router.HandleFunc("post", "/bos/url", controllers.GetBosUrl, Auth2MiddleWareFunc(logger))

	// 内部接口
	router.HandleFunc("post", "/inner/bill/list", controllers.GetBillList, ghttp.NewInternalIPMiddleWareFunc())

	// 需要验证登录的路由
	authRouter := router.Group("", AuthMiddleWareFunc(logger), VerifyMiddleWareFunc(logger))
	authRouter.HandleFunc("post", "/user/info", controllers.GetUserInfo)
	authRouter.HandleFunc("post", "/user/task/info", controllers.GetUserTaskInfo)
	authRouter.HandleFunc("post", "/user/notify", controllers.GetUserNotify)
	authRouter.HandleFunc("post", "/bos/auth", controllers.GetBosAuth)
	authRouter.HandleFunc("post", "/train/list", controllers.GetTaskList)
	authRouter.HandleFunc("post", "/task/list", controllers.GetTaskList)
	authRouter.HandleFunc("post", "/task/info", controllers.GetTaskInfo)
	authRouter.HandleFunc("post", "/task/infos", controllers.GetTaskInfos)
	authRouter.HandleFunc("post", "/task/del", controllers.DelTask)
	authRouter.HandleFunc("post", "/task/cancel", controllers.CancelTask)
	authRouter.HandleFunc("post", "/task/rename", controllers.RenameTask)
	authRouter.HandleFunc("post", "/submit/train", controllers.SubmitTrain)
	authRouter.HandleFunc("post", "/history/train/list", controllers.GetHistoryTrainList)
	authRouter.HandleFunc("post", "/task/read", controllers.SetTaskRead)
	authRouter.HandleFunc("post", "/flow/list", controllers.GetFlowList)
	authRouter.HandleFunc("post", "/result/search", controllers.SearchResult)
	authRouter.HandleFunc("post", "/result/download", controllers.DownloadResult)

	// 预处理
	authRouter.HandleFunc("post", "/submit/pretreat", controllers.SubmitPretreat)
	authRouter.HandleFunc("post", "/pretreat/info", controllers.GetPretreatInfo)

	// 蛋白相互作用
	authRouter.HandleFunc("post", "/submit/antibody/forecast", controllers.SubmitAntibody)
	authRouter.HandleFunc("post", "/submit/covid/forecast", controllers.SubmitCovid)
	authRouter.HandleFunc("post", "/submit/protein/mutation/forecast", controllers.SubmitProteinMutation)

	// 新药研发
	authRouter.HandleFunc("post", "/submit/admet/forecast", controllers.SubmitAdmet)
	authRouter.HandleFunc("post", "/submit/self/admet/forecast", controllers.SubmitSelfAdmet)
	authRouter.HandleFunc("post", "/submit/mol/docking/forecast", controllers.SubmitMolDocking)
	authRouter.HandleFunc("post", "/submit/helixvs/forecast", controllers.SubmitHelixVS)
	authRouter.HandleFunc("post", "/submit/mol/act/struct/forecast", controllers.SubmitMolActStruct)
	authRouter.HandleFunc("post", "/submit/mol/act/serial/forecast", controllers.SubmitMolActSerial)
	authRouter.HandleFunc("post", "/submit/mol/formation/forecast", controllers.SubmitMolFormation)
	authRouter.HandleFunc("post", "/submit/protein/func/forecast", controllers.SubmitProteinFunc)
	authRouter.HandleFunc("post", "/submit/compound/forecast", controllers.SubmitCompound)

	// 疫苗设计
	authRouter.HandleFunc("post", "/submit/linear/fold/forecast", controllers.SubmitLinearFold)
	authRouter.HandleFunc("post", "/submit/linear/partition/forecast", controllers.SubmitLinearPartition)

	// 精准医疗
	authRouter.HandleFunc("post", "/drug/batch/get", controllers.BatchGetDrug)
	authRouter.HandleFunc("post", "/submit/exact/drug/forecast", controllers.SubmitExactDrug)
	authRouter.HandleFunc("post", "/submit/combine/forecast", controllers.SubmitDoubleDrug)

	// 付费模块
	authRouter.HandleFunc("post", "/submit/rna/forecast", controllers.SubmitLinearDesign)
	authRouter.HandleFunc("post", "/submit/protein/forecast", controllers.SubmitProtein)
	authRouter.HandleFunc("post", "/submit/protein/single/forecast", controllers.SubmitProteinSingle)

	// 账户
	authRouter.HandleFunc("post", "/chpc/account/balance", controllers.GetAccountBalance)
	authRouter.HandleFunc("post", "/chpc/service/open", controllers.IsOpenCHPCService)
	authRouter.HandleFunc("post", "/account/info", controllers.GetAccountInfo)
	authRouter.HandleFunc("post", "/coupon/list", controllers.GetCouponList)
	authRouter.HandleFunc("post", "/coupon/cost/list", controllers.CouponCostList)
	authRouter.HandleFunc("post", "/coupon/send/list", controllers.CouponSendList)

	// 反馈
	authRouter.HandleFunc("post", "/task/comment", controllers.TaskComment)
	authRouter.HandleFunc("post", "/comment/award/info", controllers.CommentAwardInfo)

	// 5utr
	authRouter.HandleFunc("post", "/submit/utr/forecast", controllers.SubmitUTR)
	authRouter.HandleFunc("post", "/submit/protein/complex/forecast", controllers.SubmitProteinComplex)

	// 新增功能模块
	authRouter.HandleFunc("post", "/submit/helix/dock/forecast", controllers.SubmitHelixDock)
	authRouter.HandleFunc("post", "/submit/kykt/forecast", controllers.SubmitKYKT)
	authRouter.HandleFunc("post", "/submit/mmgbsa/forecast", controllers.SubmitMMGBSA)
	authRouter.HandleFunc("post", "/submit/mol/frame/forecast", controllers.SubmitHelixVSSyn)
	authRouter.HandleFunc("post", "/batch/submit/helixfoldaa/forecast", controllers.BatchSubmitHelixFold3)
	authRouter.HandleFunc("post", "/check/helixfoldaa/refstructure/forecast", controllers.CheckHelixFold3RefStructure)
	authRouter.HandleFunc("post", "/submit/protein/relaxation/forecast", controllers.SubmitProteinRelaxation)
	authRouter.HandleFunc("post", "/submit/hf3agab/forecast", controllers.SubmitHF3Agab)
	authRouter.HandleFunc("post", "/batch/submit/hf3agab/forecast", controllers.BatchSubmitHF3Agab)
	authRouter.HandleFunc("post", "/batch/submit/miniproteindesign/forecast", controllers.BatchSubmitMiniProteinDesignTask)
	authRouter.HandleFunc("post", "/check/antibodydesign/forecast", controllers.CheckAntibodyDesignTask)
	authRouter.HandleFunc("post", "/batch/submit/antibodydesign/forecast", controllers.BatchSubmitAntibodyDesignTask)

	// 工具类接口
	authRouter.HandleFunc("post", "/query/ccd", controllers.QueryCcd)

	// api Router
	apiRouter := router.Group("", ApiAuthMiddleWareFunc(logger))
	apiRouter.HandleFunc("post", "/api/submit/admet", controllers.ApiSubmitTask)
	apiRouter.HandleFunc("post", "/api/submit/helixfold3", controllers.ApiSubmitHelixFold3Task)
	apiRouter.HandleFunc("post", "/api/submit/helixfold3/price", controllers.ApiQuerySubmitHelixFold3TaskPrice)
	apiRouter.HandleFunc("post", "/api/batch/submit/helixfold3", controllers.ApiBatchSubmitHelixFold3Task)
	apiRouter.HandleFunc("post", "/api/batch/submit/helixfold3/price", controllers.ApiQueryBatchSubmitHelixFold3TaskPrice)
	apiRouter.HandleFunc("post", "/api/submit/kykt", controllers.ApiSubmitKYKTTask)
	apiRouter.HandleFunc("post", "/api/task/info", controllers.ApiGetTaskInfo)
	apiRouter.HandleFunc("post", "/api/task/cancel", controllers.ApiCancelTask)
	apiRouter.HandleFunc("post", "/api/submit/hf3agab", controllers.ApiSubmitHF3AgabTask)
	apiRouter.HandleFunc("post", "/api/submit/hf3agab/price", controllers.ApiQuerySubmitHF3AgabTaskPrice)
	apiRouter.HandleFunc("post", "/api/batch/submit/hf3agab", controllers.ApiBatchSubmitHF3AgabTask)
	apiRouter.HandleFunc("post", "/api/batch/submit/hf3agab/price", controllers.ApiQueryBatchSubmitHF3AgabTaskPrice)
	return router
}

// registerPprof 注册/debug/pprof 的handler 用于性能分析
// 引入的pprof，会给标准库的DefaultServeMux 都注册上/debug/xxx系统的handler
// 开启http pprof, 该功能依赖server 配置一个比较大的WriteTimeout
// 需要 WriteTimeout > 30s
func registerPprof(router ghttp.Router) {
	if env.RunMode() == env.RunModeRelease {
		return
	}

	// 访问地址 http://ip:port/debug/pprof/
	pprof.RegisterPProf(router, ghttp.NewInternalIPMiddleWareFunc())
}
