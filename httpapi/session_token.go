package httpapi

import (
	"bytes"
	"encoding/binary"
	"encoding/hex"
	"fmt"
	"hash/crc32"
	"time"
)

func NewItemPacker() *ItemPacker {
	packer := new(ItemPacker)
	packer.buf = bytes.NewBuffer([]byte{})
	return packer
}

func binToHex(src []byte) string {
	dst := make([]byte, hex.EncodedLen(len(src)))
	hex.Encode(dst, src)
	return string(dst)
}

func Crc32(data string) uint32 {
	return crc32.ChecksumIEEE([]byte(data))
}

type ItemPacker struct {
	buf *bytes.Buffer
}

func (packer ItemPacker) Version(ver uint16) error {
	return binary.Write(packer.buf, binary.LittleEndian, ver)
}

func (packer ItemPacker) Time(ver uint32) error {
	return binary.Write(packer.buf, binary.LittleEndian, ver)
}

func (packer ItemPacker) Bytes(data []byte) error {
	return binary.Write(packer.buf, binary.LittleEndian, data)
}

func NewAuthToken(otpKey string, appid uint32) string{
	packer := NewItemPacker()
	packer.Version(1)
	packer.Version(1)
	now := time.Now().Unix()
	packer.Time(uint32(now))

	otp := fmt.Sprintf("%s%d%d",otpKey, now, appid)
	packer.Time(Crc32(otp))

	return binToHex(packer.buf.Bytes())
}