package httpapi

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"sync"
	"time"

	"icode.baidu.com/baidu/bce-iam/sdk-go/auth"
	"icode.baidu.com/baidu/gdp/ghttp"
	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/library/chpc"
	"icode.baidu.com/helix_web/library/redis"
	"icode.baidu.com/helix_web/models"

	iamhttp "icode.baidu.com/baidu/bce-iam/sdk-go/http"
	"icode.baidu.com/baidu/bce-iam/sdk-go/iam"
	"icode.baidu.com/baidu/gdp/conf"
	"icode.baidu.com/baidu/gdp/env"
)

const (
	CookieLoginAccID       = "bce-login-accountid"
	CookieClientCookies    = "bce-ctl-client-cookies"
	CookieClientHeaders    = "bce-ctl-client-headers"
	CookieClientParameters = "bce-ctl-client-parameters"

	ClientCookiesSeparator    = ","
	ClientHeadersSeparator    = ";"
	ClientParametersSeparator = "&"
)

type CheckSessionResponse struct {
	SessionContext models.SessionContext `json:"sessionContext"`
}
type iamConfig struct {
	Endpoint        string
	UserName        string
	Password        string
	BcePassEndpoint string
	AK              string
	SK              string
}

type IAMAccountNameResponse struct {
	LoginName   string `json:"loginName"`
	AccountName string `json:"account_name"`
}

type PassportIAMAccountMapping struct {
	DisplayName  string
	PassportID   int64
	IAMAccountID string
	UCID         int64
}

var (
	once                      sync.Once
	iamBceClientConfiguration iam.BceClientConfiguration
)

// initIAMConf 初始化IAM配置信息，只会在第一次调用时执行一次
// 返回值：error类型，表示初始化过程中可能出现的错误
func initIAMConf() error {
	var err error
	once.Do(func() {
		var iamConf iamConfig
		if err = conf.Parse(env.ConfDir()+"/iam.toml", &iamConf); err != nil {
		}

		if len(iamConf.Endpoint) <= 0 || len(iamConf.UserName) <= 0 || len(iamConf.Password) <= 0 {
			err = errors.New("iam.toml conf param invalid")
		}

		iamBceClientConfiguration = iam.BceClientConfiguration{
			Endpoint: iamConf.Endpoint,
			UserName: iamConf.UserName,
			Password: iamConf.Password,
			Version:  "/v3",
			Domain:   "Default",
			Retry:    &iam.NoRetryPolicy{},
		}
	})
	return err
}

// validateHttpRequest 验证HTTP请求是否合法，返回值为*iam.Token和error类型，包含用户信息
func validateHttpRequest(req *http.Request) (*iam.Token, error) {
	_ = initIAMConf()
	client := iam.NewBceClient(&iamBceClientConfiguration)
	// iam.Token contain User Detail Info
	// 		token.User.ID: 主用户时为账户ID，子用户时为子用户ID
	// 		token.User.Name：主用户时为root，子用户时为子用户名
	// 		token.User.Domain.ID: 账户ID(accountID)
	// 		token.User.Domain.Name: PASSPORT:**********
	return client.ValidatorRequest(req)
}

// iamGetUserInfo 获取用户信息，包括登录名、账号ID和显示名称
// ctx context.Context
// accountID string       // IAM账号ID
// 返回值：
// mapping PassportIAMAccountMapping   // 包含IAM账号ID、登录名、显示名称三个字段的结构体
// err error                           // 错误信息，如果没有错误则为nil
func iamGetUserInfo(ctx context.Context, accountID string) (mapping PassportIAMAccountMapping, err error) {
	_ = initIAMConf()
	mapping.IAMAccountID = accountID
	client := iam.NewBceClient(&iamBceClientConfiguration)
	tokenid, err := client.GetConsoleTokenID()
	if err != nil {
		return
	}
	bceRequest := &iam.BceRequest{}
	bceRequest.SetProtocol("https")
	bceRequest.SetHeader("X-Auth-Token", tokenid)
	bceRequest.SetHeader("X-Subuser-Support", "true")
	bceRequest.SetUri("/v3/accounts/name/" + accountID)
	bceRequest.SetMethod(iamhttp.GET)
	resp := &iam.BceResponse{}
	if err = client.SendRequest(bceRequest, resp, client.AgentConfiguration != nil); err != nil {
		return
	}
	jsonBody := &IAMAccountNameResponse{}
	if err = resp.ParseJsonBody(jsonBody); err != nil {
		return
	}
	mapping.DisplayName = jsonBody.LoginName
	if jsonBody.AccountName != "" {
		ids := strings.Split(jsonBody.AccountName, " ")
		for _, id := range ids {
			kv := strings.Split(id, ":")
			if len(kv) == 2 {
				if kv[0] == "PASSPORT" {
					mapping.PassportID, _ = strconv.ParseInt(kv[1], 10, 64)
				} else if kv[0] == "UC" {
					mapping.UCID, _ = strconv.ParseInt(kv[1], 10, 64)
				}
			}
		}
	}
	return
}

// IAM登录，适用于前端用户
func iamLoginFrontEnd(ctx context.Context, req ghttp.Request) (models.User, error) {
	// 会话验证
	resp, err := iamCheckSession(ctx, req)
	if err != nil {
		return models.User{}, err
	}
	// 获取realID
	source := models.SourcePassport
	var realID int64
	kv := strings.Split(resp.SessionContext.LoginUserInfo.BceAccountName, ":")
	if len(kv) == 2 {
		if kv[0] == "PASSPORT" {
			source = models.SourcePassport
			realID, err = strconv.ParseInt(kv[1], 10, 64)
			if err != nil {
				helpers.LogError(ctx, err)
				return models.User{}, nil
			}
		} else if kv[0] == "UC" {
			source = models.SourceUC
			realID, err = strconv.ParseInt(kv[1], 10, 64)
			if err != nil {
				helpers.LogError(ctx, err)
				return models.User{}, nil
			}
		}
	}
	if realID == 0 {
		return models.User{}, nil
	}
	// 尝试从缓存中获取用户信息
	cacheKey := redis.IAMUserPrefix + resp.SessionContext.LoginUserInfo.BceUserID
	userStr := redis.Get(ctx, cacheKey)
	if userStr != "" {
		user := models.User{}
		_ = json.Unmarshal([]byte(userStr), &user)
		return user, nil
	}

	// 从数据库获取用户信息
	userM := models.User{}
	userNew, err := getUserByRealId(ctx, realID, resp.SessionContext.LoginUserInfo.BceUserID)
	if err != nil {
		return models.User{}, err
	}
	if userNew.ID == 0 && resp.SessionContext.LoginUserInfo.BceUserID == resp.SessionContext.LoginUserInfo.BceAccountID {
		// 如果是主账户，查询是否有之前无IAM信息的用户
		user, err := getUserByRealId(ctx, realID, "")
		if err != nil {
			return models.User{}, err
		}
		if user.ID != 0 && user.UserDomainID == "" {
			userNew = user
		}
	}
	if userNew.ID == 0 {
		// 新用户
		userNew = models.User{
			Username:     resp.SessionContext.LoginUserInfo.LoginUsername,
			DisplayName:  resp.SessionContext.LoginUserInfo.DisplayName,
			RealID:       uint64(realID),
			UserID:       resp.SessionContext.LoginUserInfo.BceUserID,
			UserDomainID: resp.SessionContext.LoginUserInfo.BceAccountID,
			Type:         uint64(models.UserTypeNormal),
			Source:       uint64(source),
		}
		if chpc.IsOpenCHPCService(resp.SessionContext.LoginUserInfo.BceAccountID) {
			userNew.Type = uint64(models.UserTypeCharge)
		}
		userNew, err = userM.Add(ctx, userNew)
		if err != nil {
			return models.User{}, err
		}
	}
	if len(userNew.UserID) <= 0 { // 更新iam用户的userID、userDomainID
		userNew.UserID = resp.SessionContext.LoginUserInfo.BceUserID
		userNew.UserDomainID = resp.SessionContext.LoginUserInfo.BceAccountID
		if err = userNew.Save(ctx); err != nil {
			return models.User{}, err
		}
	}

	// 将用户信息写入缓存
	userBytes, _ := json.Marshal(userNew)
	redis.Set(ctx, cacheKey, string(userBytes), redis.IAMUserTime)
	return userNew, nil
}

// IAM登录，适用于API用户
func iamLogin(ctx context.Context, w ghttp.Writer, req ghttp.Request) (models.User, error) {
	// 根据ak、sk解析出iam token
	token, err := validateHttpRequest(req.HTTPRequest())
	if err != nil {
		return models.User{}, err
	}

	// 如果解析成功，表示的确是IAM用户，解析出对应的PassportID、IAMAccountID、UCID映射
	mapping, err := iamGetUserInfo(ctx, token.User.Domain.ID)
	if err != nil {
		return models.User{}, err
	}

	// 尝试从缓存中获取用户信息
	source := models.SourcePassport
	realID := mapping.PassportID
	if realID == 0 {
		source = models.SourceUC
		realID = mapping.UCID
	}
	if realID == 0 {
		return models.User{}, nil
	}
	cacheKey := redis.IAMUserPrefix + token.User.ID
	userStr := redis.Get(ctx, cacheKey)
	if userStr != "" {
		user := models.User{}
		_ = json.Unmarshal([]byte(userStr), &user)
		return user, nil
	}

	// 从数据库获取用户信息
	userM := models.User{}
	userNew, err := getUserByRealId(ctx, realID, token.User.ID)
	if err != nil {
		return models.User{}, err
	}
	if userNew.ID == 0 { // 新用户
		userNew = models.User{
			Username:     token.User.Name,
			DisplayName:  mapping.DisplayName,
			RealID:       uint64(realID),
			UserID:       token.User.ID,
			UserDomainID: token.User.Domain.ID,
			Type:         uint64(models.UserTypeNormal),
			Source:       uint64(source),
		}
		userNew, err = userM.Add(ctx, userNew)
		if err != nil {
			return models.User{}, err
		}
	}
	if len(userNew.UserID) <= 0 { // 更新iam用户的userID、userDomainID
		userNew.UserID = token.User.ID
		userNew.UserDomainID = token.User.Domain.ID
		err = userNew.Save(ctx)
		if err != nil {
			return models.User{}, err
		}
	}

	// 将用户信息写入缓存
	userBytes, _ := json.Marshal(userNew)
	redis.Set(ctx, cacheKey, string(userBytes), redis.IAMUserTime)
	return userNew, nil
}

// getUserByRealId 根据realID获取Passport或UC的用户信息，如果都没有找到则返回错误
func getUserByRealId(ctx context.Context, realID int64, userID string) (models.User, error) {
	userM := models.User{}

	// 查询Passport用户
	userNew, err := userM.GetUserByRealID(ctx, realID, userID, int64(models.SourcePassport))
	if err != nil {
		return models.User{}, err
	}
	if userNew.ID > 0 {
		return userNew, nil
	}

	// 查询UC用户
	userNew, err = userM.GetUserByRealID(ctx, realID, userID, int64(models.SourceUC))
	if err != nil {
		return models.User{}, err
	}
	return userNew, nil
}

func iamCheckSession(ctx context.Context, req ghttp.Request) (*CheckSessionResponse, error) {
	var iamConf iamConfig
	if err := conf.Parse(env.ConfDir()+"/iam.toml", &iamConf); err != nil {
		return nil, err
	}
	if len(iamConf.Endpoint) <= 0 || len(iamConf.UserName) <= 0 || len(iamConf.Password) <= 0 {
		return nil, errors.New("iam.toml conf param invalid")
	}
	// 获取服务号的ak/sk
	iamBceClientConfiguration = iam.BceClientConfiguration{
		Endpoint: iamConf.Endpoint,
		UserName: iamConf.UserName,
		Password: iamConf.Password,
		Version:  "/v3",
		Domain:   "Default",
		Retry:    &iam.NoRetryPolicy{},
	}
	iamClient := iam.NewBceClient(&iamBceClientConfiguration)
	accessKey, err := iamClient.GetAccessKey()
	if err != nil {
		return nil, err
	}
	// 用服务号ak/sk创建pass客户端
	bcePassClientConfiguration := iam.BceClientConfiguration{
		Endpoint: iamConf.BcePassEndpoint,
		Credentials: &auth.BceCredentials{
			AccessKeyId:     accessKey.Access,
			SecretAccessKey: accessKey.Secret,
		},
		Domain: "Default",
		Retry:  &iam.NoRetryPolicy{},
		SignOption: &auth.SignOptions{
			HeadersToSign: map[string]struct{}{
				strings.ToLower(iamhttp.HOST): {},
			},
			Timestamp:     time.Now().UTC().Unix(),
			ExpireSeconds: auth.DEFAULT_EXPIRE_SECONDS,
			IsBce:         true,
		},
	}
	passClient := iam.NewBceClient(&bcePassClientConfiguration)
	bceRequest := &iam.BceRequest{}
	// 从请求中获取请求头
	headers, err := getClientHeaders(req)
	if err != nil {
		return nil, err
	}
	for key, value := range headers {
		if value != "" {
			bceRequest.SetHeader(key, value)
		}
	}
	sessionId, exist := req.Cookie("bce-sessionid")
	if !exist {
		return nil, errors.New("sessionid not found")
	}
	bceRequest.SetUri("/v4/bce/session/" + sessionId.Value)
	bceRequest.SetMethod(iamhttp.GET)
	resp := &iam.BceResponse{}
	// 请求会话验证
	if err = passClient.SendRequest(bceRequest, resp, passClient.AgentConfiguration != nil); err != nil {
		fmt.Println("SendRequest error: ", err)
		return nil, err
	}
	sessionResp := &CheckSessionResponse{}
	if err = resp.ParseJsonBody(sessionResp); err != nil {
		return nil, err
	}
	fmt.Println("sessionResp: ", sessionResp)
	return sessionResp, nil
}

func getClientHeaders(req ghttp.Request) (map[string]string, error) {
	headers := make(map[string]string)
	headers["x-bce-client-ip"] = req.RemoteAddr()
	headers["x-bce-client-agent"], _ = req.Header("User-Agent")
	headers["x-bce-client-referer"], _ = req.Header("Referer")
	headers["x-bce-client-host"], _ = req.Header("Host")
	headers["x-bce-client-uri"] = req.RequestURI()
	headers["x-bce-client-method"] = req.HTTPRequest().Method
	headers["x-bce-iamsdk-version"] = iam.SDK_VERSION
	// 处理 Cookies
	var cookies []string
	if clientCookie, exist := req.Cookie(CookieClientCookies); exist {
		cookieValue, err := url.QueryUnescape(clientCookie.Value)
		if err != nil {
			return nil, fmt.Errorf("decode cookie value error: %w", err)
		}
		cookieValue = strings.Trim(cookieValue, `"`)
		cookieKeys := strings.Split(cookieValue, ClientCookiesSeparator)
		for _, key := range cookieKeys {
			if cookie, exist := req.Cookie(key); exist {
				value := cookie.Value
				if key == CookieLoginAccID {
					decodedValue, err := url.QueryUnescape(value)
					if err != nil {
						return nil, fmt.Errorf("decode cookie value error: %w", err)
					}
					value = decodedValue
				}
				if value != "null" {
					cookies = append(cookies, key+"="+url.QueryEscape(value))
				}
			}
		}
	}
	if len(cookies) > 0 {
		headers["x-bce-client-cookies"] = strings.Join(cookies, ClientHeadersSeparator)
	}

	// 处理 Headers
	var headersList []string
	if clientHeader, exist := req.Cookie(CookieClientHeaders); exist {
		cookieValue, err := url.QueryUnescape(clientHeader.Value)
		if err != nil {
			return nil, fmt.Errorf("decode cookie value error: %w", err)
		}
		headerKeys := strings.Split(cookieValue, ClientHeadersSeparator)
		for _, key := range headerKeys {
			if value, _ := req.Header(key); value != "" {
				headersList = append(headersList, key+"="+url.QueryEscape(value))
			}
		}
	}
	if len(headersList) > 0 {
		headers["x-bce-client-headers"] = strings.Join(headersList, ClientHeadersSeparator)
	}

	// 处理 Query Parameters
	var parametersList []string
	if clientParameter, exist := req.Cookie(CookieClientParameters); exist {
		cookieValue, err := url.QueryUnescape(clientParameter.Value)
		if err != nil {
			return nil, fmt.Errorf("decode cookie value error: %w", err)
		}
		parameterKeys := strings.Split(cookieValue, ClientParametersSeparator)
		for _, key := range parameterKeys {
			if value := req.Param(key); value != "" {
				if key == "ticket" {
					value = strings.ReplaceAll(value, " ", "+")
				}
				parametersList = append(parametersList, key+"="+url.QueryEscape(value))
			}
		}
	}
	if len(parametersList) > 0 {
		headers["x-bce-client-parameters"] = strings.Join(parametersList, ClientHeadersSeparator)
	}

	return headers, nil
}
