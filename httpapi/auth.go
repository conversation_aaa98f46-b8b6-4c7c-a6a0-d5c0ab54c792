package httpapi

import (
	"context"
	"fmt"

	"icode.baidu.com/baidu/gdp/codec"
	"icode.baidu.com/baidu/gdp/ghttp"
	"icode.baidu.com/baidu/gdp/net/ral"
	"icode.baidu.com/baidu/gdp/nshead"
)

const (
	RetSuccessCode = 0
)

type UserInfo struct {
	UserID        int64   `json:"uid"`             // 帐号userID
	UserName      string  `json:"uname"`           // 帐号userName，(UTF8格式，session服务原始返回字段为GBK格式，此处转换为UTF8格式)
	DisplayName   string  `json:"displayname"`     // 帐号displayName，(UTF8格式，session服务原始返回字段为GBK格式，此处转换为UTF8格式)
	SecureEmail   string  `json:"secureemail"`     // 帐号secureemail绑定邮箱
	SecureMobile  string  `json:"securemobil"`     // 帐号securemobil绑定手机
	LoginTime     int64   `json:"ltime"`           // 最后登录时间
	UpdateTime    int64   `json:"utime"`           // 最后更新时间
	AccessTime    int64   `json:"atime"`           // 最后访问时间
	Success       bool    `json:"success"`         // success
}

// AuthResp 获取 auth 数据响应体
type AuthResp struct {
	Code  int64      `json:"code"`
	Data  *UserInfo  `json:"data"`
}

// 验证用户登陆信息
func AuthUser(ctx context.Context, req ghttp.Request, bduss string) (*UserInfo, error) {
	ralReq := &ghttp.RalPostRequest {
		PostData: map[string]string{"method":"bos/auth", "bduss":bduss},
		Encoder: codec.JSONEncoder,
	}
	ralReq.Path = "/dunker-pro/api"
	ralReq.Header = map[string][]string{
		"Log-Id": {req.LogID()},
		"Content-Type": {"application/json;charset=UTF-8"},
	}

	// 请求参数
	//reqBody, _ := json.Marshal(map[string]string{"method":"bos/auth"})
	//ralReq := &ghttp.RalRequest {
	//	Method: "POST",
	//	Path:   "/dunker-pro/api",
	//	Body:   bytes.NewReader([]byte(reqBody)),
	//}

	ralResp := &ghttp.RalResponse {
		Data:    &AuthResp{Code: -1},
		Decoder: codec.JSONDecoder,
	}
	err := ral.RAL(ctx, "user", ralReq, ralResp)
	if err != nil {

	}

	respData := ralResp.Data.(*AuthResp)
	if respData.Code != RetSuccessCode {

	}
	fmt.Println(ralResp.Data)

	return respData.Data, nil
}

func AuthUserRPC(ctx context.Context, req ghttp.Request, bduss string) (*UserInfo, error) {
	ralReq := &nshead.RalRequest{
		Encoder: codec.JSONEncoder,
		Body: map[string]interface{}{
			"method": "bos/auth",
		},
	}

	ralResp := &nshead.RalResponse{
		Decoder: codec.JSONDecoder,
		Data:    &AuthResp{},
	}

	if err := ral.RAL(ctx, "demo_nshead", ralReq, ralResp); err != nil {
		panic(err)
	}

	fmt.Println(ralResp.Head)
	respData := ralResp.Data.(*AuthResp)
	fmt.Println(respData.Data)
	return respData.Data, nil
}