package httpapi

import (
	"context"
	"strconv"

	"icode.baidu.com/baidu/gdp/ghttp"
	"icode.baidu.com/helix_web/library/redis"
	"icode.baidu.com/helix_web/models"
)

const gitCookieName = "BDUSSGIT"

func gitLogin(ctx context.Context, w ghttp.Writer, req ghttp.Request) (models.User, error) {
	cookie, ok := req.<PERSON><PERSON>(gitCookieName)
	if !ok || cookie.Value == "" {
		return models.User{}, nil
	}

	userIdStr := redis.Get(ctx, redis.UserCookiePrefix+cookie.Value)
	if userIdStr == "" {
		return models.User{}, nil
	}

	userM := models.User{}
	userId, _ := strconv.Atoi(userIdStr)
	user, _ := userM.GetUserByID(ctx, int64(userId))
	return user, nil
}
