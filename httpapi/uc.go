package httpapi

import (
	"context"
	"net/http"

	"icode.baidu.com/baidu/gdp/ghttp"
	"icode.baidu.com/helix_web/library/uc"
	"icode.baidu.com/helix_web/library/uc/config"
	"icode.baidu.com/helix_web/models"
)

const (
	appkey = "paddlehelix.baidu.com"
	appId  = 747

	ucCookieName = "SIGNIN_UC"
)

// ucLogin ucLogin 登录函数，从请求中获取用户信息并返回用户对象和错误信息
func ucLogin(ctx context.Context, w http.ResponseWriter, req ghttp.Request) (models.User, error) {
	cookie, ok := req.<PERSON>ie(ucCookieName)
	if !ok || cookie.Value == "" {
		return models.User{}, nil
	}

	casInfo := config.NewCasInfo()
	casInfo.AddServer("http://smartbns.group.uc-cas.darwin.all.serv:6070/uc-cas/core-services/")
	casInfo.AddServer("http://smartbns.group.uc-cas.darwin.all.serv:6071/uc-cas/core-services/")
	casInfo.AddServer("http://smartbns.group.uc-cas.darwin.all.serv:6072/uc-cas/core-services/")
	casInfo.AutoRedirect = false

	casInfo.Appid = appId
	casInfo.AppKey = appkey
	client := uc.NewCasClient(req.HTTPRequest(), &w, casInfo)
	resp := client.ValiateSt()
	if resp == nil {
		return models.User{}, nil
	}

	user := models.User{
		Username:    resp.Usename,
		DisplayName: resp.Ucname,
		RealID:      uint64(resp.Ucid),
		Type:        uint64(models.UserTypeNormal),
		Source:      uint64(models.SourceUC),
	}

	// 从Cookie获取用户账号ID
	accountIDCookie, ok := req.Cookie("accountId")
	if !ok || len(accountIDCookie.Value) <= 0 {
		return user, nil
	}
	accountID := accountIDCookie.Value

	// 如果解析成功，表示的确是IAM用户，解析出对应的PassportID、IAMAccountID、UCID映射
	mapping, err := iamGetUserInfo(ctx, accountID)
	if err != nil {
		return user, nil
	}

	// 如果是当前登录用户，则更新用户信息
	if mapping.UCID == int64(resp.Ucid) {
		user.UserID = accountID
		user.UserDomainID = accountID
	}

	return user, nil
}
