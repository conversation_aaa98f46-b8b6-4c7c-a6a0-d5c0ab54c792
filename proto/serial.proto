syntax = "proto3";

option go_package = "./generate/;proto";

import "common.proto";

message GetSmilesTokenCountRequest {
  string smiles = 1;
  uint64 count  = 2;
}

message GetSmilesTokenCountResponse {
  int64  error_code    = 1;
  string error_message = 2;
  uint64 count         = 3;
}

message GetEntitiesTokenCountRequest {
  repeated Entity entities = 1;
}

message GetEntitiesTokenCountResponse {
  int64  error_code    = 1;
  string error_message = 2;
  uint64 count         = 3;
}

service SerialService {
  rpc GetSmilesTokenCount (GetSmilesTokenCountRequest) returns (GetSmilesTokenCountResponse);
  rpc GetEntitiesTokenCount (GetEntitiesTokenCountRequest) returns (GetEntitiesTokenCountResponse);
}