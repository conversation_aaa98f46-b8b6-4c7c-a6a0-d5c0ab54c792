// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.0
// source: serial.proto

package proto

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	SerialService_GetSmilesTokenCount_FullMethodName   = "/SerialService/GetSmilesTokenCount"
	SerialService_GetEntitiesTokenCount_FullMethodName = "/SerialService/GetEntitiesTokenCount"
)

// SerialServiceClient is the client API for SerialService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type SerialServiceClient interface {
	GetSmilesTokenCount(ctx context.Context, in *GetSmilesTokenCountRequest, opts ...grpc.CallOption) (*GetSmilesTokenCountResponse, error)
	GetEntitiesTokenCount(ctx context.Context, in *GetEntitiesTokenCountRequest, opts ...grpc.CallOption) (*GetEntitiesTokenCountResponse, error)
}

type serialServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewSerialServiceClient(cc grpc.ClientConnInterface) SerialServiceClient {
	return &serialServiceClient{cc}
}

func (c *serialServiceClient) GetSmilesTokenCount(ctx context.Context, in *GetSmilesTokenCountRequest, opts ...grpc.CallOption) (*GetSmilesTokenCountResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetSmilesTokenCountResponse)
	err := c.cc.Invoke(ctx, SerialService_GetSmilesTokenCount_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serialServiceClient) GetEntitiesTokenCount(ctx context.Context, in *GetEntitiesTokenCountRequest, opts ...grpc.CallOption) (*GetEntitiesTokenCountResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetEntitiesTokenCountResponse)
	err := c.cc.Invoke(ctx, SerialService_GetEntitiesTokenCount_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SerialServiceServer is the server API for SerialService service.
// All implementations must embed UnimplementedSerialServiceServer
// for forward compatibility.
type SerialServiceServer interface {
	GetSmilesTokenCount(context.Context, *GetSmilesTokenCountRequest) (*GetSmilesTokenCountResponse, error)
	GetEntitiesTokenCount(context.Context, *GetEntitiesTokenCountRequest) (*GetEntitiesTokenCountResponse, error)
	mustEmbedUnimplementedSerialServiceServer()
}

// UnimplementedSerialServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedSerialServiceServer struct{}

func (UnimplementedSerialServiceServer) GetSmilesTokenCount(context.Context, *GetSmilesTokenCountRequest) (*GetSmilesTokenCountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSmilesTokenCount not implemented")
}
func (UnimplementedSerialServiceServer) GetEntitiesTokenCount(context.Context, *GetEntitiesTokenCountRequest) (*GetEntitiesTokenCountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetEntitiesTokenCount not implemented")
}
func (UnimplementedSerialServiceServer) mustEmbedUnimplementedSerialServiceServer() {}
func (UnimplementedSerialServiceServer) testEmbeddedByValue()                       {}

// UnsafeSerialServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SerialServiceServer will
// result in compilation errors.
type UnsafeSerialServiceServer interface {
	mustEmbedUnimplementedSerialServiceServer()
}

func RegisterSerialServiceServer(s grpc.ServiceRegistrar, srv SerialServiceServer) {
	// If the following call pancis, it indicates UnimplementedSerialServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&SerialService_ServiceDesc, srv)
}

func _SerialService_GetSmilesTokenCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSmilesTokenCountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SerialServiceServer).GetSmilesTokenCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SerialService_GetSmilesTokenCount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SerialServiceServer).GetSmilesTokenCount(ctx, req.(*GetSmilesTokenCountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SerialService_GetEntitiesTokenCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetEntitiesTokenCountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SerialServiceServer).GetEntitiesTokenCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SerialService_GetEntitiesTokenCount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SerialServiceServer).GetEntitiesTokenCount(ctx, req.(*GetEntitiesTokenCountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// SerialService_ServiceDesc is the grpc.ServiceDesc for SerialService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var SerialService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "SerialService",
	HandlerType: (*SerialServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetSmilesTokenCount",
			Handler:    _SerialService_GetSmilesTokenCount_Handler,
		},
		{
			MethodName: "GetEntitiesTokenCount",
			Handler:    _SerialService_GetEntitiesTokenCount_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "serial.proto",
}
