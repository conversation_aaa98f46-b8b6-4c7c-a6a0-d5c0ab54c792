// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.2
// 	protoc        v5.29.0
// source: serial.proto

package proto

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetSmilesTokenCountRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Smiles string `protobuf:"bytes,1,opt,name=smiles,proto3" json:"smiles,omitempty"`
	Count  uint64 `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
}

func (x *GetSmilesTokenCountRequest) Reset() {
	*x = GetSmilesTokenCountRequest{}
	mi := &file_serial_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetSmilesTokenCountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSmilesTokenCountRequest) ProtoMessage() {}

func (x *GetSmilesTokenCountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_serial_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSmilesTokenCountRequest.ProtoReflect.Descriptor instead.
func (*GetSmilesTokenCountRequest) Descriptor() ([]byte, []int) {
	return file_serial_proto_rawDescGZIP(), []int{0}
}

func (x *GetSmilesTokenCountRequest) GetSmiles() string {
	if x != nil {
		return x.Smiles
	}
	return ""
}

func (x *GetSmilesTokenCountRequest) GetCount() uint64 {
	if x != nil {
		return x.Count
	}
	return 0
}

type GetSmilesTokenCountResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ErrorCode    int64  `protobuf:"varint,1,opt,name=error_code,json=errorCode,proto3" json:"error_code,omitempty"`
	ErrorMessage string `protobuf:"bytes,2,opt,name=error_message,json=errorMessage,proto3" json:"error_message,omitempty"`
	Count        uint64 `protobuf:"varint,3,opt,name=count,proto3" json:"count,omitempty"`
}

func (x *GetSmilesTokenCountResponse) Reset() {
	*x = GetSmilesTokenCountResponse{}
	mi := &file_serial_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetSmilesTokenCountResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSmilesTokenCountResponse) ProtoMessage() {}

func (x *GetSmilesTokenCountResponse) ProtoReflect() protoreflect.Message {
	mi := &file_serial_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSmilesTokenCountResponse.ProtoReflect.Descriptor instead.
func (*GetSmilesTokenCountResponse) Descriptor() ([]byte, []int) {
	return file_serial_proto_rawDescGZIP(), []int{1}
}

func (x *GetSmilesTokenCountResponse) GetErrorCode() int64 {
	if x != nil {
		return x.ErrorCode
	}
	return 0
}

func (x *GetSmilesTokenCountResponse) GetErrorMessage() string {
	if x != nil {
		return x.ErrorMessage
	}
	return ""
}

func (x *GetSmilesTokenCountResponse) GetCount() uint64 {
	if x != nil {
		return x.Count
	}
	return 0
}

type GetEntitiesTokenCountRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Entities []*Entity `protobuf:"bytes,1,rep,name=entities,proto3" json:"entities,omitempty"`
}

func (x *GetEntitiesTokenCountRequest) Reset() {
	*x = GetEntitiesTokenCountRequest{}
	mi := &file_serial_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetEntitiesTokenCountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEntitiesTokenCountRequest) ProtoMessage() {}

func (x *GetEntitiesTokenCountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_serial_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEntitiesTokenCountRequest.ProtoReflect.Descriptor instead.
func (*GetEntitiesTokenCountRequest) Descriptor() ([]byte, []int) {
	return file_serial_proto_rawDescGZIP(), []int{2}
}

func (x *GetEntitiesTokenCountRequest) GetEntities() []*Entity {
	if x != nil {
		return x.Entities
	}
	return nil
}

type GetEntitiesTokenCountResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ErrorCode    int64  `protobuf:"varint,1,opt,name=error_code,json=errorCode,proto3" json:"error_code,omitempty"`
	ErrorMessage string `protobuf:"bytes,2,opt,name=error_message,json=errorMessage,proto3" json:"error_message,omitempty"`
	Count        uint64 `protobuf:"varint,3,opt,name=count,proto3" json:"count,omitempty"`
}

func (x *GetEntitiesTokenCountResponse) Reset() {
	*x = GetEntitiesTokenCountResponse{}
	mi := &file_serial_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetEntitiesTokenCountResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEntitiesTokenCountResponse) ProtoMessage() {}

func (x *GetEntitiesTokenCountResponse) ProtoReflect() protoreflect.Message {
	mi := &file_serial_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEntitiesTokenCountResponse.ProtoReflect.Descriptor instead.
func (*GetEntitiesTokenCountResponse) Descriptor() ([]byte, []int) {
	return file_serial_proto_rawDescGZIP(), []int{3}
}

func (x *GetEntitiesTokenCountResponse) GetErrorCode() int64 {
	if x != nil {
		return x.ErrorCode
	}
	return 0
}

func (x *GetEntitiesTokenCountResponse) GetErrorMessage() string {
	if x != nil {
		return x.ErrorMessage
	}
	return ""
}

func (x *GetEntitiesTokenCountResponse) GetCount() uint64 {
	if x != nil {
		return x.Count
	}
	return 0
}

var File_serial_proto protoreflect.FileDescriptor

var file_serial_proto_rawDesc = []byte{
	0x0a, 0x0c, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x0c,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x4a, 0x0a, 0x1a,
	0x47, 0x65, 0x74, 0x53, 0x6d, 0x69, 0x6c, 0x65, 0x73, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x6d,
	0x69, 0x6c, 0x65, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x6d, 0x69, 0x6c,
	0x65, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x77, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x53,
	0x6d, 0x69, 0x6c, 0x65, 0x73, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x22, 0x43, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x69, 0x65, 0x73,
	0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x23, 0x0a, 0x08, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x69, 0x65, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x07, 0x2e, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x52, 0x08, 0x65, 0x6e,
	0x74, 0x69, 0x74, 0x69, 0x65, 0x73, 0x22, 0x79, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x45, 0x6e, 0x74,
	0x69, 0x74, 0x69, 0x65, 0x73, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x32, 0xb9, 0x01, 0x0a, 0x0d, 0x53, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x12, 0x50, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x53, 0x6d, 0x69, 0x6c, 0x65, 0x73,
	0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1b, 0x2e, 0x47, 0x65, 0x74,
	0x53, 0x6d, 0x69, 0x6c, 0x65, 0x73, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x6d, 0x69,
	0x6c, 0x65, 0x73, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x56, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x45, 0x6e, 0x74, 0x69,
	0x74, 0x69, 0x65, 0x73, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1d,
	0x2e, 0x47, 0x65, 0x74, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x69, 0x65, 0x73, 0x54, 0x6f, 0x6b, 0x65,
	0x6e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e,
	0x47, 0x65, 0x74, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x69, 0x65, 0x73, 0x54, 0x6f, 0x6b, 0x65, 0x6e,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x13, 0x5a,
	0x11, 0x2e, 0x2f, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x2f, 0x3b, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_serial_proto_rawDescOnce sync.Once
	file_serial_proto_rawDescData = file_serial_proto_rawDesc
)

func file_serial_proto_rawDescGZIP() []byte {
	file_serial_proto_rawDescOnce.Do(func() {
		file_serial_proto_rawDescData = protoimpl.X.CompressGZIP(file_serial_proto_rawDescData)
	})
	return file_serial_proto_rawDescData
}

var file_serial_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_serial_proto_goTypes = []any{
	(*GetSmilesTokenCountRequest)(nil),    // 0: GetSmilesTokenCountRequest
	(*GetSmilesTokenCountResponse)(nil),   // 1: GetSmilesTokenCountResponse
	(*GetEntitiesTokenCountRequest)(nil),  // 2: GetEntitiesTokenCountRequest
	(*GetEntitiesTokenCountResponse)(nil), // 3: GetEntitiesTokenCountResponse
	(*Entity)(nil),                        // 4: Entity
}
var file_serial_proto_depIdxs = []int32{
	4, // 0: GetEntitiesTokenCountRequest.entities:type_name -> Entity
	0, // 1: SerialService.GetSmilesTokenCount:input_type -> GetSmilesTokenCountRequest
	2, // 2: SerialService.GetEntitiesTokenCount:input_type -> GetEntitiesTokenCountRequest
	1, // 3: SerialService.GetSmilesTokenCount:output_type -> GetSmilesTokenCountResponse
	3, // 4: SerialService.GetEntitiesTokenCount:output_type -> GetEntitiesTokenCountResponse
	3, // [3:5] is the sub-list for method output_type
	1, // [1:3] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_serial_proto_init() }
func file_serial_proto_init() {
	if File_serial_proto != nil {
		return
	}
	file_common_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_serial_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_serial_proto_goTypes,
		DependencyIndexes: file_serial_proto_depIdxs,
		MessageInfos:      file_serial_proto_msgTypes,
	}.Build()
	File_serial_proto = out.File
	file_serial_proto_rawDesc = nil
	file_serial_proto_goTypes = nil
	file_serial_proto_depIdxs = nil
}
